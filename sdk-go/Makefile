.PHONY: test build clean fmt vet lint examples

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
GOFMT=gofmt

# Build info
VERSION?=v1.0.0
BUILD_TIME=$(shell date +%Y-%m-%d\ %H:%M:%S)
GIT_COMMIT=$(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# Default target
all: fmt vet test build

# Format code
fmt:
	@echo "Formatting code..."
	@$(GOFMT) -s -w .

# Vet code
vet:
	@echo "Vetting code..."
	@$(GOCMD) vet ./...

# Run tests
test:
	@echo "Running tests..."
	@$(GOTEST) -v ./...

# Run tests with coverage
test-coverage:
	@echo "Running tests with coverage..."
	@$(GOTEST) -v -coverprofile=coverage.out ./...
	@$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Build examples
build:
	@echo "Building examples..."
	@$(GOBUILD) -o bin/basic_usage ./examples/basic_usage.go
	@$(GOBUILD) -o bin/instance_management ./examples/instance_management.go

# Run examples (requires valid token)
run-basic:
	@echo "Running basic usage example..."
	@$(GOCMD) run ./examples/basic_usage.go

run-instance:
	@echo "Running instance management example..."
	@$(GOCMD) run ./examples/instance_management.go

# Clean build artifacts
clean:
	@echo "Cleaning..."
	@$(GOCLEAN)
	@rm -rf bin/
	@rm -f coverage.out coverage.html

# Download dependencies
deps:
	@echo "Downloading dependencies..."
	@$(GOMOD) download
	@$(GOMOD) tidy

# Initialize module (if needed)
init:
	@echo "Initializing module..."
	@$(GOMOD) init github.com/chenyu-ai/sdk-go

# Check for security vulnerabilities
security:
	@echo "Checking for security vulnerabilities..."
	@$(GOCMD) list -json -m all | nancy sleuth

# Generate documentation
docs:
	@echo "Generating documentation..."
	@$(GOCMD) doc -all ./...

# Install tools
install-tools:
	@echo "Installing development tools..."
	@$(GOGET) -u golang.org/x/tools/cmd/goimports
	@$(GOGET) -u github.com/golangci/golangci-lint/cmd/golangci-lint

# Lint code
lint:
	@echo "Linting code..."
	@golangci-lint run

# Help
help:
	@echo "Available targets:"
	@echo "  all           - Format, vet, test, and build"
	@echo "  fmt           - Format code"
	@echo "  vet           - Vet code"
	@echo "  test          - Run tests"
	@echo "  test-coverage - Run tests with coverage"
	@echo "  build         - Build examples"
	@echo "  run-basic     - Run basic usage example"
	@echo "  run-instance  - Run instance management example"
	@echo "  clean         - Clean build artifacts"
	@echo "  deps          - Download dependencies"
	@echo "  init          - Initialize module"
	@echo "  security      - Check for security vulnerabilities"
	@echo "  docs          - Generate documentation"
	@echo "  install-tools - Install development tools"
	@echo "  lint          - Lint code"
	@echo "  help          - Show this help"

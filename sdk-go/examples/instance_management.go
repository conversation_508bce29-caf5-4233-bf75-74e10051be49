package main

import (
	"fmt"
	"log"
	"time"

	"github.com/chenyu-ai/sdk-go/client"
	"github.com/chenyu-ai/sdk-go/types"
)

func main() {
	// 创建客户端
	apiClient := client.NewClient("https://api.chenyu.cn/api/v1", "your-token-here")

	// 实例管理示例
	fmt.Println("=== 实例管理示例 ===")

	// 1. 查询可用镜像
	fmt.Println("1. 查询可用镜像...")
	podsResp, err := apiClient.GetPods(&types.PodListRequest{
		PaginationRequest: types.PaginationRequest{
			Page:     1,
			PageSize: 1,
		},
	})
	if err != nil {
		log.Fatalf("查询镜像失败: %v", err)
	}
	if len(podsResp.Items) == 0 {
		log.Fatal("没有可用的镜像")
	}
	selectedPod := podsResp.Items[0]
	fmt.Printf("选择镜像: %s (%s)\n", selectedPod.Title, selectedPod.Uuid)

	// 2. 查询GPU资源
	fmt.Println("\n2. 查询GPU资源...")
	gpuResp, err := apiClient.GetGpuModels(&types.GpuModelsRequest{
		PodUuid: selectedPod.Uuid,
	})
	if err != nil {
		log.Fatalf("查询GPU资源失败: %v", err)
	}
	if len(gpuResp.Items) == 0 {
		log.Fatal("没有可用的GPU资源")
	}
	selectedGpu := gpuResp.Items[0]
	fmt.Printf("选择GPU: %s (%s)\n", selectedGpu.Name, selectedGpu.Uuid)

	// 3. 创建实例
	fmt.Println("\n3. 创建实例...")
	createResp, err := apiClient.CreateInstance(&types.InstanceCreateRequest{
		PodUuid:      selectedPod.Uuid,
		GpuModelUuid: selectedGpu.Uuid,
		AutoStart:    1, // 自动启动
	})
	if err != nil {
		log.Fatalf("创建实例失败: %v", err)
	}
	instanceUuid := createResp.Instance.Uuid
	fmt.Printf("实例创建成功: %s (%s)\n", createResp.Instance.Title, instanceUuid)

	// 4. 等待实例启动
	fmt.Println("\n4. 等待实例启动...")
	for i := 0; i < 30; i++ { // 最多等待30次，每次5秒
		statusResp, err := apiClient.GetInstanceStatus(&types.InstanceStatusRequest{
			InstanceUuid: instanceUuid,
		})
		if err != nil {
			log.Printf("查询实例状态失败: %v", err)
			continue
		}
		
		fmt.Printf("实例状态: %s\n", statusResp.Instance.StatusTxt)
		
		// 如果实例已经运行，退出循环
		if statusResp.Instance.Status == 2 { // 假设2表示运行中
			fmt.Println("实例已启动!")
			if statusResp.Instance.StartupMaps != "" {
				fmt.Printf("访问信息: %s\n", statusResp.Instance.StartupMaps)
			}
			break
		}
		
		time.Sleep(5 * time.Second)
	}

	// 5. 设置定时关机（可选）
	fmt.Println("\n5. 设置定时关机...")
	err = apiClient.SetShutdownRegularTime(&types.InstanceShutdownRegularTimeRequest{
		InstanceUuid: instanceUuid,
		ShutdownTime: "23:59", // 晚上11:59自动关机
	})
	if err != nil {
		log.Printf("设置定时关机失败: %v", err)
	} else {
		fmt.Println("定时关机设置成功")
	}

	// 6. 演示实例操作
	fmt.Println("\n6. 演示实例操作...")
	
	// 停止实例
	fmt.Println("停止实例...")
	err = apiClient.StopInstance(&types.InstanceActionRequest{
		InstanceUuid: instanceUuid,
	})
	if err != nil {
		log.Printf("停止实例失败: %v", err)
	} else {
		fmt.Println("实例停止成功")
	}

	// 等待一段时间
	time.Sleep(10 * time.Second)

	// 重新启动实例
	fmt.Println("重新启动实例...")
	err = apiClient.StartInstance(&types.InstanceActionRequest{
		InstanceUuid: instanceUuid,
	})
	if err != nil {
		log.Printf("启动实例失败: %v", err)
	} else {
		fmt.Println("实例启动成功")
	}

	fmt.Printf("\n实例管理示例完成。实例UUID: %s\n", instanceUuid)
	fmt.Println("请在控制台中手动删除测试实例。")
}

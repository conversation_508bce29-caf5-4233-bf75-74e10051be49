package main

import (
	"fmt"
	"log"

	"github.com/chenyu-ai/sdk-go/client"
	"github.com/chenyu-ai/sdk-go/types"
)

func main() {
	// 创建客户端
	// 请替换为您的实际API地址和Token
	apiClient := client.NewClient("https://www.chenyu.cn/api/v1", "sk-kyhHuxOoAry36cgX1mZTWSWEAet16GsVtz5HNzODql")

	// 示例1: 查询余额
	fmt.Println("=== 查询余额 ===")
	balanceResp, err := apiClient.GetBalance(&types.BalanceRequest{})
	if err != nil {
		log.Printf("查询余额失败: %v", err)
	} else {
		fmt.Printf("账户余额: %s\n", balanceResp.Balance.String())
		fmt.Printf("算力卡数量: %d\n", len(balanceResp.Cards))
		for _, card := range balanceResp.Cards {
			fmt.Printf("卡号: %s, 剩余金额: %s, 状态: %s\n",
				card.CardNo, card.LeaveAmount.String(), card.StatusTxt)
		}
	}

	// 示例2: 查询镜像列表
	fmt.Println("\n=== 查询镜像列表 ===")
	podsResp, err := apiClient.GetPods(&types.PodListRequest{
		PaginationRequest: types.PaginationRequest{
			Page:     1,
			PageSize: 5,
		},
		Kw: "jupyter", // 搜索包含jupyter的镜像
	})
	if err != nil {
		log.Printf("查询镜像列表失败: %v", err)
	} else {
		fmt.Printf("镜像总数: %d\n", podsResp.Total)
		for _, pod := range podsResp.Items {
			fmt.Printf("镜像: %s (%s), 状态: %s\n",
				pod.Title, pod.Uuid, pod.StatusTxt)
		}
	}

	// 示例3: 查询GPU资源
	fmt.Println("\n=== 查询GPU资源 ===")
	gpuResp, err := apiClient.GetGpuModels(&types.GpuModelsRequest{})
	if err != nil {
		log.Printf("查询GPU资源失败: %v", err)
	} else {
		fmt.Printf("GPU资源总数: %d\n", gpuResp.Total)
		fmt.Printf("无卡价格: %s\n", gpuResp.NoCardPrice.String())
		for _, gpu := range gpuResp.Items {
			fmt.Printf("GPU: %s, 描述: %s, 价格: %s/小时, 库存: %s\n",
				gpu.Title, gpu.Desc, gpu.Price, gpu.FreeTxt)
		}
	}

	// 示例4: 查询实例列表
	fmt.Println("\n=== 查询实例列表 ===")
	instancesResp, err := apiClient.GetInstances(&types.InstanceListRequest{
		PaginationRequest: types.PaginationRequest{
			Page:     1,
			PageSize: 10,
		},
	})
	if err != nil {
		log.Printf("查询实例列表失败: %v", err)
	} else {
		fmt.Printf("实例总数: %d\n", instancesResp.Total)
		for _, instance := range instancesResp.Items {
			fmt.Printf("实例: %s (%s), 状态: %s\n",
				instance.Title, instance.Uuid, instance.StatusTxt)
		}
	}

	// 示例5: 查询账单
	fmt.Println("\n=== 查询账单 ===")
	billResp, err := apiClient.GetBill(&types.BillListRequest{
		PaginationRequest: types.PaginationRequest{
			Page:     1,
			PageSize: 5,
		},
	})
	if err != nil {
		log.Printf("查询账单失败: %v", err)
	} else {
		fmt.Printf("账单总数: %d\n", billResp.Total)
		for _, bill := range billResp.Items {
			fmt.Printf("订单: %s, 金额: %s, 描述: %s\n",
				bill.OrderNo, bill.OccurredAmount.String(), bill.Show)
		}
	}
}

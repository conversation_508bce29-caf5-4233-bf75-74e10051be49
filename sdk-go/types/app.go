package types

import "github.com/shopspring/decimal"

// PodListRequest 镜像列表查询请求
type PodListRequest struct {
	PaginationRequest
	PodId   uint   `json:"pod_id,omitempty"`
	PodUuid string `json:"pod_uuid,omitempty"`
	Kw      string `json:"kw,omitempty"` // 关键词搜索
}

// PodItem 镜像项
type PodItem struct {
	Uuid      string   `json:"uuid"`
	Title     string   `json:"title"`
	ImageTags string   `json:"image_tags"`
	ImageTag  string   `json:"image_tag"`
	PodName   string   `json:"pod_name"`
	SecretKey string   `json:"secret_key"`
	Status    int      `json:"status"`
	StatusTxt string   `json:"status_txt"`
	CreatedAt JsonTime `json:"created_at"`
}

// PodListResponse 镜像列表查询响应
type PodListResponse struct {
	Items []PodItem `json:"items"`
	PaginationResponse
}

// InstanceCreateRequest 创建实例请求
type InstanceCreateRequest struct {
	PodUuid      string `json:"pod_uuid"`
	ImageUuid    string `json:"image_uuid,omitempty"`
	ImageTag     string `json:"image_tag,omitempty"`
	GpuModelUuid string `json:"gpu_model_uuid"`
	AutoStart    int    `json:"auto_start,omitempty"` // 1-自动启动，0-不自动启动
}

// InstanceResponse 实例响应
type InstanceResponse struct {
	Instance InstanceItem `json:"instance"`
}

// InstanceItem 实例项
type InstanceItem struct {
	Uuid                string                   `json:"uuid"`
	Title               string                   `json:"title"`
	PodUuid             string                   `json:"pod_uuid"`
	PodTitle            string                   `json:"pod_title"`
	PodName             string                   `json:"pod_name"`
	PodPrice            string                   `json:"pod_price"`
	PodStartupElapse    int                      `json:"pod_startup_elapse"`
	PodCatalogue        []map[string]interface{} `json:"pod_catalogue"`
	ImageTag            string                   `json:"image_tag"`
	ImageTitle          string                   `json:"image_title"`
	OutWebUrl           string                   `json:"out_web_url"`
	Category            int                      `json:"category"`
	Gpus                int                      `json:"gpus"`
	NoCard              int                      `json:"no_card"`
	GpuModelName        string                   `json:"gpu_model_name"`
	GpuModel            map[string]interface{}   `json:"gpu_model"`
	ChargingType        int                      `json:"charging_type"`
	ChargingTypeName    string                   `json:"charging_type_name"`
	ChargingNum         int                      `json:"charging_num"`
	PriceH              string                   `json:"price_h"`
	InstanceTypeTxt     string                   `json:"instance_type_txt"`
	CurrentTime         JsonTime                 `json:"current_time"`
	StartTime           JsonTime                 `json:"start_time"`
	EndTime             JsonTime                 `json:"end_time"`
	StartupTime         JsonTime                 `json:"startup_time"`
	ShutdownTime        JsonTime                 `json:"shutdown_time"`
	ShutdownRegularTime JsonTime                 `json:"shutdown_regular_time"`
	Status              int                      `json:"status"`
	StatusTxt           string                   `json:"status_txt"`
	ImageStatus         int                      `json:"image_status"`
	ImageStatusTxt      string                   `json:"image_status_txt"`
	ImageStartupLogTime string                   `json:"image_startup_log_time"`
	SaveImageStatus     int                      `json:"save_image_status"`
	SaveImageStatusTxt  string                   `json:"save_image_status_txt"`
	SaveImageStatusTime JsonTime                 `json:"save_image_status_time"`
	StartupTxt          string                   `json:"startup_txt"`
	StartupLogTime      string                   `json:"startup_log_time"`
	StartupMaps         string                   `json:"startup_maps"`
	StartupMarkLog      []string                 `json:"startup_mark_log"`
	StartupInstLog      []string                 `json:"startup_inst_log"`
	StartupImageLog     []string                 `json:"startup_image_log"`
	TaskTxt             string                   `json:"task_txt,omitempty"`
	TaskLastTime        JsonTime                 `json:"task_last_time,omitempty"`
	TaskPercent         float64                  `json:"task_percent,omitempty"`
	ClassType           int                      `json:"class_type"`
	Components          map[string]interface{}   `json:"components,omitempty"`
}

// InstanceActionRequest 实例操作请求（启动、停止、重启）
type InstanceActionRequest struct {
	InstanceUuid string `json:"instance_uuid"`
}

// InstanceStatusRequest 实例状态查询请求
type InstanceStatusRequest struct {
	InstanceUuid string `json:"instance_uuid"`
}

// InstanceStatusResponse 实例状态查询响应
type InstanceStatusResponse struct {
	Instance InstanceStatusItem `json:"instance"`
}

// InstanceStatusItem 实例状态项
type InstanceStatusItem struct {
	Uuid        string `json:"uuid"`
	Title       string `json:"title"`
	ImageTag    string `json:"image_tag"`
	PodName     string `json:"pod_name"`
	StartupMaps string `json:"startup_maps"`
	Status      int    `json:"status"`
	StatusTxt   string `json:"status_txt"`
}

// InstanceShutdownRegularTimeRequest 设置定时关机请求
type InstanceShutdownRegularTimeRequest struct {
	InstanceUuid string `json:"instance_uuid"`
	ShutdownTime string `json:"shutdown_time"` // 格式：HH:MM
}

// InstanceListRequest 实例列表查询请求
type InstanceListRequest struct {
	PaginationRequest
	Status int `json:"status,omitempty"` // 实例状态过滤
}

// InstanceListResponse 实例列表查询响应
type InstanceListResponse struct {
	Items []InstanceItem `json:"items"`
	PaginationResponse
}

// GpuModelsRequest GPU资源查询请求
type GpuModelsRequest struct {
	PodUuid string `json:"pod_uuid,omitempty"`
}

// GpuModelItem GPU资源项
type GpuModelItem struct {
	Uuid      string   `json:"uuid"`
	Title     string   `json:"title"`
	Desc      string   `json:"desc"`
	Price     string   `json:"price"`
	Remark    string   `json:"remark"`
	Status    int      `json:"status"`
	StatusTxt string   `json:"status_txt"`
	CreatedAt JsonTime `json:"created_at"`
	FreeTxt   string   `json:"free_txt"`
}

// GpuModelsResponse GPU资源查询响应
type GpuModelsResponse struct {
	Items       []GpuModelItem  `json:"items"`
	NoCardPrice decimal.Decimal `json:"no_card_price"`
	Total       int             `json:"total"`
}

package types

import "github.com/shopspring/decimal"

// PodListRequest 镜像列表查询请求
type PodListRequest struct {
	PaginationRequest
	PodId    uint   `json:"pod_id,omitempty"`
	PodUuid  string `json:"pod_uuid,omitempty"`
	Kw       string `json:"kw,omitempty"` // 关键词搜索
}

// PodItem 镜像项
type PodItem struct {
	Uuid      string   `json:"uuid"`
	Title     string   `json:"title"`
	ImageTags string   `json:"image_tags"`
	ImageTag  string   `json:"image_tag"`
	PodName   string   `json:"pod_name"`
	SecretKey string   `json:"secret_key"`
	Status    int      `json:"status"`
	StatusTxt string   `json:"status_txt"`
	CreatedAt JsonTime `json:"created_at"`
}

// PodListResponse 镜像列表查询响应
type PodListResponse struct {
	Items []PodItem `json:"items"`
	PaginationResponse
}

// InstanceCreateRequest 创建实例请求
type InstanceCreateRequest struct {
	PodUuid      string `json:"pod_uuid"`
	ImageUuid    string `json:"image_uuid,omitempty"`
	ImageTag     string `json:"image_tag,omitempty"`
	GpuModelUuid string `json:"gpu_model_uuid"`
	AutoStart    int    `json:"auto_start,omitempty"` // 1-自动启动，0-不自动启动
}

// InstanceResponse 实例响应
type InstanceResponse struct {
	Instance InstanceItem `json:"instance"`
}

// InstanceItem 实例项
type InstanceItem struct {
	ID               uint            `json:"id"`
	Uuid             string          `json:"uuid"`
	Title            string          `json:"title"`
	PodUuid          string          `json:"pod_uuid"`
	PodTitle         string          `json:"pod_title"`
	PodName          string          `json:"pod_name"`
	ImageTag         string          `json:"image_tag"`
	GpuModelUuid     string          `json:"gpu_model_uuid"`
	Status           int             `json:"status"`
	StatusTxt        string          `json:"status_txt"`
	StartupMaps      string          `json:"startup_maps"`
	CreatedAt        JsonTime        `json:"created_at"`
}

// InstanceActionRequest 实例操作请求（启动、停止、重启）
type InstanceActionRequest struct {
	InstanceUuid string `json:"instance_uuid"`
}

// InstanceStatusRequest 实例状态查询请求
type InstanceStatusRequest struct {
	InstanceUuid string `json:"instance_uuid"`
}

// InstanceStatusResponse 实例状态查询响应
type InstanceStatusResponse struct {
	Instance InstanceStatusItem `json:"instance"`
}

// InstanceStatusItem 实例状态项
type InstanceStatusItem struct {
	Uuid        string `json:"uuid"`
	Status      int    `json:"status"`
	StatusTxt   string `json:"status_txt"`
	StartupMaps string `json:"startup_maps"`
}

// InstanceShutdownRegularTimeRequest 设置定时关机请求
type InstanceShutdownRegularTimeRequest struct {
	InstanceUuid string `json:"instance_uuid"`
	ShutdownTime string `json:"shutdown_time"` // 格式：HH:MM
}

// InstanceListRequest 实例列表查询请求
type InstanceListRequest struct {
	PaginationRequest
	Status int `json:"status,omitempty"` // 实例状态过滤
}

// InstanceListResponse 实例列表查询响应
type InstanceListResponse struct {
	Items []InstanceItem `json:"items"`
	PaginationResponse
}

// GpuModelsRequest GPU资源查询请求
type GpuModelsRequest struct {
	PodUuid string `json:"pod_uuid,omitempty"`
}

// GpuModelItem GPU资源项
type GpuModelItem struct {
	ID           uint            `json:"id"`
	Uuid         string          `json:"uuid"`
	Name         string          `json:"name"`
	Memory       int             `json:"memory"`
	Price        decimal.Decimal `json:"price"`
	Available    int             `json:"available"`
	Total        int             `json:"total"`
	Status       int             `json:"status"`
	StatusTxt    string          `json:"status_txt"`
}

// GpuModelsResponse GPU资源查询响应
type GpuModelsResponse struct {
	Items []GpuModelItem `json:"items"`
	PaginationResponse
}

package types

import "github.com/shopspring/decimal"

// BalanceRequest 余额查询请求
type BalanceRequest struct {
	// 目前API不需要额外参数
}

// CardResponse 算力卡响应
type CardResponse struct {
	Uuid        string                   `json:"uuid"`
	Title       string                   `json:"title"`
	CardNo      string                   `json:"card_no"`
	ValidDays   int                      `json:"valid_days"`
	BindTime    JsonTime                 `json:"bind_time"`
	ExpireDate  JsonTime                 `json:"expire_date"`
	SalePrice   decimal.Decimal          `json:"sale_price"`
	FacePrice   decimal.Decimal          `json:"face_price"`
	LeaveAmount decimal.Decimal          `json:"leave_amount"`
	PodsObj     []map[string]interface{} `json:"pods"`
	Remark      string                   `json:"remark"`
	Status      int                      `json:"status"`
	StatusTxt   string                   `json:"status_txt"`
}

// BalanceResponse 余额查询响应
type BalanceResponse struct {
	Cards   []CardResponse  `json:"cards"`
	Balance decimal.Decimal `json:"balance"`
}

// RechargeListRequest 充值记录查询请求
type RechargeListRequest struct {
	PaginationRequest
	KW string `json:"kw"` // 关键词搜索
}

// RechargeItem 充值记录项
type RechargeItem struct {
	CreatedAt  JsonTime        `json:"created_at"`
	PayTime    JsonTime        `json:"pay_time"`
	OutTradeNo string          `json:"out_trade_no"`
	Gateway    string          `json:"gateway"`
	PayChannel string          `json:"pay_channel"`
	Amount     decimal.Decimal `json:"amount"`
}

// RechargeListResponse 充值记录查询响应
type RechargeListResponse struct {
	Items []RechargeItem `json:"items"`
	PaginationResponse
}

// BillListRequest 账单查询请求
type BillListRequest struct {
	PaginationRequest
	KW string `json:"kw"` // 关键词搜索
}

// BillItem 账单项
type BillItem struct {
	OrderNo        string          `json:"order_no"`
	OccurredAmount decimal.Decimal `json:"occurred_amount"`
	CardTxt        string          `json:"card_txt"`
	Show           string          `json:"show"`
	CreatedAt      JsonTime        `json:"created_at"`
}

// BillListResponse 账单查询响应
type BillListResponse struct {
	Items []BillItem `json:"items"`
	PaginationResponse
}

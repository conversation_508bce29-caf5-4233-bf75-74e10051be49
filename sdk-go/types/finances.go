package types

import "github.com/shopspring/decimal"

// BalanceRequest 余额查询请求
type BalanceRequest struct {
	// 目前API不需要额外参数
}

// CardResponse 算力卡响应
type CardResponse struct {
	ID          uint            `json:"id"`
	Uuid        string          `json:"uuid"`
	CardNo      string          `json:"card_no"`
	Amount      decimal.Decimal `json:"amount"`
	UsedAmount  decimal.Decimal `json:"used_amount"`
	Balance     decimal.Decimal `json:"balance"`
	Status      int             `json:"status"`
	StatusTxt   string          `json:"status_txt"`
	CreatedAt   JsonTime        `json:"created_at"`
	ExpiredAt   JsonTime        `json:"expired_at"`
}

// BalanceResponse 余额查询响应
type BalanceResponse struct {
	Items []CardResponse `json:"items"`
	Total int            `json:"total"`
}

// RechargeListRequest 充值记录查询请求
type RechargeListRequest struct {
	PaginationRequest
	KW string `json:"kw"` // 关键词搜索
}

// RechargeItem 充值记录项
type RechargeItem struct {
	ID            uint            `json:"id"`
	OrderNo       string          `json:"order_no"`
	Amount        decimal.Decimal `json:"amount"`
	PayAmount     decimal.Decimal `json:"pay_amount"`
	PayMethod     string          `json:"pay_method"`
	PayStatus     int             `json:"pay_status"`
	PayStatusTxt  string          `json:"pay_status_txt"`
	CreatedAt     JsonTime        `json:"created_at"`
}

// RechargeListResponse 充值记录查询响应
type RechargeListResponse struct {
	Items []RechargeItem `json:"items"`
	PaginationResponse
}

// BillListRequest 账单查询请求
type BillListRequest struct {
	PaginationRequest
	KW string `json:"kw"` // 关键词搜索
}

// BillItem 账单项
type BillItem struct {
	ID             uint            `json:"id"`
	OrderNo        string          `json:"order_no"`
	OccurredAmount decimal.Decimal `json:"occurred_amount"`
	CardId         int64           `json:"card_id"`
	CardTxt        string          `json:"card_txt"`
	Show           string          `json:"show"`
	CreatedAt      JsonTime        `json:"created_at"`
}

// BillListResponse 账单查询响应
type BillListResponse struct {
	Items []BillItem `json:"items"`
	PaginationResponse
}

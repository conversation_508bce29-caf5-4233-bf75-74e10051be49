package types

import "time"

// APIResponse 通用API响应结构
type APIResponse struct {
	Code   int         `json:"code"`
	Msg    string      `json:"msg"`
	Result interface{} `json:"result"`
}

// PaginationRequest 分页请求参数
type PaginationRequest struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
}

// PaginationResponse 分页响应结构
type PaginationResponse struct {
	Total int `json:"total"`
}

// JsonTime 自定义时间类型
type JsonTime struct {
	time.Time
}

// MarshalJSON 实现JSON序列化
func (jt JsonTime) MarshalJSON() ([]byte, error) {
	formatted := jt.Format("2006-01-02 15:04:05")
	return []byte(`"` + formatted + `"`), nil
}

// UnmarshalJSON 实现JSON反序列化
func (jt *JsonTime) UnmarshalJSON(data []byte) error {
	str := string(data)
	str = str[1 : len(str)-1] // 去掉引号
	
	t, err := time.Parse("2006-01-02 15:04:05", str)
	if err != nil {
		return err
	}
	
	jt.Time = t
	return nil
}

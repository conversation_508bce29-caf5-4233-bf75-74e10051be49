package client

import (
	"encoding/json"
	"fmt"

	"github.com/chenyu-ai/sdk-go/types"
)

// GetBalance 查询余额信息
func (c *Client) GetBalance(req *types.BalanceRequest) (*types.BalanceResponse, error) {
	var apiResp struct {
		types.APIResponse
		Result types.BalanceResponse `json:"result"`
	}

	if err := c.post("/finances/balance", req, &apiResp); err != nil {
		return nil, fmt.Errorf("get balance: %w", err)
	}

	if err := checkAPIResponse(&apiResp.APIResponse); err != nil {
		return nil, err
	}

	return &apiResp.Result, nil
}

// GetRecharge 查询充值记录
func (c *Client) GetRecharge(req *types.RechargeListRequest) (*types.RechargeListResponse, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	var apiResp struct {
		types.APIResponse
		Result types.RechargeListResponse `json:"result"`
	}

	if err := c.post("/finances/recharge", req, &apiResp); err != nil {
		return nil, fmt.Errorf("get recharge: %w", err)
	}

	if err := checkAPIResponse(&apiResp.APIResponse); err != nil {
		return nil, err
	}

	return &apiResp.Result, nil
}

// GetBill 查询账单记录
func (c *Client) GetBill(req *types.BillListRequest) (*types.BillListResponse, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 50 {
		req.PageSize = 50
	}

	var apiResp struct {
		types.APIResponse
		Result types.BillListResponse `json:"result"`
	}

	if err := c.post("/finances/bill", req, &apiResp); err != nil {
		return nil, fmt.Errorf("get bill: %w", err)
	}

	if err := checkAPIResponse(&apiResp.APIResponse); err != nil {
		return nil, err
	}

	return &apiResp.Result, nil
}

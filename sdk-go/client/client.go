package client

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/chenyu-ai/sdk-go/types"
)

// Client 晨羽智云API客户端
type Client struct {
	BaseURL    string
	Token      string
	HTTPClient *http.Client
}

// NewClient 创建新的API客户端
func NewClient(baseURL, token string) *Client {
	return &Client{
		BaseURL: baseURL,
		Token:   token,
		HTTPClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// SetTimeout 设置HTTP请求超时时间
func (c *Client) SetTimeout(timeout time.Duration) {
	c.HTTPClient.Timeout = timeout
}

// post 发送POST请求的通用方法
func (c *Client) post(endpoint string, req interface{}, resp interface{}) error {
	url := fmt.Sprintf("%s%s", c.BaseURL, endpoint)
	
	// 序列化请求体
	var body []byte
	var err error
	if req != nil {
		body, err = json.Marshal(req)
		if err != nil {
			return fmt.Errorf("marshal request: %w", err)
		}
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(body))
	if err != nil {
		return fmt.Errorf("create request: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	if c.Token != "" {
		httpReq.Header.Set("Authorization", "Bearer "+c.Token)
	}

	// 发送请求
	httpResp, err := c.HTTPClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("send request: %w", err)
	}
	defer httpResp.Body.Close()

	// 读取响应体
	respBody, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return fmt.Errorf("read response: %w", err)
	}

	// 检查HTTP状态码
	if httpResp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP %d: %s", httpResp.StatusCode, string(respBody))
	}

	// 解析响应
	if resp != nil {
		if err := json.Unmarshal(respBody, resp); err != nil {
			return fmt.Errorf("unmarshal response: %w", err)
		}
	}

	return nil
}

// checkAPIResponse 检查API响应状态
func checkAPIResponse(resp *types.APIResponse) error {
	if resp.Code != 0 {
		return fmt.Errorf("API error (code %d): %s", resp.Code, resp.Msg)
	}
	return nil
}

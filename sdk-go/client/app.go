package client

import (
	"fmt"

	"github.com/chenyu-ai/sdk-go/types"
)

// GetPods 查询可用镜像列表
func (c *Client) GetPods(req *types.PodListRequest) (*types.PodListResponse, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 50 {
		req.PageSize = 10
	}

	var apiResp struct {
		types.APIResponse
		Result types.PodListResponse `json:"result"`
	}

	if err := c.post("/app/list", req, &apiResp); err != nil {
		return nil, fmt.Errorf("get pods: %w", err)
	}

	if err := checkAPIResponse(&apiResp.APIResponse); err != nil {
		return nil, err
	}

	return &apiResp.Result, nil
}

// CreateInstance 创建实例
func (c *Client) CreateInstance(req *types.InstanceCreateRequest) (*types.InstanceResponse, error) {
	if req.PodUuid == "" {
		return nil, fmt.Errorf("pod_uuid is required")
	}
	if req.GpuModelUuid == "" {
		return nil, fmt.Errorf("gpu_model_uuid is required")
	}

	var apiResp struct {
		types.APIResponse
		Result types.InstanceResponse `json:"result"`
	}

	if err := c.post("/app/instance/create", req, &apiResp); err != nil {
		return nil, fmt.Errorf("create instance: %w", err)
	}

	if err := checkAPIResponse(&apiResp.APIResponse); err != nil {
		return nil, err
	}

	return &apiResp.Result, nil
}

// StopInstance 停止实例
func (c *Client) StopInstance(req *types.InstanceActionRequest) error {
	if req.InstanceUuid == "" {
		return fmt.Errorf("instance_uuid is required")
	}

	var apiResp types.APIResponse

	if err := c.post("/app/instance/stop", req, &apiResp); err != nil {
		return fmt.Errorf("stop instance: %w", err)
	}

	return checkAPIResponse(&apiResp)
}

// StartInstance 启动实例
func (c *Client) StartInstance(req *types.InstanceActionRequest) error {
	if req.InstanceUuid == "" {
		return fmt.Errorf("instance_uuid is required")
	}

	var apiResp types.APIResponse

	if err := c.post("/app/instance/start", req, &apiResp); err != nil {
		return fmt.Errorf("start instance: %w", err)
	}

	return checkAPIResponse(&apiResp)
}

// RestartInstance 重启实例
func (c *Client) RestartInstance(req *types.InstanceActionRequest) error {
	if req.InstanceUuid == "" {
		return fmt.Errorf("instance_uuid is required")
	}

	var apiResp types.APIResponse

	if err := c.post("/app/instance/restart", req, &apiResp); err != nil {
		return fmt.Errorf("restart instance: %w", err)
	}

	return checkAPIResponse(&apiResp)
}

// GetInstanceStatus 查询实例状态
func (c *Client) GetInstanceStatus(req *types.InstanceStatusRequest) (*types.InstanceStatusResponse, error) {
	if req.InstanceUuid == "" {
		return nil, fmt.Errorf("instance_uuid is required")
	}

	var apiResp struct {
		types.APIResponse
		Result types.InstanceStatusResponse `json:"result"`
	}

	if err := c.post("/app/instance/status", req, &apiResp); err != nil {
		return nil, fmt.Errorf("get instance status: %w", err)
	}

	if err := checkAPIResponse(&apiResp.APIResponse); err != nil {
		return nil, err
	}

	return &apiResp.Result, nil
}

// SetShutdownRegularTime 设置定时关机
func (c *Client) SetShutdownRegularTime(req *types.InstanceShutdownRegularTimeRequest) error {
	if req.InstanceUuid == "" {
		return fmt.Errorf("instance_uuid is required")
	}
	if req.ShutdownTime == "" {
		return fmt.Errorf("shutdown_time is required")
	}

	var apiResp types.APIResponse

	if err := c.post("/app/instance/scheduled/shutdown", req, &apiResp); err != nil {
		return fmt.Errorf("set shutdown regular time: %w", err)
	}

	return checkAPIResponse(&apiResp)
}

// GetInstances 查询实例列表
func (c *Client) GetInstances(req *types.InstanceListRequest) (*types.InstanceListResponse, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 50 {
		req.PageSize = 10
	}

	var apiResp struct {
		types.APIResponse
		Result types.InstanceListResponse `json:"result"`
	}

	if err := c.post("/app/instance/list", req, &apiResp); err != nil {
		return nil, fmt.Errorf("get instances: %w", err)
	}

	if err := checkAPIResponse(&apiResp.APIResponse); err != nil {
		return nil, err
	}

	return &apiResp.Result, nil
}

// GetGpuModels 查询GPU资源
func (c *Client) GetGpuModels(req *types.GpuModelsRequest) (*types.GpuModelsResponse, error) {
	var apiResp struct {
		types.APIResponse
		Result types.GpuModelsResponse `json:"result"`
	}

	if err := c.post("/gpu/models", req, &apiResp); err != nil {
		return nil, fmt.Errorf("get gpu models: %w", err)
	}

	if err := checkAPIResponse(&apiResp.APIResponse); err != nil {
		return nil, err
	}

	return &apiResp.Result, nil
}

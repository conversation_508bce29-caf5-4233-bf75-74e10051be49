# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [1.0.0] - 2025-06-05

### Added
- Initial release of 晨羽智云 Go SDK
- Complete API client implementation for 晨羽智云 AI Lab
- Support for all major API endpoints:
  - Financial APIs (balance, recharge, billing)
  - Application management (pods, instances)
  - GPU resource management
- Comprehensive type definitions
- Authentication support with Bearer tokens
- Error handling and validation
- Pagination support
- Complete documentation and examples
- Unit tests with coverage
- CI/CD pipeline with GitHub Actions
- Code quality tools (golangci-lint, gofmt, govet)

### Features
- **Client Management**
  - HTTP client with configurable timeout
  - Automatic JSON serialization/deserialization
  - Bearer token authentication
  - Comprehensive error handling

- **Financial APIs**
  - `GetBalance()` - Query account balance and compute cards
  - `GetRecharge()` - Query recharge records with pagination
  - `GetBill()` - Query billing records with search

- **Application Management**
  - `GetPods()` - List available AI images/containers
  - `CreateInstance()` - Create new AI instances
  - `GetInstances()` - List user instances with filtering
  - `GetInstanceStatus()` - Query instance status and access info

- **Instance Operations**
  - `StartInstance()` - Start stopped instances
  - `StopInstance()` - Stop running instances
  - `RestartInstance()` - Restart instances
  - `SetShutdownRegularTime()` - Schedule automatic shutdown

- **Resource Management**
  - `GetGpuModels()` - Query available GPU resources

- **Type Safety**
  - Complete type definitions for all API requests/responses
  - Decimal support for financial calculations
  - Custom JSON time handling
  - Pagination helpers

### Documentation
- Comprehensive README with quick start guide
- Complete API reference documentation
- Working examples for common use cases
- Code comments and documentation

### Development
- Unit tests with mocked HTTP responses
- CI/CD pipeline for automated testing
- Code quality checks and linting
- Makefile for common development tasks
- GitHub Actions for continuous integration

### Examples
- `basic_usage.go` - Demonstrates basic API usage
- `instance_management.go` - Complete instance lifecycle management
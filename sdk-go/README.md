# 晨羽智云 Go SDK

晨羽智云AI实验室API的官方Go SDK，提供简单易用的接口来管理您的AI实例、查询资源和财务信息。

## 功能特性

- 🚀 **实例管理**: 创建、启动、停止、重启AI实例
- 💰 **财务查询**: 查询余额、充值记录、账单信息
- 🖥️ **资源管理**: 查询可用镜像和GPU资源
- 🔒 **安全认证**: 支持Token认证
- 📝 **完整类型**: 提供完整的类型定义和文档
- 🛠️ **错误处理**: 详细的错误信息和处理

## 安装

```bash
go get github.com/chenyu-ai/sdk-go
```

## 快速开始

### 1. 创建客户端

```go
package main

import (
    "github.com/chenyu-ai/sdk-go/client"
    "github.com/chenyu-ai/sdk-go/types"
)

func main() {
    // 创建API客户端
    apiClient := client.NewClient("https://api.chenyu.cn/api/v1", "your-token-here")
    
    // 设置超时时间（可选）
    apiClient.SetTimeout(60 * time.Second)
}
```

### 2. 查询余额

```go
// 查询账户余额和算力卡信息
balanceResp, err := apiClient.GetBalance(&types.BalanceRequest{})
if err != nil {
    log.Fatal(err)
}

fmt.Printf("算力卡数量: %d\n", balanceResp.Total)
for _, card := range balanceResp.Items {
    fmt.Printf("卡号: %s, 余额: %s\n", card.CardNo, card.Balance.String())
}
```

### 3. 查询可用镜像

```go
// 查询可用的AI镜像
podsResp, err := apiClient.GetPods(&types.PodListRequest{
    PaginationRequest: types.PaginationRequest{
        Page:     1,
        PageSize: 10,
    },
    Kw: "jupyter", // 搜索关键词
})
if err != nil {
    log.Fatal(err)
}

for _, pod := range podsResp.Items {
    fmt.Printf("镜像: %s (%s)\n", pod.Title, pod.Uuid)
}
```

### 4. 创建和管理实例

```go
// 创建实例
createResp, err := apiClient.CreateInstance(&types.InstanceCreateRequest{
    PodUuid:      "pod-uuid-here",
    GpuModelUuid: "gpu-model-uuid-here",
    AutoStart:    1, // 自动启动
})
if err != nil {
    log.Fatal(err)
}

instanceUuid := createResp.Instance.Uuid
fmt.Printf("实例创建成功: %s\n", instanceUuid)

// 查询实例状态
statusResp, err := apiClient.GetInstanceStatus(&types.InstanceStatusRequest{
    InstanceUuid: instanceUuid,
})
if err != nil {
    log.Fatal(err)
}

fmt.Printf("实例状态: %s\n", statusResp.Instance.StatusTxt)

// 停止实例
err = apiClient.StopInstance(&types.InstanceActionRequest{
    InstanceUuid: instanceUuid,
})
if err != nil {
    log.Fatal(err)
}
```

## API 参考

### 财务相关

- `GetBalance()` - 查询余额和算力卡信息
- `GetRecharge()` - 查询充值记录
- `GetBill()` - 查询账单记录

### 应用管理

- `GetPods()` - 查询可用镜像列表
- `CreateInstance()` - 创建实例
- `GetInstances()` - 查询实例列表
- `GetInstanceStatus()` - 查询实例状态

### 实例操作

- `StartInstance()` - 启动实例
- `StopInstance()` - 停止实例
- `RestartInstance()` - 重启实例
- `SetShutdownRegularTime()` - 设置定时关机

### 资源查询

- `GetGpuModels()` - 查询GPU资源

## 示例代码

查看 `examples/` 目录下的完整示例：

- `basic_usage.go` - 基本使用示例
- `instance_management.go` - 实例管理示例

## 错误处理

SDK提供详细的错误信息：

```go
resp, err := apiClient.GetBalance(&types.BalanceRequest{})
if err != nil {
    // 处理网络错误、序列化错误等
    log.Printf("请求失败: %v", err)
    return
}

// API返回的错误会自动检查，无需手动处理
```

## 认证

使用您的API Token进行认证：

1. 登录晨羽智云控制台
2. 在API管理页面生成Token
3. 在创建客户端时传入Token

```go
client := client.NewClient("https://api.chenyu.cn/api/v1", "your-token-here")
```

## 贡献

欢迎提交Issue和Pull Request来改进这个SDK。

## 许可证

MIT License

## 支持

如有问题，请联系技术支持或查看[官方文档](https://docs.chenyu.cn)。

import redis
import time
class MyRedis:
    def __init__(self, host='localhost', port=6379, db=0, password=None, decode_responses=True):
        """
        初始化 Redis 连接。

        Args:
            host (str): Redis 服务器主机名或 IP 地址。默认为 'localhost'。
            port (int): Redis 服务器端口号。默认为 6379。
            db (int): 要连接的 Redis 数据库编号。默认为 0。
            password (str, optional): Redis 服务器密码（如果需要）。默认为 None。
            decode_responses (bool): 是否自动解码 Redis 返回的字节串为字符串。默认为 True。
        """
        self.host = host
        self.port = port
        self.db = db
        self.password = password
        self.decode_responses = decode_responses
        self.retry_interval = 5
        self._redis = self._connect()

    def _connect(self):
        """
        建立 Redis 连接。
        """
        try:
            r = redis.Redis(
                host=self.host,
                port=self.port,
                db=self.db,
                password=self.password,
                decode_responses=self.decode_responses
            )
            r.ping()  # 检查连接是否成功
            return r
        except redis.exceptions.ConnectionError as e:
            print(f"连接 Redis 失败: {e}")
            return None
    def reconnect(self):
        """
        尝试重新连接 Redis。
        """
        print(f"尝试重新连接 Redis (每 {self.retry_interval} 秒重试一次)...")
        while True:
            self._redis = self._connect()
            if self._redis:
                print("重新连接 Redis 成功！")
                return self._redis.ping()
            else:
                print("重新连接 Redis 失败！")

    def ping(self):
        """
        检查 Redis 连接是否仍然有效。
        """
        if self._redis:
            try:
                return self._redis.ping()
            except redis.exceptions.ConnectionError as e:
                print(f"Redis 连接错误: {e}")
                self._redis = self._connect()  # 尝试重新连接
                return self._redis.ping() if self._redis else False
        return False

    # --- 字符串操作 ---
    def set(self, key, value, ex=None, px=None, nx=False, xx=False):
        """
        设置字符串键的值。

        Args:
            key (str): 键名。
            value (any): 键值。
            ex (int, optional): 过期时间（秒）。默认为 None。
            px (int, optional): 过期时间（毫秒）。默认为 None。
            nx (bool): 如果键不存在则设置。默认为 False。
            xx (bool): 如果键已存在则设置。默认为 False。
        """
        if self._redis:
            return self._redis.set(key, value, ex=ex, px=px, nx=nx, xx=xx)
        return False

    def get(self, key):
        """
        获取字符串键的值。

        Args:
            key (str): 键名。

        Returns:
            str or None: 键值，如果键不存在则返回 None。
        """
        if self._redis:
            return self._redis.get(key)
        return None

    def delete(self, *keys):
        """
        删除一个或多个键。

        Args:
            *keys (str): 要删除的键名。

        Returns:
            int: 成功删除的键的数量。
        """
        if self._redis:
            return self._redis.delete(*keys)
        return 0

    def zrem(self, *keys):

        if self._redis:
            return self._redis.zrem(*keys)
        return 0

    # --- 列表操作 ---
    def lpush(self, key, *values):
        """
        将一个或多个值推入列表的左端。

        Args:
            key (str): 列表的键名。
            *values (any): 要推入的值。

        Returns:
            int: 推入后列表的长度。
        """
        if self._redis:
            return self._redis.lpush(key, *values)
        return 0

    def rpop(self, key):
        """
        移除并返回列表的右端的一个元素。

        Args:
            key (str): 列表的键名。

        Returns:
            str or None: 移除的元素，如果列表为空则返回 None。
        """
        if self._redis:
            result = self._redis.rpop(key)
            if result:
                # result 是一个json字符串
                return result.decode(self._redis.connection_pool.connection_kwargs.get('decode_responses', 'utf-8')) if isinstance(result, bytes) and not self._redis.connection_pool.connection_kwargs.get('decode_responses') else result
            else:
                # 超时返回 None
                return None
        return None

    def brpop(self, key, timeout=0):
        """
        阻塞式地移除并返回列表的右端的一个元素。

        Args:
            key (str): 列表的键名。
            timeout (int): 阻塞等待的秒数。如果为 0，则无限期阻塞。

        Returns:
            tuple or None: 一个包含 (键名, 元素值) 的元组，如果超时则返回 None。
        """
        if self._redis:
            result = self._redis.brpop(key, timeout=timeout)
            if result:
                # result 是一个元组 (key, value)
                popped_key, popped_value = result
                return popped_value.decode(self._redis.connection_pool.connection_kwargs.get('decode_responses', 'utf-8')) if isinstance(popped_value, bytes) and not self._redis.connection_pool.connection_kwargs.get('decode_responses') else popped_value
            else:
                # 超时返回 None
                return None

        return None

    # --- 哈希操作 ---
    def hset(self, key, field, value):
        """
        设置哈希字段的值，将 Python 对象序列化为 JSON 字符串存储。

        Args:
            key (str): 哈希的键名。
            field (str): 字段名。
            json_value (any): 要存储的 Python 对象 (将使用 json.dumps 序列化)。
        """
        if self._redis:
            try:
                return self._redis.hset(key, field, value)
            except TypeError as e:
                print(f"JSON 序列化错误: {e}")
                return False
        return False

    def hget(self, key, field):
        """
        获取哈希字段的值。

        Args:
            key (str): 哈希的键名。
            field (str): 字段名。

        Returns:
            str or None: 字段值，如果字段不存在则返回 None。
        """
        if self._redis:
            return self._redis.hget(key, field)
        return None

    def hgetall(self, key):
        """
        获取哈希中所有的字段和值。

        Args:
            key (str): 哈希的键名。

        Returns:
            dict: 包含所有字段和值的字典。
        """
        if self._redis:
            return self._redis.hgetall(key)
        return {}

    # --- 更多操作可以根据需要添加 ---

    def close(self):
        """
        关闭 Redis 连接。
        """
        if self._redis:
            self._redis.close()
            self._redis = None

    def __del__(self):
        """
        对象销毁时关闭 Redis 连接。
        """
        self.close()

# 示例用法 (在其他 Python 文件中):

RedisQueueListInputPrefix = "redisqueue:list:input:"
RedisQueueListOutputPrefix = "redisqueue:list:output:"
RedisQueueZsetPrefix = "redisqueue:zset:"
RedisQueueHsetPrefix = "redisqueue:hset:"

queueName = "Florence2"
listInputKey = RedisQueueListInputPrefix + "tagger:" + queueName
listOutputKey = RedisQueueListOutputPrefix + "tagger:" + queueName
zsetKey = RedisQueueZsetPrefix + "tagger:"+ queueName
hsetKey = RedisQueueHsetPrefix + "tagger:" + queueName
import json
if __name__ == "__main__":
    redis_client = MyRedis(host='*************', port=6379, db=0, password="Dm2mrAHN")

    taskJson = redis_client.brpop(listInputKey, timeout=1)
    print(taskJson)
    if taskJson == None:
        print("None")
        exit()

    task = json.loads(taskJson)
    print(task)
    qTaskId = task["q_task_id"]
    print(task["q_task_id"])
    print(task["q_task_input"])
    print(task["q_task_input"]["tags_uuid"])
    print(task["q_task_output"])

    redis_client.zrem(zsetKey, task["q_task_id"])

    timestamp_milliseconds = int(round(time.time() * 1000))

    task["q_task_progress"] = 0.09
    task["q_task_updated_at"] = int(round(time.time() * 1000))


    taskJson = json.dumps(task)
    print(json.dumps(task))
    redis_client.hset(hsetKey, qTaskId,taskJson)

    task["q_task_progress"] = 1
    task["q_task_updated_at"] = int(round(time.time() * 1000))
    task["q_task_completed_at"] = int(round(time.time() * 1000))
    task["q_task_output"] = {}

    taskJson = json.dumps(task)
    print(json.dumps(task))
    redis_client.lpush(listOutputKey,taskJson)

    # 字符串操作
    redis_client.set('mykey', 'myvalue', ex=60)
    value = redis_client.get('mykey')
    print(f"Get 'mykey': {value}")
    redis_client.delete('mykey')

    # 列表操作
    redis_client.lpush('mylist', 'item1', 'item2')
    item = redis_client.rpop('mylist')
    print(f"Popped from 'mylist': {item}")

    # 哈希操作
    redis_client.hset('myhash', {'field1': 'value1', 'field2': 'value2'})
    field1_value = redis_client.hget('myhash', 'field1')
    all_hash = redis_client.hgetall('myhash')
    print(f"Get 'myhash' field1: {field1_value}")
    print(f"Get all 'myhash': {all_hash}")

    redis_client.close()
from PIL import Image
import smartcrop


def center_crop(image_path, target_width, target_height):
    """
    对图片进行中心裁剪，裁剪出指定尺寸的区域。
    不进行缩放。
    """
    image = Image.open(image_path)
    img_width, img_height = image.size

    if target_width > img_width or target_height > img_height:
        print(f"Warning: Target size {target_width}x{target_height} is larger than image {img_width}x{img_height}.")
        print("Cannot center crop without resizing or padding.")
        # 在这种情况下，如果你仍想得到300x300，就必须缩放或填充。
        # 这里为了演示“不缩放”，我们选择返回原始图片或者抛出错误。
        # return image.resize((target_width, target_height), Image.LANCZOS) # 如果接受缩放
        return None # 或者抛出异常

    # 计算裁剪区域的左上角坐标
    left = (img_width - target_width) / 2
    top = (img_height - target_height) / 2
    right = (img_width + target_width) / 2
    bottom = (img_height + target_height) / 2

    # 裁剪图片
    cropped_image = image.crop((left, top, right, bottom))
    return cropped_image

image_path = "/Users/<USER>/Downloads/97fm3028app30.jpg"
image = Image.open(image_path)


cropped_img = center_crop(image_path, 300, 300)

if cropped_img:
    cropped_img.save("/Users/<USER>/Downloads/cropped_smart.jpg")
    print(f"Image cropped to {cropped_img.size} and saved to ")
else:
    print("Failed to crop image as target size is larger than original.")

# cropper = smartcrop.SmartCrop()
# # 裁剪到 300x300 像素，result 会包含裁剪框的坐标
# result = cropper.crop(image, 300, 300)
# print(result)
# box = (result['top_crop']['x'], result['top_crop']['y'],
#        result['top_crop']['x'] + result['top_crop']['width'],
#        result['top_crop']['y'] + result['top_crop']['height'])
# cropped_image = image.crop(box)
# cropped_image.save("/Users/<USER>/Downloads/cropped_smart.jpg")
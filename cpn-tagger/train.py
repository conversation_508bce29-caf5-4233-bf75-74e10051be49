# train.py
from pydantic import BaseModel
import gradio as gr
from PIL import Image
from fastapi import FastAPI
from pydantic import BaseModel
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import torch
from transformers import AutoProcessor, AutoModelForCausalLM
import os
import csv
import numpy as np
import asyncio
import redisqueue
import json
import time
import yaml
from slugify import slugify
import os
import subprocess
import re



import redisqueue

import time
from typing import Dict
import sys

# 添加 Toolkit 的父目录到 sys.path
toolkit_path = '/root/ai-toolkit'
if toolkit_path not in sys.path:
    sys.path.append(toolkit_path)
try:
    from toolkit import job as toolkit_job
    print("toolkit.job 导入成功！")
except ImportError as e:
    print(f"导入错误: {e}")


JOBS_LEVEL = ["Idle", "Normal", "Member"]

def get_queue_name(jobs_level: str) -> Dict[str, str]:
    return {
        "input": "train:" + jobs_level,
        "output": "train",
        "zset": "train:" + jobs_level,
        "hset": "train"
    }

class TrainRequest(BaseModel):
    q_task_id: str
    tag_alg: str
    image_folder: str
    crop_method: str
    crop_target: str
    crop_size: str
    crop_image_folder: str
    crop_param: dict

TrainStorage1 = "/Users/<USER>/mnt/team-data/872de996f7ab954bc0c18dff2a6e08f4/train"
TrainStorage2 = "/usrdata/team-data/872de996f7ab954bc0c18dff2a6e08f4/train"
TrainStorage = "/team-data/train"

# 获取当前执行文件的绝对路径
file_path = os.path.abspath(__file__)
# 获取所在文件夹路径
dir_path = os.path.dirname(file_path)

models_dir = os.path.join(dir_path, "models")

task_run_status = 1
current_q_task_id = ""
async def task_run():
    global task_run_status
    global current_q_task_id
    while True:
        try:
            if task_run_status != 1:
                print("run status is stop, sleep 10 sec")
                await asyncio.sleep(10)
                continue
            task = redisqueue.task_pop_auto(JOBS_LEVEL, get_queue_name)
            if task is None:
                print("pop_auto:", task, " task is none sleep 3 sec")
                await asyncio.sleep(3)
                continue
            q_task_id = ""
            q_task_name = ""
            try:
                print("pop_auto:", task)
                q_task_id = task["q_task_id"]
                q_task_name = task["q_task_name"]

                if not q_task_name or not q_task_id:
                    raise ValueError("q_task_name or q_task_id is empty")

                current_q_task_id = q_task_id
                await start_train(task)

            except Exception as e:
                print("处理任务出错 err:", e)
                if q_task_name and q_task_id:
                    redisqueue.completed(q_task_name, get_queue_name, q_task_id, {"err": "处理任务出错 err:"+str(e)})
        except Exception as e:
            print("出错了，延迟5秒 err:", e)
            await asyncio.sleep(5)


def recursive_update(d, u):
    for k, v in u.items():
        if isinstance(v, dict) and v:
            d[k] = recursive_update(d.get(k, {}), v)
        else:
            d[k] = v
    return d

job = None
async def progress_callback():
    await asyncio.sleep(20)
    while True:
        await asyncio.sleep(3)
        print("progress_callback")
        if job:
            print(f"my当前进度: %",job.process[0].progress_bar)
            print(f"my当前进度: %", job.process[0].step_num)


async def terminate_job_process():
    print("尝试通过进程名硬停止 train_job.py...")
    stopped_count = 0
    try:
        # 使用 asyncio.subprocess 替代 subprocess.run
        process = await asyncio.create_subprocess_shell(
            "ps aux | grep 'python3 /root/train/train_job.py' | grep -v grep | awk '{print $2}'",
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        stdout, stderr = await process.communicate()
        pids_str = stdout.decode().strip()

        if not pids_str:
            print("未找到 train_job.py 相关的运行进程。")
            return 0

        pids = pids_str.split('\n')
        print(f"找到以下 train_job.py 进程 PID: {pids}")

        for pid in pids:
            try:
                pid = int(pid)
                # 使用 asyncio.create_subprocess_exec 替代 subprocess.run
                kill_process = await asyncio.create_subprocess_exec(
                    "kill", "-9", str(pid),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await kill_process.wait()
                print(f"已强制终止进程 PID: {pid}")
                stopped_count += 1
            except ValueError:
                print(f"无效的 PID: {pid}")
            except Exception as e:
                print(f"停止 PID {pid} 时发生错误: {e}")

        # 使用 asyncio.sleep 替代 time.sleep
        await asyncio.sleep(1)

    except Exception as e:
        print(f"停止进程时发生错误: {e}")

    return stopped_count

async def print_job_process_async(q_task_id: str, log_path: str):
    global current_q_task_id
    while True:
        if not q_task_id:
            break

        if q_task_id != current_q_task_id:
            print("跳出日志打印")
            break

        await asyncio.sleep(15)

        try:
            task = redisqueue.task_get("", get_queue_name, q_task_id)
            if task is None:
                continue

            q_task_abort_at = int(task.get("q_task_abort_at") or 0)
            if q_task_abort_at > 0:
                print("获取到任务终止时间，开始终止任务：", q_task_abort_at)
                stopped_count = await terminate_job_process()
                print("任务终止情况 stopped_count:", stopped_count)
                break


            with open(log_path, 'r', encoding='utf-8') as f:
                content = f.read()
            matches = re.findall(r'(\d+)%\|', content)
            numbers = [int(m) for m in matches]
            result = None
            for num in reversed(numbers):
                if num < 100:
                    result = num
                    break
            if result is None and numbers:
                result = numbers[-1]
            if result is None:
                result = 0
            print("当前任务进度：", result)
            if result > 0:
                progress = round(result / 100, 2)
                redisqueue.progress("", get_queue_name, q_task_id, progress)
        except FileNotFoundError:
            print(f"文件不存在: {log_path}")
            await asyncio.sleep(15)
        except Exception as e:
            print(f"读取文件时发生错误: {e}")
            await asyncio.sleep(15)




async def start_job_process_async(config_file_path: str, log_file_path: str) -> int:
    """
    在 Python 内部以阻塞方式启动 train_job.py，但通过 run_in_executor
    在后台线程中执行，从而不阻塞主事件循环。
    该函数返回子进程的退出码。
    """
    # 获取当前事件循环
    loop = asyncio.get_running_loop()

    # 定义一个同步函数，它将封装原来的 subprocess.run() 调用
    def _run_blocking_job():
        # 确保 train_job.py 路径正确
        train_job_script_path = os.path.join(os.path.dirname(__file__), "train_job.py")
        if not os.path.exists(train_job_script_path):
            print(f"错误: 找不到 train_job.py 脚本在 {train_job_script_path}")
            return 1 # 返回非零表示失败

        # 构建命令字符串
        command_str = (
            f"python3 {train_job_script_path} {config_file_path} {log_file_path} "
            f"2>&1 | tee -a {log_file_path}"
        )
        print(f"尝试在后台线程中以阻塞方式启动命令: {command_str}")

        try:
            process_result = subprocess.run(
                command_str,
                shell=True,
                check=True, # 如果子进程返回非零退出码，会抛出 CalledProcessError
                capture_output=False
            )
            print(f"训练任务子进程已完成，退出码: {process_result.returncode}")
            return process_result.returncode
        except subprocess.CalledProcessError as e:
            print(f"训练任务子进程失败，退出码: {e.returncode}")
            print(f"命令: {e.cmd}")
            return e.returncode
        except Exception as e:
            print(f"在后台线程启动阻塞式训练任务时发生异常: {e}")
            return 1

    # 使用 run_in_executor 将 _run_blocking_job 提交到默认的线程池中执行
    # `None` 表示使用默认的 ThreadPoolExecutor
    # 这样，_run_blocking_job 的执行不会阻塞主事件循环
    return await loop.run_in_executor(
        None,
        _run_blocking_job
    )

def start_job_process(config_file_path: str, log_file_path: str):
    """
    在 Python 内部以阻塞方式启动 train_job.py，
    并将其标准输出/错误重定向到 tee 命令。
    当前函数会阻塞，直到 train_job.py 执行完成。
    """
    # 确保 train_job.py 路径正确
    train_job_script_path = os.path.join(os.path.dirname(__file__), "train_job.py")
    if not os.path.exists(train_job_script_path):
        print(f"错误: 找不到 train_job.py 脚本在 {train_job_script_path}")
        return None # 返回 None 表示启动失败

    # 构建命令字符串，确保参数传递顺序正确
    command_str = (
        f"python3 {train_job_script_path} {config_file_path} {log_file_path} "
        f"2>&1 | tee -a {log_file_path}"
    )
    print(f"尝试在 Python 内部以阻塞方式启动命令: {command_str}")

    try:
        process_result = subprocess.run(
            command_str,
            shell=True,
            check=True, # 如果子进程返回非零退出码，会抛出 CalledProcessError
            capture_output=False # 不捕获输出，让tee的输出直接到终端并写入文件
            # 如果想捕获tee的输出，可以设置为True，然后通过 process_result.stdout 和 process_result.stderr 获取
        )

        print(f"训练任务子进程已完成，退出码: {process_result.returncode}")
        # 如果 check=True 并且没有抛异常，则 returncode 肯定是 0
        return process_result.returncode # 返回退出码，0 表示成功
    except subprocess.CalledProcessError as e:
        print(f"训练任务子进程失败，退出码: {e.returncode}")
        print(f"命令: {e.cmd}")
        # 如果 capture_output=True，这里可以打印 stderr
        # print(f"标准输出: {e.stdout.decode() if e.stdout else '无'}")
        # print(f"标准错误: {e.stderr.decode() if e.stderr else '无'}")
        return e.returncode # 返回非零退出码表示失败
    except Exception as e:
        print(f"启动阻塞式训练任务时发生异常: {e}")
        return 1 # 返回非零退出码表示失败

async def start_train(
    task
):
    global current_q_task_id
    print("进入 start_train")
    q_task_id = task["q_task_id"]
    q_task_name = task["q_task_name"]

    try:
        q_task_input = task["q_task_input"]
        jobs_uuid = q_task_input["jobs_uuid"]
        tags_uuid = q_task_input["tags_uuid"]
        lora_name = q_task_input["lora_name"]
        model_name_or_path = q_task_input["model_name_or_path"]

        if lora_name == "":
            raise ValueError("lora_name is empty")

        slugged_lora_name = slugify(lora_name)
        with open("/root/ai-toolkit/config/examples/train_lora_flux_24gb.yaml", "r") as f:
            config = yaml.safe_load(f)
        config["config"]["name"] = slugged_lora_name


        config["config"]["process"][0]["train"]["steps"] = int(q_task_input.get("total_steps") or 1000)
        #config["config"]["process"][0]["train"]["lr"] = float(lr)
        config["config"]["process"][0]["train"]["batch_size"] = int(q_task_input.get("batch_size") or 1)

        jobs_dir = os.path.join(TrainStorage, "jobs", jobs_uuid)
        config["config"]['process'][0]['training_folder'] = jobs_dir

        if q_task_input.get("trigger_word"):
            config["config"]['process'][0]['trigger_word'] = q_task_input.get("trigger_word")

        config["config"]["process"][0]["model"]["name_or_path"] = model_name_or_path

        dataset_folder = os.path.join(TrainStorage, "tags", tags_uuid)
        config["config"]["process"][0]["datasets"][0]["folder_path"] = dataset_folder

        sample_1 = q_task_input.get("sample_1")
        sample_2 = q_task_input.get("sample_2")
        sample_3 = q_task_input.get("sample_3")

        if sample_1 or sample_2 or sample_3:
            config["config"]["process"][0]["train"]["disable_sampling"] = False
            #config["config"]["process"][0]["sample"]["sample_every"] = steps
            config["config"]["process"][0]["sample"]["sample_steps"] = int(q_task_input.get("sample_steps") or 28)
            config["config"]["process"][0]["sample"]["prompts"] = []
            if sample_1:
                config["config"]["process"][0]["sample"]["prompts"].append(sample_1)
            if sample_2:
                config["config"]["process"][0]["sample"]["prompts"].append(sample_2)
            if sample_3:
                config["config"]["process"][0]["sample"]["prompts"].append(sample_3)
        else:
            config["config"]["process"][0]["train"]["disable_sampling"] = True


        if not os.path.exists(jobs_dir):
            os.makedirs(jobs_dir, exist_ok=True)  # exist_ok=True 防止目录已存在时抛出异常
        config_path = os.path.join(jobs_dir, "config.yaml")
        with open(config_path, "w") as f:
            yaml.dump(config, f)

        log_path = os.path.join(jobs_dir, "train.log")

        log_reader_task = asyncio.create_task(print_job_process_async(q_task_id, log_path))

        # run the job locally
        # print("start get job")
        # job = toolkit_job.get_job(config_path)
        # print("start progress job")
        # progress_callback()
        # print("start run job")
        # job.run()
        # print("start clean job")
        # job.cleanup()

        # exit_code = start_job_process(config_path, log_path)
        exit_code = await start_job_process_async(config_path, log_path)
        print(f"阻塞函数调用完成，最终退出码: {exit_code}")

        print("start set complete job")
        if exit_code == 0:
            redisqueue.completed(q_task_name, get_queue_name, q_task_id, {"msg": f"Training completed successfully. Model saved as {slugged_lora_name}"})
        else:
            reason = f"执行失败 exit_code:{exit_code}"
            if exit_code == -9:
                reason = f"终止执行 exit_code:{exit_code}"
            redisqueue.completed(q_task_name, get_queue_name, q_task_id, {"err": reason, "exit_code": exit_code})
        print(f"Training completed successfully. Model saved as {slugged_lora_name}")
        return f"Training completed successfully. Model saved as {slugged_lora_name}"
    except Exception as e:
        print("进入异常 e:", e)
        redisqueue.completed(q_task_name, get_queue_name, q_task_id, {"err": str(e)})
        return {"code": 1, "msg": "执行失败 err:" + str(e)}
    finally:
        #redisqueue.completed(req.tag_alg, get_queue_name, req.q_task_id, {})
        current_q_task_id = ""
        print("进入 finally")
def test_train():
    config_path = "/tmp/train_output/mzy005/config.yaml"
    print("start get job")
    job = toolkit_job.get_job(config_path)
    job.run()
    job.cleanup()
    print(f"Training completed successfully. Model saved as ==================")

# 创建 FastAPI 应用
app = FastAPI()

# 添加 CORS 中间件，允许跨域请求 (根据你的需求配置)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 定义 FastAPI 接口

@app.on_event("startup")
async def startup_event():
    asyncio.create_task(task_run())
    print("FastAPI 应用启动完成，后台任务已启动。")


async def click_terminate_task():
    global current_q_task_id
    try:
        stopped_count = await terminate_job_process()
        print("任务已成功终止 stopped_count:", stopped_count)
        tmp1 = "当前任务：无"
        if current_q_task_id:
            tmp1 = "终止当前任务：" + current_q_task_id
        return gr.Button(value=tmp1)
    except Exception as e:
        print(f"操作失败: {e}")



async def switch_task_status():
    try:
        global task_run_status
        if task_run_status == 1:
            task_run_status = 0
        else:
            task_run_status = 1
        print(f"操作成功: {task_run_status}")
        tmp = "当前状态：开启"
        if task_run_status == 0:
            tmp = "当前状态：暂停"
        return gr.Button(value=tmp)
        # return f"操作成功: {task_run_status}"
    except Exception as e:
        print(f"操作失败: {task_run_status} {e}")
        #return f"启动失败: {e}"

async def refresh_task_status():
    print("refresh_task_status===============")
    try:
        global task_run_status
        global current_q_task_id
        tmp = "当前状态：开启"
        if task_run_status == 0:
            tmp = "当前状态：暂停"

        tmp1 = "当前任务：无"
        if current_q_task_id:
            tmp1 = "终止当前任务：" + current_q_task_id
        return gr.Button(value=tmp), gr.Button(value=tmp1)
    except Exception as e:
        print(f"操作失败: {task_run_status} {e}")



with gr.Blocks(theme=gr.themes.Soft()) as demo:
    gr.Markdown("## 后台任务控制")
    with gr.Row():
        tmp = "当前状态：开启"
        if task_run_status == 0:
            tmp = "当前状态：暂停"
        btn_switch_task = gr.Button(tmp, variant="primary")
    with gr.Row():
        tmp = "终止任务"
        btn_terminate_task = gr.Button(tmp, variant="primary")
    with gr.Row():
        with gr.Accordion(open=True, label="原始图片"):
            fileImage = gr.Image(type="filepath")
        with gr.Accordion(open=True, label="裁剪图片"):
            fileCropImage = gr.Image(type="filepath")

    btn_switch_task.click(
        fn=switch_task_status,
        inputs=[],
        outputs=[btn_switch_task]  # 可以将操作结果也显示在状态文本框
    )
    btn_terminate_task.click(
        fn=click_terminate_task,
        inputs=[],
        outputs=[]  # 可以将操作结果也显示在状态文本框
    )
    demo.load(
        fn=refresh_task_status,
        inputs=[],
        outputs=[btn_switch_task, btn_terminate_task]
    )

# 将 Gradio 应用挂载到 FastAPI
app = gr.mount_gradio_app(app, demo, path="/")

if __name__ == "__main__":
    print("开始启动uvicorn")
    uvicorn.run(app, host="0.0.0.0", port=7880)
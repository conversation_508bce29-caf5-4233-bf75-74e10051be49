#train_job.py

import sys
import os

# 添加 Toolkit 的父目录到 sys.path
toolkit_path = '/root/ai-toolkit'
if toolkit_path not in sys.path:
    sys.path.append(toolkit_path)
try:
    from toolkit import job as toolkit_job
    print("toolkit.job 导入成功！")
except ImportError as e:
    print(f"导入错误: {e}")

def main(config_path, log_path):
    """主函数，执行训练任务"""
    job = None
    try:
        print("开始执行训练任务")
        print(f"配置文件路径: {config_path}")
        print(f"日志文件路径: {log_path}")

        # 获取并运行任务
        print("开始获取任务")
        job = toolkit_job.get_job(config_path)

        print("开始运行任务")
        job.run()

        print("开始清理任务")
        job.cleanup()

        print("任务执行完成")

    except Exception as e:
        print(f"任务执行失败 e: {e}", exc_info=True)
        sys.exit(1)
    finally:
        print("train_job 进入 finally")
        if job:
            print("进入 job.cleanup")
            job.cleanup()
            print("完成 job.cleanup")


if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("使用方法: python3 train_job.py <配置文件路径> <日志文件路径>")
        sys.exit(1)

    config_path = sys.argv[1]
    log_path = sys.argv[2]

    if not os.path.exists(config_path):
        print(f"错误: 配置文件 {config_path} 不存在")
        sys.exit(1)

    main(config_path, log_path)
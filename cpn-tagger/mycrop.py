import cv2
from PIL import Image
import numpy as np

output_dir = "/Users/<USER>/Downloads/"
output_dir = ""
def scale2win(img: Image.Image, win_width: int, win_height: int) -> Image.Image:
    """
    将图像缩放以填充（覆盖）整个目标窗口尺寸，同时保持其宽高比，然后居中裁剪。

    Args:
        img (Image.Image): 输入的 PIL Image 对象。
        win_width (int): 目标窗口的宽度。
        win_height (int): 目标窗口的高度。

    Returns:
        Image.Image: 缩放并裁剪后的 PIL Image 对象，能适应窗口。
    """
    if not isinstance(img, Image.Image):
        raise TypeError("输入 'img' 必须是一个 PIL Image 对象。")
    if not all(isinstance(dim, int) and dim > 0 for dim in [win_width, win_height]):
        raise ValueError("窗口尺寸必须是正整数。")

    img_width, img_height = img.size
    win_aspect_ratio = win_width / win_height
    img_aspect_ratio = img_width / img_height

    # 确定缩放因子以覆盖窗口
    if img_aspect_ratio > win_aspect_ratio:
        # 图像相对于其高度更宽（或同样宽）于窗口
        # 按高度缩放以匹配窗口高度，宽度将溢出
        scale_factor = win_height / img_height
    else:
        # 图像相对于其宽度更高（或同样高）于窗口
        # 按宽度缩放以匹配窗口宽度，高度将溢出
        scale_factor = win_width / img_width

    new_img_width = int(img_width * scale_factor)
    new_img_height = int(img_height * scale_factor)

    # 调整图像大小
    # Image.LANCZOS 是一个高质量的下采样过滤器
    resized_img = img.resize((new_img_width, new_img_height), Image.LANCZOS)
    return resized_img

def target_crop(img: Image.Image, win_width: int, win_height: int, target_x: int, target_y: int) -> Image.Image:

    # print(win_width,win_height,target_x,target_y)

    img_width, img_height = img.size


    # 计算裁剪区域的左上角坐标，使其中心尽量对齐 target_x, target_y
    # 理想情况下，裁剪框的中心点 (crop_left + win_width/2, crop_top + win_height/2)
    # 应该等于 (target_x, target_y)
    crop_left = target_x - win_width / 2
    crop_top = target_y - win_height / 2
    # print("crop_left, crop_top:", crop_left, crop_top)

    # 调整裁剪区域，确保它完全在图像边界内
    # 确保左边缘不小于0
    crop_left = max(0, crop_left)
    if crop_left + win_width > img_width:
        d = crop_left + win_width - img_width
        crop_left = max(0, crop_left-d)

    # 确保上边缘不小于0
    crop_top = max(0, crop_top)
    if crop_top+win_height > img_height:
        d = crop_top+win_height - img_height
        crop_top = max(0, crop_top-d)

    # 计算裁剪框的右下角坐标
    crop_right = min(crop_left + win_width, img_width)
    crop_bottom = min(crop_top + win_height, img_height)

    # print("crop_left, crop_top, crop_right, crop_bottom:",crop_left, crop_top, crop_right, crop_bottom)

    # 执行裁剪
    # 将浮点数坐标转换为整数
    cropped_img = img.crop((int(crop_left), int(crop_top), int(crop_right), int(crop_bottom)))
    return cropped_img

def center_crop(img: Image.Image, win_width: int, win_height: int) -> Image.Image:
    scale_img = scale2win(img, win_width, win_height)
    width, height = scale_img.size
    # 计算裁剪区域
    left = (width - win_width) / 2
    top = (height - win_height) / 2
    right = left+win_width
    bottom = top+win_height

    print(width, "  ", height, "  ",)
    # 执行裁剪
    cropped_img = scale_img.crop((left, top, right, bottom))
    return cropped_img

def focus_crop(img: Image.Image, win_width: int, win_height: int) -> Image.Image:
    scale_img = scale2win(img, win_width, win_height)
    if output_dir != "":
        scale_img.save(output_dir+"scale_img.jpg")

    width, height = scale_img.size
    detected_faces = detect_faces(scale_img)
    if len(detected_faces)>0:
        print(detected_faces)
        max_face = get_max_face_center(detected_faces)
        print(max_face)
        cropped_img = target_crop(scale_img, win_width, win_height, max_face[1], max_face[2])
    else:
        # 计算裁剪区域
        left = (width - win_width) / 2
        top = (height - win_height) / 2
        right = left+win_width
        bottom = top+win_height
        print(width, "  ", height, "  ",)
        # 执行裁剪
        cropped_img = scale_img.crop((left, top, right, bottom))
    return cropped_img

def detect_faces(img: Image.Image):
    """
    使用OpenCV检测图像中的人脸，并返回每个人脸的中心坐标。

    Args:
        image_path (str): 图像文件的路径。

    Returns:
        list[tuple]: 包含检测到的人脸中心 (x, y) 坐标的列表。
                     如果没有人脸，则返回空列表。
    """
    # 加载Haarcascade分类器
    # 确保 'haarcascade_frontalface_default.xml' 文件在你的脚本目录下
    # 或者提供完整路径
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

    # 从文件加载图像
    # OpenCV 默认使用 BGR 格式，PIL 使用 RGB。这里我们先用 PIL 加载并转换为 OpenCV 格式
    pil_img = img.convert("RGB")
    cv_img = np.array(pil_img) # PIL Image to NumPy array
    cv_img = cv_img[:, :, ::-1].copy() # Convert RGB to BGR for OpenCV

    gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)

    # 检测人脸
    # scaleFactor: 每次图像尺寸减小的比例。1.05 表示每次减少5%。
    # minNeighbors: 每个人脸矩形应该有多少个邻居才能被保留。值越大，误报越少。
    faces = face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30))
    return faces
    # face_centers = []
    # if len(faces) > 0:
    #     print(f"检测到 {len(faces)} 个人脸。")
    #     for (x, y, w, h) in faces:
    #         # 计算人脸的中心点
    #         center_x = x + w // 2
    #         center_y = y + h // 2
    #         face_centers.append((center_x, center_y))
    #
    #         # 可选：在图像上绘制矩形和中心点（用于可视化）
    #         cv2.rectangle(cv_img, (x, y), (x+w, y+h), (255, 0, 0), 2) # 蓝色矩形
    #         cv2.circle(cv_img, (center_x, center_y), 5, (0, 255, 0), -1) # 绿色中心点
    #
    #     # 可选：显示结果图像
    #     # cv2.imshow('Detected Faces', cv_img)
    #     # cv2.waitKey(0)
    #     # cv2.destroyAllWindows()
    #
    # return face_centers

def get_max_face_center(faces):
    max_index = -1
    max_num = 0
    if len(faces) > 0:
        print(f"检测到 {len(faces)} 个人脸。")
        index = 0
        for (x, y, w, h) in faces:
            # 计算人脸的中心点
            if w*h>max_num:
                max_index = index
                max_num = w*h
            index += 1
        face = faces[max_index]
        center_x = face[0] + face[2] // 2
        center_y = face[1] + face[3] // 2
        return (max_index,center_x, center_y)

    return None

if __name__ == "__main__":
    test_image_path = "/Users/<USER>/Downloads/abc.jpg"
    #test_image_path = "/Users/<USER>/Downloads/IMG_8115.JPG"
    test_image = Image.open(test_image_path)
    # detected_faces = detect_faces(test_image)
    # print(detected_faces)
    # max_face = get_max_face_center(detected_faces)
    # print(max_face)
    # img_result = target_crop(test_image,500,500,max_face[1],max_face[2])

    img_result = focus_crop(test_image,512,512)

    # img_original = Image.open(test_image_path)
    # img_result = center_crop(img_original,1024,1024)
    output_path = "/Users/<USER>/Downloads/scaled_to_win_example.jpg"
    img_result.save(output_path)
import os
from PIL import Image
import sys
import torch
import numpy as np
from onnxruntime import InferenceSession
import csv


defaults = {
    "model": "wd-v1-4-moat-tagger-v2",
    "threshold": 0.35,
    "character_threshold": 0.85,
    "replace_underscore": False,
    "trailing_comma": False,
    "exclude_tags": "",
    "ortProviders": ["CUDAExecutionProvider", "CPUExecutionProvider"],
    "HF_ENDPOINT": "https://huggingface.co"
}

models_dir="/usrdata/models/tagger"
class Wd14Tagger:
    def __init__(self):
        print("开始加载 Wd14Tagger")
        self.model_name = ""
        self.model = None
        # # self.device = "cuda" if torch.cuda.is_available() else "cpu"
        # self.device = "cuda"
        #
        # # 设置默认参数
        # self.llm_model = "/chenyudata/ComfyUI/models/LLM/Orenguteng--Llama-3.1-8B-Lexi-Uncensored-V2"  # 根据实际路径修改
        # self.llm_model = "/chenyudata/ComfyUI/models/LLM/unsloth--Meta-Llama-3.1-8B-Instruct"
        # self.dtype = "bf16"  # bf16 或 "nf4"
        # self.vlm_lora = "text_model"
        #
        # # 加载模型
        # try:
        #     self.model = load_models(
        #         model_path=self.llm_model,
        #         dtype=self.dtype
        #     )
        #     print("JoyCaption2Tagger 模型加载完成")
        #     print("model.clip_model:", self.model.clip_model)
        # except Exception as e:
        #     print(f"模型加载失败: {e}")
        #     self.model = None

    def tag(self, image_path, concept_sentence, *captions):
        """
        对图像进行标注，生成描述文本

        Args:
            image_path: 图像路径或PIL图像对象
            concept_sentence: 概念句子，如果非空，将添加到生成的描述末尾
            *captions: 额外的描述参数（未使用）

        Returns:
            生成的描述文本
        """
        print("开始打标 ", image_path)
        model_name = "wd-vit-tagger-v3"
        # 生成描述
        try:
            result = self.wd14_tag(image_path, model_name)
            print("result:", result)
            return result

        except Exception as e:
            print(f"生成描述失败: {e}")
            return f"生成描述失败: {e}"

    def wd14_tag(self,image_path, model_name, threshold=0.35, character_threshold=0.85, exclude_tags="",
                 replace_underscore=True, trailing_comma=False, client_id=None, node=None):
        image = Image.open(image_path).convert("RGB")
        if model_name.endswith(".onnx"):
            model_name = model_name[0:-5]

        name = os.path.join(models_dir, model_name + ".onnx")

        if self.model_name == model_name:
            print("不加载模型")
            model = self.model
        else:
            print("重新加载模型")
            model = InferenceSession(name, providers=defaults["ortProviders"])

        if self.model_name != model_name:
            self.model_name = model_name
            self.model = model


        input = model.get_inputs()[0]
        height = input.shape[1]

        # Reduce to max size and pad with white
        ratio = float(height) / max(image.size)
        new_size = tuple([int(x * ratio) for x in image.size])
        image = image.resize(new_size, Image.LANCZOS)
        square = Image.new("RGB", (height, height), (255, 255, 255))
        square.paste(image, ((height - new_size[0]) // 2, (height - new_size[1]) // 2))

        image = np.array(square).astype(np.float32)
        image = image[:, :, ::-1]  # RGB -> BGR
        image = np.expand_dims(image, 0)

        # Read all tags from csv and locate start of each category
        tags = []
        general_index = None
        character_index = None
        with open(os.path.join(models_dir, model_name + ".csv")) as f:
            reader = csv.reader(f)
            next(reader)
            for row in reader:
                if general_index is None and row[2] == "0":
                    general_index = reader.line_num - 2
                elif character_index is None and row[2] == "4":
                    character_index = reader.line_num - 2
                if replace_underscore:
                    tags.append(row[1].replace("_", " "))
                else:
                    tags.append(row[1])

        label_name = model.get_outputs()[0].name
        probs = model.run([label_name], {input.name: image})[0]

        result = list(zip(tags, probs[0]))

        # rating = max(result[:general_index], key=lambda x: x[1])
        general = [item for item in result[general_index:character_index] if item[1] > threshold]
        character = [item for item in result[character_index:] if item[1] > character_threshold]

        all = character + general
        remove = [s.strip() for s in exclude_tags.lower().split(",")]
        all = [tag for tag in all if tag[0] not in remove]

        res = ("" if trailing_comma else ", ").join(
            (item[0].replace("(", "\\(").replace(")", "\\)") + (", " if trailing_comma else "") for item in all))

        print(res)
        return res


if __name__ == "__main__":
    tagger = Wd14Tagger()
    print(" tagger = Wd14Tagger()")
    tag1 = tagger.tag(
        "/usrdata/team-data/872de996f7ab954bc0c18dff2a6e08f4/train/tags/0c42f21f45354fe4834588e3dcfb131b/96498c3bc5bf4d93b989c8fb89e5d3af.jpeg",
        "a beautiful scene")
    tag2 = tagger.tag(
        "/usrdata/team-data/872de996f7ab954bc0c18dff2a6e08f4/train/tags/0c42f21f45354fe4834588e3dcfb131b/3ce09996d5f84ca8a86eb5a878f6e92a.png",
        "")
    print("Tag 1:", tag1)
    print("Tag 2:", tag2)
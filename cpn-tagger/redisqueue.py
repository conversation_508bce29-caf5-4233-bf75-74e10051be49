from myredis import MyRedis
import json
import time
RedisQueueListInputPrefix = "redisqueue:list:input:"
RedisQueueListOutputPrefix = "redisqueue:list:output:"
RedisQueueZsetPrefix = "redisqueue:zset:"
RedisQueueHsetPrefix = "redisqueue:hset:"

REDIS_CLIENT = MyRedis(host='*************', port=6379, db=0, password="Dm2mrAHN")

def reConnect():
    REDIS_CLIENT.reconnect()

def task_pop_auto(tag_algs, get_queue_name):
    for tag_alg in tag_algs:
        task = task_pop(tag_alg,get_queue_name)
        if task is not None:
            return task
    #time.sleep(3)


def task_pop(tag_alg: str, get_queue_name):
    queue_name = get_queue_name(tag_alg)
    #print("queue_name:", queue_name)
    list_input_key = RedisQueueListInputPrefix + queue_name["input"]

    zset_key = RedisQueueZsetPrefix + queue_name["zset"]

    hset_key = RedisQueueHsetPrefix + queue_name["hset"]
    task_json = REDIS_CLIENT.rpop(list_input_key)
    #print("task_json:", task_json)
    if task_json is None or task_json == "":
        return None
    task = json.loads(task_json)
    try:
        q_task_id = task["q_task_id"]
    except KeyError:
        return None


    REDIS_CLIENT.zrem(zset_key, q_task_id)
    task["q_task_progress"] = 0
    task["q_task_start_at"] = int(round(time.time() * 1000))
    task["q_task_updated_at"] = int(round(time.time() * 1000))

    taskJson = json.dumps(task)
    # print(json.dumps(task))
    REDIS_CLIENT.hset(hset_key, q_task_id, taskJson)
    return task

def task_get(tag_alg: str, get_queue_name, q_task_id: str):
    queue_name = get_queue_name(tag_alg)
    hset_key = RedisQueueHsetPrefix + queue_name["hset"]

    task_json = REDIS_CLIENT.hget(hset_key, q_task_id)
    if task_json is None or task_json == "":
        return None
    task = json.loads(task_json)
    return task

def output(tag_alg: str, get_queue_name, q_task_id: str, q_task_output):
    queue_name = get_queue_name(tag_alg)
    hset_key = RedisQueueHsetPrefix + queue_name["hset"]

    list_output_key = RedisQueueListOutputPrefix + queue_name["output"]
    task_json = REDIS_CLIENT.hget(hset_key, q_task_id)
    if task_json is None or task_json == "":
        return
    task = json.loads(task_json)
    try:
        q_task_id = task["q_task_id"]
    except KeyError:
        return
    task["q_task_output"] = q_task_output
    task_json = json.dumps(task)
    REDIS_CLIENT.lpush(list_output_key, task_json)

def progress(tag_alg: str,get_queue_name, q_task_id: str, progress: float):
    queue_name = get_queue_name(tag_alg)
    hset_key = RedisQueueHsetPrefix + queue_name["hset"]
    task_json = REDIS_CLIENT.hget(hset_key, q_task_id)
    if task_json is None or task_json == "":
        return
    task = json.loads(task_json)
    try:
        q_task_id = task["q_task_id"]
    except KeyError:
        return
    task["q_task_progress"] = progress
    task["q_task_updated_at"] = int(round(time.time() * 1000))
    task_json = json.dumps(task)
    REDIS_CLIENT.hset(hset_key, q_task_id, task_json)

def completed(tag_alg: str, get_queue_name, q_task_id: str, q_task_output):
    queue_name = get_queue_name(tag_alg)
    hset_key = RedisQueueHsetPrefix + queue_name["hset"]

    list_output_key = RedisQueueListOutputPrefix + queue_name["output"]
    task_json = REDIS_CLIENT.hget(hset_key, q_task_id)
    if task_json is None or task_json == "":
        return
    task = json.loads(task_json)
    try:
        q_task_id = task["q_task_id"]
    except KeyError:
        return
    task["q_task_output"] = q_task_output
    task["q_task_completed_at"] = int(round(time.time() * 1000))
    task["q_task_updated_at"] = int(round(time.time() * 1000))
    if q_task_output.get("err"):
        task["q_task_err"] = q_task_output.get("err")

    task_json = json.dumps(task)
    REDIS_CLIENT.hset(hset_key, q_task_id, task_json)
    REDIS_CLIENT.lpush(list_output_key, task_json)

import torch
from transformers import AutoModelForCausalLM, AutoProcessor
from PIL import Image

class Florence2Tagger11:
    def __init__(self):
        print("开始加载Florence2Tagger")
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.torch_dtype = torch.float32
        model_path1 = "/Users/<USER>/git/ai-toolkit-docker/ai-toolkit/Florence-2-large-no-flash-attn"
        model_path = "/usrdata/models/tagger/Florence-2-large-no-flash-attn"
        self.pretrained_model_name_or_path = model_path
        self.model = AutoModelForCausalLM.from_pretrained(
            self.pretrained_model_name_or_path, torch_dtype=self.torch_dtype, trust_remote_code=True
        ).to(self.device)
        self.processor = AutoProcessor.from_pretrained(self.pretrained_model_name_or_path, trust_remote_code=True)

        print("Florence2Tagger加载完成")
    def tag(self, image_path, concept_sentence, *captions):
        print("打标device:", self.device)
        print("开始打标 ", image_path)
        if isinstance(image_path, str):
            image = Image.open(image_path).convert("RGB")

        prompt = "<DETAILED_CAPTION>"
        inputs = self.processor(text=prompt, images=image, return_tensors="pt").to(self.device, self.torch_dtype)

        generated_ids = self.model.generate(
            input_ids=inputs["input_ids"], pixel_values=inputs["pixel_values"], max_new_tokens=1024, num_beams=3
        )

        generated_text = self.processor.batch_decode(generated_ids, skip_special_tokens=False)[0]
        parsed_answer = self.processor.post_process_generation(
            generated_text, task=prompt, image_size=(image.width, image.height)
        )
        caption_text = parsed_answer["<DETAILED_CAPTION>"].replace("The image shows ", "")
        print("caption_text:", caption_text)
        if concept_sentence:
            caption_text = f"{caption_text} [trigger]"
        return caption_text

if __name__ == "__main__":
    tagger = Florence2Tagger()
    tag1 = tagger.tag("test1.jpg", "a beautiful scene")
    tag2 = tagger.tag("test2.png", "")
    print("Tag 1:", tag1)
    print("Tag 2:", tag2)
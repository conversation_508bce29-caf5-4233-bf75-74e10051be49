job: extension
config:
  name: mzy022
  process:
  - type: sd_trainer
    training_folder: /usrdata/train_output_flux_aitoolkit
    device: cuda:0
    network:
      type: lora
      linear: 16
      linear_alpha: 16
    save:
      dtype: float16
      save_every: 200
      max_step_saves_to_keep: 4
      push_to_hub: false
      num_repeats: 10
    datasets:
    - folder_path: datasets/d74201ba-6cc5-4581-bc61-6555e879463c
      caption_ext: txt
      caption_dropout_rate: 0.05
      shuffle_tokens: false
      cache_latents_to_disk: true
      resolution:
      - 768
      flip_aug: true
      keep_tokens: true
    train:
      reg_weight: 0.8
      batch_size: 1
      steps: 1000
      gradient_accumulation: 2
      train_unet: true
      train_text_encoder: false
      gradient_checkpointing: true
      noise_scheduler: flowmatch
      optimizer: adamw8bit
      lr: 0.0004
      lr_scheduler: constant
      lr_scheduler_params: {}
      ema_config:
        use_ema: true
        ema_decay: 0.99
      dtype: bf16
      skip_first_sample: true
      disable_sampling: false
    model:
      name_or_path: /poddata/FLUX.1-dev
      is_flux: true
      quantize: true
      low_vram: false
    sample:
      sampler: flowmatch
      sample_every: 1000
      width: 1024
      height: 1024
      prompts:
      - A person in a bustling cafe mzy022
      neg: ''
      seed: 648
      walk_seed: true
      guidance_scale: 4
      sample_steps: 28
    trigger_word: mzy022
meta:
  name: mzy022
  version: '1.0'

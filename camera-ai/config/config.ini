[server]
Bundle = camera.cyuai.aigc
AppMode = debug
Env = dev
RunTimer = true
HttpPort  = :5203
JwtKey = cbD88c01wb12

DiffusionFilePath = /Users/<USER>/stable-diffusion-webui/aigc-output/
DiffusionDomain = https://img.cyuai.com/

Domain = https://camera.cyuai.com/
CenterServer = http://localhost:5101/api/

AccessKeyId = LTAI5tGmiupcZDwcgABKLCBW
AccessKeySecret = ******************************

WeixinAppId = wx0c28efb0e2571c12
WeixinSecret = ba583e9f885afd6c9a0f667315b9bb20

[database]
Dd = mysql
DbHost = *************
DbPort = 3306
DbUser = aigc-test
DbPassWord = Zeyun1234!
DbName = camera-test
RedisAddr = *************:6379
RedisPassWord = cyuai


[database11测试数据库服务器上的链接]
Dd = mysql
DbHost = **************
DbPort = 3306
DbUser = aigc-test
DbPassWord = Zeyun1234!
DbName = center-test
RedisAddr = **************:6379
RedisPassWord = cyuai

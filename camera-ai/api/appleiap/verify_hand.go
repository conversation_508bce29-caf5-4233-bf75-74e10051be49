package appleiap

import (
	"design-ai/model"
	"design-ai/utils/config"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"design-ai/utils/myhttp"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
)

func VerifyHand(c *gin.Context) {
	var code int
	var msg string

	//tokenHeader := c.Request.Header.Get("Authorization")
	////logger.Info("tokenHeader:", tokenHeader)
	//claims, err1 := middleware.GetClaimsByToken(tokenHeader)
	//if claims == nil || err1 != nil {
	//	logger.Error(claims, err1)
	//	errmsg.Abort(c, errmsg.FAIL, "请先登录账号")
	//	return
	//}

	//claims := c.Value("claims").(*middleware.MyClaims)
	//if claims.UserId <= 0 {
	//	errmsg.Abort(c, errmsg.FAIL, "请先登录")
	//	return
	//}

	var oReq verifyReceiptReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	oReq.UserId = 407
	oReq.TransactionJson = `
{"payment":{"productid":"roodesign_con_01","quantity":"1","username":"r2023053122333000004942"},"transactionDate":"2023-05-31 22:33:41","transactionIdentifier":"290001500938323","transactionReceipt":"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","transactionState":"1"}
`
	logTrack := fmt.Sprintf("user%d ", oReq.UserId)
	logger.Info(logTrack, "transaction_json:  ", oReq.TransactionJson)

	var trans transaction
	if err := json.Unmarshal([]byte(oReq.TransactionJson), &trans); err != nil {
		logger.Error(logTrack, err)
		errmsg.Abort(c, errmsg.FAIL, "序列化数据失败")
	}

	outTradeNo := trans.Payment.Username
	tradeId := trans.TransactionIdentifier

	var recharge model.Recharge
	if err := recharge.GetByOutTradeNo(outTradeNo); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取充值订单出错")
		return
	}
	if oReq.UserId != recharge.UserId {
		logger.Error("订单用户ID 不一致", oReq.UserId, "  ", recharge.UserId)
		errmsg.Abort(c, errmsg.FAIL, "订单用户ID 不一致")
		return
	}

	song := make(map[string]interface{})
	song["receipt-data"] = trans.TransactionReceipt
	song["password"] = "ab2f43217ef1412eace3771e5526362a"

	url := "https://buy.itunes.apple.com/verifyReceipt"
	if config.AppMode == "debug" { //|| recharge.ProductCategory == enums.ProductCategoryEnum.Subscription
		//url = "https://sandbox.itunes.apple.com/verifyReceipt"
	}
	resString, err := myhttp.Post(url, song)
	if len(resString) < 50 {
		logger.Error(err)
		logger.Info(logTrack, "切换验证URL:", resString)
		url = "https://sandbox.itunes.apple.com/verifyReceipt"
		resString, err = myhttp.Post(url, song)
	}
	logger.Info("resString:", resString) // resString:{"status":21007}
	if err != nil {
		logger.Error(logTrack, err)
		errmsg.Abort(c, errmsg.FAIL, "验证票据信息出错")
		return
	}
	mapReceipt := make(map[string]interface{})
	if err := json.Unmarshal([]byte(resString), &mapReceipt); err != nil {
		logger.Error(logTrack, err)
		errmsg.Abort(c, errmsg.FAIL, "序列化票据信息出错")
		return
	}
	success := false
	if val, ok := mapReceipt["status"].(float64); ok {
		if val == 0 {
			success = true
		}
	} else {
		success = false
	}

	if success == false {
		logger.Error(logTrack, success, mapReceipt)
		errmsg.Abort(c, errmsg.FAIL, "购买失败，状态不正确")
		return
	}

	latestReceiptInfo, _ := mapReceipt["latest_receipt_info"].([]interface{})
	//if latestReceiptInfo == nil { //第一次购买 latest_receipt_info字段不存在
	//	logger.Info(logTrack, "第一次购买 latest_receipt_info字段不存在,用receipt里面的in_app数组")
	//	inApp, ok := mapReceipt["receipt"].(map[string]interface{})["in_app"].([]interface{})
	//	if !ok {
	//		logger.Error(logTrack, "获取in_app字段出错")
	//	}
	//	latestReceiptInfo = inApp
	//}

	if err := HandleReceipt(oReq.BRestore, oReq.UserId, outTradeNo, tradeId, mapReceipt["receipt"].(map[string]interface{}), latestReceiptInfo); err != nil {
		logger.Error(logTrack, err)
		errmsg.Abort(c, errmsg.FAIL, "恢复购买失败，分析票据出错")
		return
	}

	result := make(map[string]interface{})

	msg = "购买成功"
	if oReq.BRestore {
		msg = "恢复购买成功"
	}
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "购买失败")
	}
}

func VerifyHand11(c *gin.Context) {
	var code int
	var msg string

	//tokenHeader := c.Request.Header.Get("Authorization")
	////logger.Info("tokenHeader:", tokenHeader)
	//claims, err1 := middleware.GetClaimsByToken(tokenHeader)
	//if claims == nil || err1 != nil {
	//	logger.Error(claims, err1)
	//	errmsg.Abort(c, errmsg.FAIL, "请先登录账号")
	//	return
	//}

	//claims := c.Value("claims").(*middleware.MyClaims)
	//if claims.UserId <= 0 {
	//	errmsg.Abort(c, errmsg.FAIL, "请先登录")
	//	return
	//}

	var oReq verifyReceiptReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.UserId <= 0 {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "未指定用户ID")
		return
	}
	//logger.Info("transaction_json:  ", oReq.TransactionJson)
	//
	//var trans transaction
	//if err := json.Unmarshal([]byte(oReq.TransactionJson), &trans); err != nil {
	//	logger.Error(err)
	//	errmsg.Abort(c, errmsg.FAIL, "序列化数据失败")
	//}
	//
	//outTradeNo := trans.Payment.Username
	//tradeId := trans.TransactionIdentifier
	//
	//song := make(map[string]interface{})
	//song["receipt-data"] = trans.TransactionReceipt
	//song["password"] = "ab2f43217ef1412eace3771e5526362a"
	//
	//url := "https://buy.itunes.apple.com/verifyReceipt"
	////if config.AppMode == "debug" { //|| recharge.ProductCategory == enums.ProductCategoryEnum.Subscription
	////	url = "https://sandbox.itunes.apple.com/verifyReceipt"
	////}
	//resString, err := myhttp.Post(url, song)
	//if len(resString) < 50 {
	//	logger.Info("切换验证URL:", resString)
	//	url = "https://sandbox.itunes.apple.com/verifyReceipt"
	//	resString, err = myhttp.Post(url, song)
	//}
	//logger.Info("resString:", resString) // resString:{"status":21007}

	outTradeNo := "r2023052915222800004717"

	var recharge model.Recharge
	if err := recharge.GetByOutTradeNo(outTradeNo); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取充值订单出错")
		return
	}
	if oReq.UserId != recharge.UserId || true {
		logger.Error("订单用户ID 不一致")
		errmsg.Abort(c, errmsg.FAIL, "订单用户ID 不一致")
		return
	}

	tradeId := "730001154738126"
	resString := `
{
	"receipt": {
		"receipt_type": "Production",
		"adam_id": 6446925122,
		"app_item_id": 6446925122,
		"bundle_id": "design.cyuai.aigc",
		"application_version": "13",
		"download_id": 502466537714647708,
		"version_external_identifier": 857285544,
		"receipt_creation_date": "2023-05-29 07:22:44 Etc/GMT",
		"receipt_creation_date_ms": "1685344964000",
		"receipt_creation_date_pst": "2023-05-29 00:22:44 America/Los_Angeles",
		"request_date": "2023-05-29 07:22:51 Etc/GMT",
		"request_date_ms": "1685344971046",
		"request_date_pst": "2023-05-29 00:22:51 America/Los_Angeles",
		"original_purchase_date": "2023-05-26 10:06:47 Etc/GMT",
		"original_purchase_date_ms": "1685095607000",
		"original_purchase_date_pst": "2023-05-26 03:06:47 America/Los_Angeles",
		"original_application_version": "13",
		"in_app": [{
			"quantity": "1",
			"product_id": "roodesign_con_01",
			"transaction_id": "730001154738126",
			"original_transaction_id": "730001154738126",
			"purchase_date": "2023-05-29 07:22:44 Etc/GMT",
			"purchase_date_ms": "1685344964000",
			"purchase_date_pst": "2023-05-29 00:22:44 America/Los_Angeles",
			"original_purchase_date": "2023-05-29 07:22:44 Etc/GMT",
			"original_purchase_date_ms": "1685344964000",
			"original_purchase_date_pst": "2023-05-29 00:22:44 America/Los_Angeles",
			"is_trial_period": "false",
			"in_app_ownership_type": "PURCHASED"
		}]
	},
	"environment": "Production",
	"latest_receipt": "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",
	"status": 0
}
`

	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "验证票据信息出错")
		return
	}
	mapReceipt := make(map[string]interface{})
	if err := json.Unmarshal([]byte(resString), &mapReceipt); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "序列化票据信息出错")
		return
	}
	success := false
	if val, ok := mapReceipt["status"].(float64); ok {
		if val == 0 {
			success = true
		}
	} else {
		success = false
	}

	if success == false {
		logger.Error(success, mapReceipt)
		errmsg.Abort(c, errmsg.FAIL, "购买失败，状态不正确")
		return
	}

	latestReceiptInfo, _ := mapReceipt["latest_receipt_info"].([]interface{})
	if err := HandleReceipt(oReq.BRestore, oReq.UserId, outTradeNo, tradeId, mapReceipt["receipt"].(map[string]interface{}), latestReceiptInfo); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "恢复购买失败，分析票据出错")
		return
	}

	result := make(map[string]interface{})

	msg = "购买成功"
	if oReq.BRestore {
		msg = "恢复购买成功"
	}
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "购买失败")
	}
}

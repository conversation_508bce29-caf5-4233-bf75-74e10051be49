package manage

import (
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/utils/errmsg"
	"design-ai/utils/tools"
	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
)

type loginReq struct {
	Username  string `json:"username"`
	Password  string `json:"password"`
	Type      string `json:"type"`
	AutoLogin bool   `json:"autoLogin"`
}

type userInfoResp struct {
	UserId    uint   `json:"user_id"`
	Username  string `json:"username"`
	AvatarUrl string `json:"avatar_url"`
	Mobile    string `json:"mobile"`
}

// Login 后台登录
func Login(c *gin.Context) {
	var code int
	var msg string
	var token string

	var oReq loginReq
	var user model.User

	er := c.ShouldBindJSON(&oReq)
	if er != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if len(oReq.Username) < 2 {
		errmsg.Abort(c, errmsg.FAIL, "用户名不正确")
		return
	}

	if len(oReq.Password) < 5 {
		errmsg.Abort(c, errmsg.FAIL, "密码不正确")
		return
	}

	if tools.IsMobile(oReq.Username) {
		user.GetByMobile(oReq.Username)
		if user.ID == 0 {
			errmsg.Abort(c, errmsg.FAIL, "用户不存在")
			return
		}
	} else {
		user.GetByUsername(oReq.Username)
		if user.ID == 0 {
			errmsg.Abort(c, errmsg.FAIL, "用户不存在")
			return
		}
	}

	err := user.CheckPassword(oReq.Password)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "密码错误")
		return
	}

	if user.ID != 1 {
		errmsg.Abort(c, errmsg.FAIL, "账号密码错误")
		return
	}

	token, code, msg = setToken(user)

	resp := userInfoResp{
		UserId:   user.ID,
		Username: user.Username,
		Mobile:   tools.FormatMobileStar(user.Mobile),
	}

	result := make(map[string]interface{})
	result["token"] = token
	result["user"] = resp

	//{"status":"ok","type":"account","currentAuthority":"admin"}
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "ok",
			"result": result,
		})
	} else {

	}
}

// token生成函数
func setToken(user model.User) (string, int, string) {
	j := middleware.NewJWT()
	claims := middleware.MyClaims{
		UserId:   user.ID,
		Username: user.Username,
		Mobile:   tools.FormatMobileStar(user.Mobile),
		StandardClaims: jwt.StandardClaims{
			NotBefore: time.Now().Unix() - 100,
			ExpiresAt: time.Now().Unix() + ***********,
			Issuer:    "design",
		},
	}

	token, err := j.CreateToken(claims)
	if err != nil {
		return "", errmsg.FAIL, "生成token失败"
	}

	return token, errmsg.SUCCESS, ""
}

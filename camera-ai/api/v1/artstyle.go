package v1

import (
	"design-ai/enums"
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/service"
	"design-ai/utils/config"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"github.com/gin-gonic/gin"
	"net/http"
)

type artstyleApi_ struct {
}

var ArtstyleApi artstyleApi_

type artStyleListReq struct {
	ArtStyleId uint   `json:"art_style_id"`
	ArtType    int    `json:"art_type"`
	Sex        int    `json:"sex"`
	AgeSect    string `json:"age_sect"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}

type uploadImgItem struct {
	Md5         string  `json:"md5" gorm:"type:varchar(50);not null;default:'';comment:图片md5"`
	Path        string  `json:"-" gorm:"type:varchar(100);not null;default:'';comment:图片路径"`
	ImgUrl      string  `json:"img_url"`
	SmallImgUrl string  `json:"small_img_url"`
	Width       int     `json:"-" gorm:"type:int;not null;default:0;comment:图片宽度"`
	Height      int     `json:"-" gorm:"type:int;not null;default:0;comment:图片高度"`
	OrderIndex  float32 `json:"-" gorm:"type:float;not null;default:0;comment:排序 越大越前面"`
	State       int     `json:"-" gorm:"type:tinyint;not null;default:0;comment:状态 0初始 1上传成功 2校验失败 3非法图片 4校验通过 5使用中"`
}

type artStyleListItem struct {
	ID           uint            `json:"-"`
	Title        string          `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	ArtType      int             `json:"art_type" gorm:"type:tinyint;not null;default:0;comment:1证件照 2艺术照 3社交头像" `
	Sex          int             `json:"sex" gorm:"type:tinyint;not null;default:0;comment:1男 2女"`
	StyleImgUrls []uploadImgItem `json:"style_img_urls" gorm:"type:json;comment:风格图片"`
	OrderIndex   int             `json:"-" gorm:"type:int;not null;default:0;comment:序号(越大越前面)"`
	PriceCoin    int             `json:"-" gorm:"type:int;not null;default:0;comment:所需Coin"`
	IsHot        int             `json:"is_hot" gorm:"type:int;not null;default:0;comment:热门推荐"`
	IsDefault    int             `json:"is_default" gorm:"type:int;not null;default:0;comment:1默认"`
}

func (obj artstyleApi_) GetList(c *gin.Context) {
	var code int
	var oReq artStyleListReq
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	if err := errmsg.ShouldBindJSON(c, &oReq); err != nil {
		logger.Error(err)
		return
	}
	if oReq.ArtType == 0 {
		oReq.ArtType = 2
	}
	var artStyle model.ArtStyle
	var arr = make([]artStyleListItem, 0)
	total, err := artStyle.GetList(&arr, oReq.ArtStyleId, oReq.ArtType, oReq.Sex, oReq.AgeSect, " ", " ", 1, oReq.Page, oReq.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据查询失败")
		return
	}

	var uploadImg model.UploadImg
	for i := 0; i < len(arr); i++ {
		var arrImg = make([]uploadImgItem, 0)
		_, err := uploadImg.GetList(&arrImg, uint(0), enums.UploadImgOrigWhereEnum.ArtStyle, arr[i].ID, -1, "asc", 1, 100)
		if err != nil {
			logger.Error(err)
		}
		for j := 0; j < len(arrImg); j++ {
			arrImg[j].ImgUrl = config.DiffusionDomain + arrImg[j].Path
			arrImg[j].SmallImgUrl = config.DiffusionDomain + service.ImgService.GetSmallImagePath(arrImg[j].Path)
		}
		arr[i].StyleImgUrls = arrImg
		//arr[i].StateTxt = enums.HandrawStateEnum.GetKey(arr[i].State)
	}
	var arrOut = make([]artStyleListItem, 0)
	for i := 0; i < len(arr); i++ {
		if len(arr[i].StyleImgUrls) == 0 {
			continue
		}
		arrOut = append(arrOut, arr[i])
	}

	result := make(map[string]interface{})
	result["items"] = arrOut
	result["total"] = total
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}
}

package main

//CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o design_ai.linux
import (
	"design-ai/enums"
	"design-ai/model"
	"design-ai/routes"
	"design-ai/service"
	"design-ai/utils/config"
	"design-ai/utils/logger"
	"design-ai/utils/myredis"
	"fmt"
	"net"
	"os"
	"os/exec"

	"strconv"
	"strings"
	"syscall"

	"github.com/gin-gonic/gin"
)

func main() {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("main奔溃:", e)
		}
	}()

	logger.Info("启动main")
	gin.SetMode(config.AppMode)
	if enums.EnvEnum.GetKey(config.Env) == "" {
		logger.Fatal("运行环境参数值错误：", config.Env)
	}

	model.InitDb()
	myredis.InitRedis()

	if config.RunTimer == true {
		logger.Info("启动RunTimer")
		service.RunTimer()
		logger.Info("启动RunTimer完成")
	}
	routes.InitRouter()
	logger.Info("main启动完成")
}

func getPidByPort(addr string) (int, error) {
	//addr := fmt.Sprintf(":%d", port)
	l, err := net.Listen("tcp", addr)
	if err != nil {
		logger.Error(err)
		if opErr, ok := err.(*net.OpError); ok {
			// 获取导致端口占用的进程的PID
			if syscallErr, ok := opErr.Err.(*os.SyscallError); ok {
				if errno, ok := syscallErr.Err.(syscall.Errno); ok {
					if errno == syscall.EADDRINUSE {
						pidStr := opErr.Error()[len(opErr.Error())-7:]
						pid, err := strconv.Atoi(pidStr)
						if err != nil {
							return 0, err
						}
						return pid, nil
					}
				}
			}
		}
		return 0, err
	}
	defer l.Close()
	logger.Info("port %d is not in use", addr)
	return 0, nil
}

func getPIDByPort(addr string) (int, error) {
	//lsofCmd := exec.Command("lsof", "-t", "-i", ":"+port)
	lsofCmd := exec.Command("lsof", "-t", "-i", addr)
	output, err := lsofCmd.Output()
	if err != nil {
		logger.Error(err)
		return -1, err
	}
	pidStr := strings.TrimSpace(string(output))
	pid, err := strconv.Atoi(pidStr)
	if err != nil {
		logger.Error(err)
		return -1, err
	}
	return pid, nil
}

func killProcess(pid int) error {
	killCmd := exec.Command("kill", "-9", strconv.Itoa(pid))
	if err := killCmd.Run(); err != nil {
		if exitError, ok := err.(*exec.ExitError); ok {
			if status, ok := exitError.Sys().(syscall.WaitStatus); ok {
				if status.ExitStatus() == 1 {
					return fmt.Errorf("process with PID %d not found", pid)
				}
			}
		}
		return err
	}
	return nil
}

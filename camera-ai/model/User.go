package model

import (
	"design-ai/enums"
	"design-ai/utils/logger"
	"errors"
	"fmt"
	"log"
	"strconv"
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
	"gorm.io/plugin/optimisticlock"
)

type User struct {
	gorm.Model
	Username       string    `gorm:"type:varchar(20);not null " json:"username"`
	Mobile         string    `gorm:"type:varchar(20);not null " json:"mobile"`
	Password       string    `gorm:"type:varchar(500);not null;" json:"password" `
	Nickname       string    `json:"nickname" gorm:"type:varchar(50);not null;default:'';comment:昵称"`
	Coin           int       `gorm:"type:int;not null;default:0;comment:钱币" json:"coin"`
	FrozenCoin     int       `gorm:"type:int;not null;default:0;comment:冻结资金" json:"frozen_coin" `
	Role           int       `gorm:"type:int;not null;default:0;comment:权限" json:"role"`
	DigitalId      uint      `json:"digital_id" gorm:"type:bigint;not null;default:0;comment:当前数字人ID"`
	DigitalUuId    string    `json:"digital_uuid" gorm:"type:varchar(50);comment:当前数字人UUID"`
	Avatar         string    `gorm:"type:varchar(100);not null;default:'';comment:头像链接" json:"avatar"`
	Unionid        string    `gorm:"type:varchar(50);comment:微信开放平台的唯一标识符" json:"unionid"`
	Openid         string    `gorm:"type:varchar(50);comment:应用中用户唯一标识" json:"openid"`
	WxSubscribe    int       `gorm:"type:int;not null;default:0;comment:是否同意订阅消息 1已同意" json:"wx_subscribe"`
	PhoneInfo      string    `gorm:"type:json;comment:微信获取手机号码返回信息"json:"phone_info" `
	InvitationCode string    `gorm:"type:varchar(50);not null;default:'';comment:邀请码" json:"invitation_code"`
	InvitedCode    string    `gorm:"type:varchar(50);not null;default:'';comment:被邀请码" json:"invited_code"`
	InvitedUserId  uint      `gorm:"type:bigint;not null;default:0;comment:被邀请用户ID" json:"invited_user_id"`
	Plat           string    `gorm:"type:varchar(50);not null;default:'';comment:来源平台" json:"plat"`
	Channel        string    `json:"channel" gorm:"type:varchar(50);not null;default:'';comment:渠道"`
	RegIp          string    `json:"reg_ip" gorm:"type:varchar(50);not null;default:'';comment:注册IP"`
	RegAddress     string    `json:"reg_address" gorm:"type:varchar(100);not null;default:'';comment:注册地址"`
	RegEnv         string    `json:"reg_env" gorm:"type:json;comment:对应结构UserEnv"`
	MemberType     int       `gorm:"type:int;not null;default:0;comment:会员类型 0不是会员 1普通会员 2高级会员" json:"member_type"`
	MemberExpires  time.Time `gorm:"type:datetime;default:'1900-01-01';comment:会员到期时间" json:"member_expires"`
	Insider        int       `gorm:"type:int;not null;default:0;comment:会员类型 0不是内部用户 1打开 2关闭" json:"insider"`
	Remark         string    `gorm:"type:varchar(50);not null;default:'';comment:备注" json:"remark"`
	Version        optimisticlock.Version
}

func (User) TableName() string {
	return "T_User"
}

func (o *User) GetById(id uint) error {
	return db.First(o, id).Error
}

func (o *User) GetByOpenid(openid string) error {
	if openid == "" {
		return errors.New("openid is empty")
	}
	return db.Debug().First(o, "openid = ?", openid).Error
}

func (o *User) GetByMobile(mobile string) error {
	var total int64
	err := db.Where("mobile = ?", mobile).Find(o).Count(&total).Error
	if total > 1 {
		return errors.New("存在多条数据")
	}
	return err
}

func (o *User) GetByInvitationCode(code string) error {
	return db.Where("invitation_code = ?", code).First(o).Error
}

func (o *User) GetByUsername(username string) error {
	var total int64
	err := db.Select("*").Where("username = ?", username).Find(o).Count(&total).Error
	if total > 1 {
		return errors.New("存在多条数据")
	}
	return err
}

func (o *User) ExistsUsername(username string) (bool, error) {
	var user User
	if err := db.First(&user, "username = ?", username).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			//fmt.Println("Record not found")
			return false, nil
		} else {
			//fmt.Println("Other error:", err)
			return true, err
		}
	} else {
		//fmt.Println("Record found:", user)
		return true, nil
	}
}

func (o *User) ExistsMobile(mobile string) (bool, error) {
	var user User
	if err := db.First(&user, "mobile = ?", mobile).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			//fmt.Println("Record not found")
			return false, nil
		} else {
			//fmt.Println("Other error:", err)
			logger.Error(err)
			return true, err
		}
	} else {
		//fmt.Println("Record found:", user)
		return true, nil
	}
}

func (o *User) ExistsInvitationCode(invitationCode string) (bool, error) {
	var user User
	if err := db.First(&user, "invitation_code = ?", invitationCode).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			//fmt.Println("Record not found")
			return false, nil
		} else {
			//fmt.Println("Other error:", err)
			return true, err
		}
	} else {
		//fmt.Println("Record found:", user)
		return true, nil
	}
}

func (o *User) SetMemberExpires(tx *gorm.DB, memberType int, memberExpires time.Time) error {
	if tx == nil {
		return db.Transaction(func(txx *gorm.DB) error {
			return txx.Model(o).Unscoped().Updates(User{MemberType: memberType, MemberExpires: memberExpires}).Error
		})
	} else {
		return tx.Model(o).Unscoped().Updates(User{MemberType: memberType, MemberExpires: memberExpires}).Error
	}
}

func (o *User) SetInsider(insider int) error {
	return db.Model(o).Omit("version").Updates(map[string]interface{}{"insider": insider}).Error
}

func (o *User) SetDigital(tx *gorm.DB, digitalId uint, digitalUuid string) error {
	return tx.Model(o).Unscoped().Updates(User{DigitalId: digitalId, DigitalUuId: digitalUuid}).Error
}

func (o *User) DelDigital() error {
	return db.Model(o).Omit("version").UpdateColumns(map[string]interface{}{"digital_id": 0, "digital_uuid": ""}).Error
}

//func (o *User) GetByOpenid(openid string) error {
//	var total int64
//	err := db.Select("*").Where("openid = ?", openid).Find(o).Count(&total).Error
//	if total > 1 {
//		return errors.New("存在多条数据")
//	}
//	return err
//}

func (o *User) GetIchiMoreList(lastId uint, pageSize int) ([]User, error) {
	var ary []User

	tx := *db.Debug().Model(o).Where("id>? and coin<?", lastId, 100).Order("id asc")
	tx.Limit(pageSize).Scan(&ary)
	return ary, tx.Error
}

func (o *User) Save() error {
	return db.Save(o).Error
}

func (o *User) ResetHandrawFree() error {
	return db.Debug().Unscoped().Model(&User{}).Omit("version").Where("handraw_free < ?", 10).UpdateColumns(map[string]interface{}{"handraw_free": 10}).Error
}

func (o *User) SetInvitationCode(invitationCode string) error {
	return db.Model(o).Omit("version").Updates(User{InvitationCode: invitationCode}).Error
}

func (o *User) SetNicknameAvatar(nickname string, avatar string) error {
	return db.Model(o).Omit("version").Updates(User{Nickname: nickname, Avatar: avatar}).Error
}

func (o *User) SetMobile(mobile string) error {
	if o.Mobile == "" {
		return db.Debug().Omit("version").Model(o).Updates(User{Mobile: mobile}).Error
	} else {
		return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
			changeRecord := ChangeRecord{
				UserId:      o.ID,
				ChangeType:  enums.ChangeRecordType.UserMobile,
				DataId:      o.ID,
				OriginData:  fmt.Sprintf(`{"mobile":"%s"}`, o.Mobile),
				CurrentData: fmt.Sprintf(`{"mobile":"%s"}`, mobile),
			}

			if err := changeRecord.New(tx); err != nil {
				logger.Error(err)
				return err
			}

			if err := tx.Debug().Omit("version").Model(o).Updates(User{Mobile: mobile}).Error; err != nil {
				logger.Error(err)
				return err
			}
			return nil
		})
	}
}

func (o *User) SetPhoneInfo(phoneInfo string) error {
	return db.Debug().Omit("version").Model(o).Updates(User{PhoneInfo: phoneInfo}).Error
}

func (o *User) SetAvatar(avatar string) error {
	return db.Debug().Omit("version").Unscoped().Updates(User{Avatar: avatar}).Error
}

func (o *User) SetUsername(username string) error {
	return db.Model(o).Omit("version").Updates(User{Username: username}).Error
}

func (o *User) SetUsername11(username string) (int64, error) {
	result := db.Model(o).Omit("version").Updates(User{Username: username})

	return result.RowsAffected, result.Error
}

func (o *User) SetWxSubscribe(sub int) error {
	return db.Model(o).Omit("version").Updates(map[string]interface{}{"wx_subscribe": sub}).Error
}

func (o *User) SetRegEnv(userEnvStr string, address string, plat string, channel string) error {
	return db.Model(o).Omit("version").Updates(User{RegEnv: userEnvStr, RegAddress: address, Plat: plat, Channel: channel}).Error
}

func (o *User) SetPassword() error {
	if o.Password == "" {
		return errors.New("密码不能为空")
	}
	if len(o.Password) > 30 {
		return errors.New("该密码已加密过")
	}
	o.Password = ScryptPw(o.Password)
	return db.Debug().Unscoped().Model(o).Update("password", o.Password).Error
}

func (o *User) CheckPassword(pw string) error {
	return bcrypt.CompareHashAndPassword([]byte(o.Password), []byte(pw))
}

// BeforeCreate 密码加密&权限控制
func (u *User) BeforeCreate(_ *gorm.DB) (err error) {
	u.Password = ScryptPw(u.Password)
	return nil
}

// ScryptPw 生成密码
func ScryptPw(password string) string {
	const cost = 10

	HashPw, err := bcrypt.GenerateFromPassword([]byte(password), cost)
	if err != nil {
		log.Println(err)
	}

	return string(HashPw)
}

func (u *User) StatisticUser(start time.Time, end time.Time) (int64, error) {
	var total int64
	tx := db.Debug().Model(u).Where("created_at > ? AND created_at < ?", start, end).Count(&total)
	err := tx.Error
	return total, err
}

func (u *User) GetUserList(dest interface{}, username string, mobile string, invitor string, orderBy string, page int, pageSize int) (int64, error) {
	var total int64

	// tx := db.Debug().Model(u).Where("username like ? and mobile like ?", "%"+username+"%", "%"+mobile+"%")
	// tx := db.Debug().Table("T_User as u1").Select("u1.*, u2.mobile as invitor_mobile").Joins("LEFT JOIN T_User as u2 ON u1.invited_user_id = u2.id WHERE u1.username like ? and u1.mobile like ?", "%"+username+"%", "%"+mobile+"%")
	tx := db.Debug().Table("T_User as u1").Select("u1.*, u2.mobile as invitor_mobile").Joins("LEFT JOIN T_User as u2 ON u1.invited_user_id = u2.id")

	if username != "" {
		userid, err := strconv.Atoi(username)
		if err == nil {
			tx.Where("u1.id = ?", userid)
		} else {
			tx.Where("u1.username like ?", "%"+username+"%")
		}

	}
	tx.Where("u1.mobile like ?", "%"+mobile+"%")
	if invitor != "" {
		var invitorId uint
		if len(invitor) == 11 {
			var user User
			user.GetByMobile(invitor)
			invitorId = user.ID
		} else {
			ii, _ := strconv.ParseUint(invitor, 10, 64)
			invitorId = uint(ii)
		}
		tx.Where("u1.invited_user_id = ?", invitorId)
	}
	if orderBy != "" {
		tx.Order(orderBy)
	} else {
		tx.Order("id desc")
	}

	if err = tx.Count(&total).Error; err != nil {
		return 0, err
	}
	tx.Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error

}

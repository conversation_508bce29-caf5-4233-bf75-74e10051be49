package model

import (
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type RechargeProduct struct {
	gorm.Model
	BundleId        string          `json:"bundle_id" gorm:"type:varchar(50);not null;default:'';comment:应用唯一标识"`
	Store           string          `json:"store" gorm:"type:varchar(50);not null;default:'';comment:销售平台商店"`
	GroupName       string          `json:"group_name" gorm:"type:varchar(50);not null;default:'';comment:分组名称"`
	ProductCode     string          `json:"product_code" gorm:"type:varchar(50);not null;default:'';comment:产品编号 appleiap支付通道时为apple配置的产品id"`
	ProductCategory string          `json:"product_category" gorm:"type:varchar(50);not null;default:'';comment:产品类型 对应ProductCategory枚举"`
	SubDuration     string          `json:"sub_duration" gorm:"type:varchar(50);not null;default:'';comment:订阅间隔时长 1周 1月 1年 对应SubDurationEnum枚举"`
	ShowTitle       string          `json:"show_title" gorm:"type:varchar(50);not null;default:'';comment:显示名称"`
	Description     string          `json:"description" gorm:"type:varchar(50);not null;default:'';comment:商品描述"`
	Tip             string          `json:"tip" gorm:"type:varchar(50);not null;default:'';comment:显示小文字"`
	Coin            int             `json:"coin" gorm:"type:int;not null;default:0;comment:虚拟币(整数)"`
	Price           decimal.Decimal `json:"price" gorm:"type:decimal(16,2);not null;default:0;comment:价格"`
	OrigPrice       decimal.Decimal `json:"orig_price" gorm:"type:decimal(16,2);not null;default:0;comment:原价格"`
	OrderIndex      int             `json:"order_index" gorm:"type:int;not null;default:0;comment:排序 越小越前面"`
	State           int             `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态 0新创建 1生效中"`
}

func (RechargeProduct) TableName() string {
	return "T_RechargeProduct"
}

func (o *RechargeProduct) GetByID(id uint) error {
	err := db.First(o, id).Error
	return err
}

func (o *RechargeProduct) GetByProductCode(productCode string) error {
	return db.Debug().Where("product_code=?", productCode).First(o).Error
}

func (o *RechargeProduct) GetList(productId uint, bundleId string, store string, groupName string) ([]RechargeProduct, error) {
	ary := make([]RechargeProduct, 0)
	tx := db.Debug().Model(o)
	if productId > 0 {
		tx.Where("id=?", productId)
	} else {
		if bundleId != "" {
			tx.Where("bundle_id=?", bundleId)
		}
		if store != "" {
			tx.Where("store=?", store)
		}
		if groupName != "" {
			tx.Where("group_name=?", groupName)
		}
		tx.Where("state=?", 1)
	}
	tx.Order("order_index asc").Scan(&ary)
	return ary, tx.Error
}

func (o *RechargeProduct) Save() error {
	return db.Debug().Save(o).Error
}

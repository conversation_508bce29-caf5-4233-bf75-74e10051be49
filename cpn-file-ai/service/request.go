package service

type (
	DirSizeReq struct {
		BasePath string `json:"base_path"`
	}

	FileSizeReq struct {
		FilePaths []string `json:"file_paths"` // 文件/文件夹
	}

	CompressFileReq struct {
		FilePaths    []string `json:"file_paths"`         // 要压缩的文件
		CompressPath string   `json:"file_compress_path"` // 压缩后的路径
	}

	GetFileCompressProgressReq struct {
		FileUuid string `json:"file_uuid"`
	}
)

type LFSPointer struct {
	Sha256 string `json:"oid,omitempty"`
}
type HuggingFacFileInfo struct {
	Path string     `json:"path"`
	Type string     `json:"type"`
	Size int64      `json:"size"`
	Lfs  LFSPointer `json:"lfs,omitempty"`
}

type CivitaiModelVersion struct {
	ID          int           `json:"id"`
	Name        string        `json:"name"`
	DownloadURL string        `json:"downloadUrl"`
	Files       []CivitaiFile `json:"files"`
}

type CivitaiFile struct {
	Name        string        `json:"name"`
	SizeK<PERSON>      float64       `json:"sizeKB"`
	Hashes      CivitaiHashes `json:"hashes"`
	Primary     bool          `json:"primary"`
	DownloadURL string        `json:"downloadUrl"`
}

type CivitaiHashes struct {
	SHA256 string `json:"SHA256"`
	CRC32  string `json:"CRC32"`
	BLAKE3 string `json:"BLAKE3"`
}

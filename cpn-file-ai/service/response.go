package service

// 文件操作返回值
type (
	CommonResp struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}
	UserPrivateSizeResp struct {
		CommonResp
		Result struct {
			Data map[string]string
		}
	}

	SizeResp struct {
		CommonResp
		Result struct {
			SumSize int64 `json:"sum_size"`
		}
	}

	CompressFileResp struct {
		CommonResp
		Result struct {
			FileUuid string `json:"file_uuid"`
		} `json:"result"`
	}

	GetFileCompressProgressResp struct {
		CommonResp
		Result struct {
			CompressPercentage uint `json:"compress_percentage"`
		} `json:"result"`
	}

	CivitaiModelResponse struct {
		ID      int    `json:"id"`
		Name    string `json:"name"`
		Version struct {
			ID int `json:"id"`
		} `json:"version"`
		ModelVersions []CivitaiModelVersion `json:"modelVersions"`
	}

	CivitaiModelVersionResponse struct {
		ID    int           `json:"id"`
		Name  string        `json:"name"`
		Files []CivitaiFile `json:"files"`
	}

	ModelFileListResponse struct {
		FileName        string // 模型路径+名称 e: ace_step_transformer/diffusion_pytorch_model.safetensors
		Sha256          string // 文件的sha256
		FileDownloadUrl string // 文件下载地址
		FileSize        int64  // 文件大小
	}
)

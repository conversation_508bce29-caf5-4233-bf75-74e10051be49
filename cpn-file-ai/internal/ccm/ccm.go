// Compute Capability Market
package ccm

import "slices"

type A = []any
type M = map[string]any

func Must(ok bool) {
	if !ok {
		panic(ok)
	}
}

func MustNoError(err error) {
	if err != nil {
		panic(err)
	}
}

// any cast
func Slice2A[E any](a []E) A {
	if a == nil {
		return nil
	}
	b := make(A, len(a))
	for i, e := range a {
		b[i] = e
	}
	return b
}

// build map
func Slice2Map[K comparable, E any](a []E, f func(E) K) map[K]E {
	if a == nil {
		return nil
	}
	m := make(map[K]E, len(a))
	for _, e := range a {
		m[f(e)] = e
	}
	return m
}

// filter+map
func MapSlice[E, T any](a []E, f func(E) (bool, T)) []T {
	if a == nil {
		return nil
	}
	b := make([]T, 0, len(a))
	for _, e := range a {
		if ok, t := f(e); ok {
			b = append(b, t)
		}
	}
	return b
}

func InSlice[E comparable](v E, a ...E) bool {
	return slices.Contains[[]E, E](a, v)
}

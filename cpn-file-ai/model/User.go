package model

import (
	"cpn-file-ai/common/constants"
	"cpn-file-ai/common/jsontime"
	"cpn-file-ai/common/logger"
	"cpn-file-ai/common/sysLogger"
	"cpn-file-ai/common/utils"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
	"gorm.io/plugin/optimisticlock"
	"log"
	"strings"
	"time"
)

type User struct {
	gorm.Model
	UserType             int             `json:"user_type" gorm:"type:int;not null;default:1;comment:用户类型 1算云用户"`
	Username             string          `json:"username" gorm:"type:varchar(50);not null;default:'';comment:登录用户名"`
	ShortId              string          `json:"short_id" gorm:"type:varchar(50);not null;default:'';comment:用户短字符串ID"`
	Mobile               string          `json:"mobile" gorm:"type:varchar(20);not null;default:'';comment:登录手机号"`
	Password             string          `json:"password" gorm:"type:varchar(100);not null;default:'';comment:密码"`
	DisplayName          string          `json:"display_name" gorm:"type:varchar(50);not null;default:'';comment:显示名称"`
	CertName             string          `json:"cert_name" gorm:"type:varchar(100);not null;default:'';comment:真实姓名"`
	CertNo               string          `json:"cert_no" gorm:"type:varchar(100);not null;default:'';comment:真实身份证号"`
	CertType             string          `json:"cert_type" gorm:"type:varchar(50);not null;default:'';comment:认证类型(空为身份证)"`
	CertTime             time.Time       `json:"cert_time" gorm:"type:datetime;default:'1900-01-01';comment:认证时间"`
	Amount               decimal.Decimal `json:"amount" gorm:"type:decimal(16, 8);not null;default:0;comment:当前金额"`
	RewardAmount         decimal.Decimal `json:"reward_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:当前报酬金额"`
	RewardFrozen         decimal.Decimal `json:"reward_frozen" gorm:"type:decimal(16, 8);not null;default:0;comment:提现时冻结金额"`
	Avatar               string          `json:"avatar" gorm:"type:varchar(100);not null;default:'';comment:头像链接"`
	Sex                  int             `json:"sex" gorm:"type:tinyint;not null;default:0;comment:性别 1男 2女"`
	Birthday             time.Time       `json:"birthday" gorm:"type:datetime;default:'1900-01-01';comment:出生日期"`
	Role                 int             `json:"role" gorm:"type:integer;not null;default:1;comment:角色"`     // 参见constants.go->RoleGuestUser
	Status               int             `json:"status" gorm:"type:integer;not null;default:1;comment:用户状态"` //参见constants.go->UserStatusEnabled
	CertifyPassed        int             `json:"certify_passed" gorm:"type:integer;not null;default:0;comment:实名认证是否通过 1通过"`
	StudentCerifyId      int             `json:"student_cerify_id" gorm:"type:bigint;not null;default:0;comment:学生认证 大于0通过"`
	StudentCerifyAt      time.Time       `json:"student_cerify_at" gorm:"type:datetime;default:'1900-01-01';comment:学生认证时间"`
	CompanyCerifyId      int             `json:"company_cerify_id" gorm:"type:bigint;not null;default:0;comment:公司认证 大于0通过"`
	CompanyCerifyAt      time.Time       `json:"company_cerify_at" gorm:"type:datetime;default:'1900-01-01';comment:公司认证时间"`
	CompanyName          string          `json:"company_name" gorm:"type:varchar(100);not null;default:'';comment:公司名称"`
	AccessToken          string          `json:"access_token" gorm:"type:varchar(32);not null;default:'';comment:登录token"` // this token is for system management
	PrivateStorage       string          `json:"private_storage" gorm:"type:varchar(100);not null;default:'';comment:用户私有目录路径"`
	PrivateSize          int64           `json:"private_size" gorm:"type:bigint;not null;default:0;comment:用户私有目录大小(kb)"`
	PrivateStatAt        time.Time       `json:"private_stat_at" gorm:"type:datetime;default:'1900-01-01';comment:用户私有目录统计时间"`
	PrivateImageSize     int64           `json:"private_image_size" gorm:"type:bigint;not null;default:0;comment:用户私有镜像目录大小(kb)"`
	PrivateImageStatAt   time.Time       `json:"private_image_stat_at" gorm:"type:datetime;default:'1900-01-01';comment:用户私有镜像目录统计时间"`
	RechargeAmount       decimal.Decimal `json:"recharge_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:总充值金额"`
	RechargeCount        int             `json:"recharge_count" gorm:"type:integer;not null;default:0;comment:总充值次数"`
	RechargeStatAt       time.Time       `json:"recharge_stat_at" gorm:"type:datetime;default:'1900-01-01';comment:充值统计时间"`
	RechargeFirstAt      time.Time       `json:"recharge_first_at" gorm:"type:datetime;default:'1900-01-01';comment:第一次充值时间"`
	RechargeLastAt       time.Time       `json:"recharge_last_at" gorm:"type:datetime;default:'1900-01-01';comment:最后一次充值时间"`
	Email                string          `json:"email" gorm:"type:varchar(50);not null;default:'';comment:邮箱"`
	InvitationCode       string          `json:"invitation_code" gorm:"type:varchar(50);not null;default:'';comment:邀请码" `
	InvitationRewardRule string          `json:"invitation_reward_rule" gorm:"type:varchar(250);not null;default:'';comment:邀请奖励规则" `
	InvitedCode          string          `json:"invited_code" gorm:"type:varchar(50);not null;default:'';comment:被邀请码" `
	InvitedUserId        uint            `json:"invited_user_id" gorm:"type:bigint;not null;default:0;comment:邀请用户ID" `
	InvitedRewardAmount  decimal.Decimal `json:"invited_reward_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:邀请用户累计奖励金额"`
	InvitedRewardDone    int             `json:"invited_reward_done" gorm:"type:tinyint;not null;default:0;comment:0继续计算，1为奖励完成"`
	LastLoginIp          string          `json:"last_login_ip" gorm:"type:varchar(50);not null;default:'';comment:最后登录Ip"`
	LastLoginTime        time.Time       `json:"last_login_time" gorm:"type:datetime;default:'1900-01-01';comment:最后登录时间"`
	LastLoginAddress     string          `json:"last_login_address" gorm:"type:varchar(50);not null;default:'';comment:最后登录Ip位置"`
	GuideStatus          int             `json:"guide_status" gorm:"type:integer;not null;default:0;comment:新手指引确认时间"`
	RegEnv               string          `json:"reg_env" gorm:"type:json;comment:对应结构UserEnv"`
	Insider              int             `json:"insider" gorm:"type:integer;not null;default:0;comment:内部用户 0不是内部用户 1打开 2关闭" `
	Free                 int             `json:"free" gorm:"type:integer;not null;default:0;comment:1免费使用" `
	Remark               string          `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注"`
	AccountId            uint            `json:"account_id" gorm:"type:bigint;not null;default:0;comment:统一账号ID"` //绑定了这个ID 用户的基本信息以这个为准
	Version              optimisticlock.Version
}

func (User) TableName() string {
	return "T_User"
}

func (o *User) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *User) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *User) GetByShortId(shortId string) error {
	return DB.First(o, "short_id=?", shortId).Error
}

func (o *User) GetByInvitationCode(code string) error {
	return DB.Debug().First(o, "invitation_code = ?", code).Error
}

func (o *User) GetByMobile(mobile string) error {
	return DB.Debug().First(o, "mobile = ?", mobile).Error
}

func (o *User) GetByUsername(username string) error {
	return DB.First(o, "username = ?", username).Error
}

func (o *User) GetByDisplayName(displayName string) error {
	if err := DB.First(o, "display_name=?", displayName).Error; err != nil {
		return err
	}
	if o.ID == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

func (o *User) ExistsUsername(username string) (bool, error) {
	var user User
	if err := DB.First(&user, "username = ?", username).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			//fmt.Println("Record not found")
			return false, nil
		} else {
			//fmt.Println("Other error:", err)
			return true, err
		}
	} else {
		//fmt.Println("Record found:", user)
		return true, nil
	}
}

func (o *User) ExistsMobile(mobile string) (bool, error) {
	var user User
	if err := DB.First(&user, "mobile = ?", mobile).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			//fmt.Println("Record not found")
			return false, nil
		} else {
			//fmt.Println("Other error:", err)
			return true, err
		}
	} else {
		//fmt.Println("Record found:", user)
		return true, nil
	}
}

func (o *User) ListInsider(dest interface{}) error {
	tx := DB.Debug().Model(o)
	tx.Where("insider>?", 0).Scan(dest)
	return tx.Error
}

func (o *User) ListForCheckBalance(dest interface{}, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)
	tx.Order("id asc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *User) RangeById(dest interface{}, lastAutoId uint, pageSize int) error {
	tx := DB.Debug().Model(o)
	tx.Where("id>?", lastAutoId)
	tx.Order("id asc").Limit(pageSize).Scan(dest)
	return tx.Error
}

func (o *User) ListInviters(dest interface{}, page int, pageSize int) (int64, error) {
	//today := time.Date(rewardMonth.Year(), rewardMonth.Month(), 1, 0, 0, 0, 0, rewardMonth.Location())
	var total int64
	tx := DB.Debug().Model(o)
	tx.Where("invitation_code<>'' and invitation_reward_rule<>''")
	tx.Order("id asc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *User) ListForInviterReward(dest interface{}, afterDate time.Time, inviterUserId uint, page int, pageSize int) (int64, error) {
	//today := time.Date(rewardMonth.Year(), rewardMonth.Month(), 1, 0, 0, 0, 0, rewardMonth.Location())
	var total int64
	tx := DB.Debug().Model(o)
	tx.Where("invited_user_id=?", inviterUserId)
	tx.Where("created_at>=?", afterDate)
	tx.Where("invited_reward_done = 0")
	tx.Order("id asc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *User) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {

		if _, ok := queryParm["display_name"]; ok {
			kw := "%" + queryParm["kw"].(string) + "%"
			tx.Where("display_name like ? ", kw)
		}

		if _, ok := queryParm["mobile"]; ok {
			kw := "%" + queryParm["mobile"].(string) + "%"
			tx.Where("mobile like ? ", kw)
		}

		if _, ok := queryParm["user_type"]; ok {
			tx.Where("user_type=?", queryParm["user_type"])
		}

		if _, ok := queryParm["invitation_code"]; ok {
			tx.Where("invitation_code=?", queryParm["invitation_code"])
		}

		if _, ok := queryParm["invited_user_id"]; ok {
			tx.Where("invited_user_id=?", queryParm["invited_user_id"])
		}

		if _, ok := queryParm["invited_code"]; ok {
			tx.Where("invited_code=?", queryParm["invited_code"])
		}

		if _, ok := queryParm["insider"]; ok {
			tx.Where("insider=?", queryParm["insider"])
		}

		if _, ok := queryParm["kw"]; ok {
			kw := "%" + queryParm["kw"].(string) + "%"
			tx.Where("(mobile like ? or display_name like ? or username like ? or remark like ?)", kw, kw, kw, kw)
		}
		if _, ok := queryParm["status"]; ok {
			tx.Where("status=?", queryParm["status"])
		}

		if page == 1 {
			if err := tx.Count(&total).Error; err != nil {
				return 0, err
			}
		}

		if val, ok := queryParm["order"]; ok {
			if val.(string) == "private_size" {
				tx.Order("private_size desc,id desc")
			} else if val.(string) == "private_image_size" {
				tx.Order("private_image_size desc,id desc")
			} else {
				tx.Order("id desc")
			}
		} else {
			tx.Order("id desc")
		}

		tx.Limit(pageSize).Offset((page - 1) * pageSize)
	}
	tx.Scan(dest)
	return total, tx.Error
}

func (o *User) StatsPrivateSize(userId uint) (int64, error) {
	var totalSize int64
	tx := DB.Debug().Model(o).
		Select("IFNULL(sum(private_size),0) as total_size")
	if userId > 0 {
		tx.Where("id = ?", userId)
	}
	tx.Scan(&totalSize)
	return totalSize, tx.Error
}

func (o *User) StatsPrivateSizeOutLimit(userId uint) (int64, error) {
	var totalSize int64
	tx := DB.Debug().Model(o).
		Select("IFNULL(sum(private_size-?),0) as total_size", constants.CloudStoreFreeSize.IntPart())
	if userId > 0 {
		tx.Where("id = ?", userId)
	}
	tx.Where("private_size>?", constants.CloudStoreFreeSize.IntPart())
	tx.Scan(&totalSize)
	return totalSize, tx.Error
}

func (o *User) StatsPrivateImageSizeOutLimit(userId uint) (int64, error) {
	var totalSize int64
	tx := DB.Debug().Model(o).
		Select("IFNULL(sum(private_image_size-?),0) as total_size", constants.ImageStoreFreeSize.IntPart())
	if userId > 0 {
		tx.Where("id = ?", userId)
	}
	tx.Where("private_image_size>?", constants.ImageStoreFreeSize.IntPart())
	tx.Scan(&totalSize)
	return totalSize, tx.Error
}

func (o *User) StatsInviteeCount(inviterUserId uint) (int, error) {
	var count int64
	tx := DB.Debug().Model(o).
		Where("invited_user_id = ?", inviterUserId).
		Count(&count)
	return int(count), tx.Error
}

func (o *User) StatsInvitation(inviterUserId uint) (decimal.Decimal, int, error) {
	type tempStats struct {
		TotalReward decimal.Decimal `json:"total_reward"`
		TotalCount  int             `json:"total_count"`
	}

	// 使用 Scan 方法将查询结果映射到临时结构体
	var stats tempStats

	tx := DB.Debug().Model(o).
		Select("IFNULL(sum(invited_reward_amount), 0) as total_reward, IFNULL(count(id),0) as total_count").
		Where("invited_user_id = ?", inviterUserId).
		Scan(&stats)
	if tx.Error != nil {
		return decimal.Zero, 0, tx.Error
	}

	return stats.TotalReward, stats.TotalCount, tx.Error
}

func (o *User) SetAccountId(accountId uint) error {
	return DB.Model(o).Omit("version").Where("account_id=0").Updates(User{AccountId: accountId}).Error
}

func (o *User) SetAccessToken(accessToken string) error {
	//return DB.Model(o).Omit("version").Updates(User{AccessToken: accessToken}).Error
	return DB.Model(o).Omit("version").Update("access_token", accessToken).Error
}
func (o *User) SetInsider(insider int) error {
	return DB.Model(o).Omit("version").Update("insider", insider).Error
}
func (o *User) SetDisplayName(displayName string) error {
	return DB.Model(o).Omit("version").Updates(User{DisplayName: displayName}).Error
}
func (o *User) SetAvatar(avatar string) error {
	return DB.Model(o).Omit("version").Updates(User{Avatar: avatar}).Error
}
func (o *User) SetMobile(avatar string) error {
	return DB.Model(o).Omit("version").Updates(User{Mobile: o.Mobile}).Error
}
func (o *User) SetUserType(userType int) error {
	return DB.Model(o).Omit("version").Updates(User{UserType: userType}).Error
}

func (o *User) SetRemark(remark string) error {
	return DB.Model(o).Omit("version").Update("remark", remark).Error
}

func (o *User) SetDestroy(remark string) error {
	if len(o.Mobile) < 1 {
		return errors.New("手机号码不正确")
	}
	if o.Status == 4 {
		return errors.New("该账号已经是注销状态")
	}
	// 将字符串转为 rune 切片，处理每个字符
	runes := []rune(o.Mobile)
	// 修改第一个字符为 '4'
	runes[0] = '4'
	// 将 rune 切片转换回字符串
	newStr := string(runes)
	return DB.Model(o).Omit("version").Updates(map[string]interface{}{"mobile": newStr, "status": 4, "remark": remark, "access_token": utils.GetUUID()}).Error
}

func (o *User) SetRegEnv(envStr string) error {
	return DB.Model(o).Unscoped().Update("reg_env", envStr).Error
}

func (o *User) SetLastLogin(clientIp string) error {
	if o.LastLoginIp == clientIp {
		return DB.Model(o).Omit("version").Update("last_login_time", time.Now()).Error
	}
	return DB.Model(o).Omit("version").Update("last_login_time", time.Now()).Update("last_login_ip", clientIp).Update("last_login_address", "").Error
}

func (o *User) SetLastLoginAddress(address string) error {
	return DB.Model(o).Omit("version").Update("last_login_address", address).Error
}

func (o *User) SetInvitationCode(invitationCode string) error {
	return DB.Model(o).Unscoped().Omit("version").Update("invitation_code", invitationCode).Error
}

func (o *User) SetGuideStatus(at int) error {
	return DB.Model(o).Unscoped().Omit("version").Update("guide_status", at).Error
}

func (o *User) SetInvitationRewardRule(ruleJson string) error {
	return DB.Model(o).Unscoped().Omit("version").Update("invitation_reward_rule", ruleJson).Error
}

func (o *User) SetInvitedRewardDone(addRewardAmount decimal.Decimal) error {
	after := o.InvitedRewardAmount.Add(addRewardAmount)
	return DB.Model(o).Unscoped().Updates(map[string]interface{}{"invited_reward_done": 1, "invited_reward_amount": after}).Error
}

//func (o *User) SetInvitedRewardCur(curRewardAmount decimal.Decimal) error {
//	return DB.Model(o).Unscoped().Updates(map[string]interface{}{"invited_reward_cur": curRewardAmount}).Error
//}

func (o *User) SetUserCert(certName string, certNo string, certType string) error {
	return DB.Model(o).Omit("version").Updates(User{CertName: certName, CertNo: certNo, CertType: certType, CertTime: time.Now()}).Error
}

func (o *User) UpdatePrivateSize(userId uint, size int64) error {
	//return DB.Model(o).Omit("version").Where("id=?", userId).Updates(User{PrivateSize: size}).Error
	return DB.Model(o).Omit("version").Where("id=?", userId).Updates(map[string]interface{}{"private_size": size, "private_stat_at": time.Now()}).Error
}

func (o *User) SetPrivateImageSize(imageSize int64) error {
	return DB.Model(o).Omit("version").Updates(map[string]interface{}{"private_image_size": imageSize, "private_image_stat_at": time.Now()}).Error
}

func (o *User) SetRechargeStat(rechargeCount int, rechargeAmount decimal.Decimal, firstRechargeAt time.Time, lastRechargeAt time.Time) error {
	m := make(map[string]interface{})
	if rechargeCount > o.RechargeCount {
		m["recharge_count"] = rechargeCount
	}
	if rechargeAmount.GreaterThan(o.RechargeAmount) {
		m["recharge_amount"] = rechargeAmount
	}
	if o.RechargeFirstAt.Before(constants.NationalDay) && firstRechargeAt.After(o.RechargeFirstAt) {
		m["recharge_first_at"] = firstRechargeAt
	}
	if lastRechargeAt.After(o.RechargeLastAt) {
		m["recharge_last_at"] = lastRechargeAt
	}
	m["recharge_stat_at"] = time.Now()

	return DB.Model(o).Omit("version").Updates(m).Error
}

func (o *User) SetPassword() error {
	if o.Password == "" {
		return errors.New("密码不能为空")
	}
	if len(o.Password) > 30 {
		return errors.New("该密码已加密过")
	}
	o.Password = ScryptPw(o.Password)
	return DB.Debug().Unscoped().Model(o).Update("password", o.Password).Error
}

func (o *User) CheckPassword(pw string) error {
	return bcrypt.CompareHashAndPassword([]byte(o.Password), []byte(pw))
}

// BeforeCreate 密码加密&权限控制
func (u *User) BeforeCreate(_ *gorm.DB) (err error) {
	u.Password = ScryptPw(u.Password)
	return nil
}

// ScryptPw 生成密码
func ScryptPw(password string) string {
	const cost = 10

	HashPw, err := bcrypt.GenerateFromPassword([]byte(password), cost)
	if err != nil {
		log.Println(err)
	}

	return string(HashPw)
}
func (o *User) SetShortId() error {
	if o.ID <= 0 {
		err := errors.New("用户ID错误")
		logger.Error(err, o.ID)
		return err
	}
	if o.CreatedAt.Before(jsontime.DefaultTime()) {
		err := errors.New("用户创建时间错误")
		logger.Error(err, o.ID)
		return err
	}
	if o.ShortId != "" {
		err := errors.New("用户短ID已创建")
		logger.Error(err, o.ID)
		return err
	}
	shortId := utils.EncodeAdvance(int64(o.ID), 12)
	if len(shortId) != 12 {
		err := errors.New("用户短ID创建失败")
		logger.Error(err, o.ID)
		return err
	}
	path0 := o.ID / 10000
	path := fmt.Sprintf("%d/%s/", path0, shortId)
	return DB.Model(o).Omit("version").Updates(User{ShortId: shortId, PrivateStorage: path}).Error
}
func IsAdmin(userId int) bool {
	if userId == 0 {
		return false
	}
	var user User
	err := DB.Where("id = ?", userId).Select("role").Find(&user).Error
	if err != nil {
		sysLogger.SysError("no such user " + err.Error())
		return false
	}
	return user.Role >= constants.RoleAdminUser
}

func IsEmailAlreadyTaken(email string) bool {
	return DB.Where("email = ?", email).Find(&User{}).RowsAffected == 1
}

func ResetUserPasswordByEmail(email string, password string) error {
	if email == "" || password == "" {
		return errors.New("邮箱地址或密码为空！")
	}
	hashedPassword, err := utils.Password2Hash(password)
	if err != nil {
		return err
	}
	err = DB.Model(&User{}).Where("email = ?", email).Update("password", hashedPassword).Error
	return err
}

func ValidateAccessToken(token string) (user *User) {
	if token == "" {
		return nil
	}
	token = strings.Replace(token, "CpnAi ", "", 1)
	user = &User{}
	if DB.Where("access_token = ?", token).First(user).RowsAffected == 1 {
		return user
	}
	return nil
}

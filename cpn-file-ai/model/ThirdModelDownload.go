package model

import (
	"cpn-file-ai/enums"
	"gorm.io/gorm"
)

type ThirdModelDownloadTask struct {
	gorm.Model
	TaskUuid         string  `json:"task_uuid" gorm:"type:varchar(50);not null;default:'';comment:任务uuid"`
	UserId           uint    `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID;index"`
	PodUuid          string  `json:"pod_uuid" gorm:"type:varchar(50);not null;default:'';comment:pod uuid"`
	DownloadUrl      string  `json:"download_url" gorm:"type:varchar(200);not null;default:'';comment:下载地址"`
	DownloadPath     string  `json:"download_path" gorm:"type:varchar(200);not null;default:'';comment:下载路径"`
	DownloadFileSize float32 `json:"download_file_size" gorm:"type:float(10,2);not null;default:0;comment:下载文件大小"`
	Status           uint    `json:"status" gorm:"type:int;not null;default:0;comment:状态 1.下载完成 2.下载失败 3.下载中"`
	Msg              string  `json:"msg" gorm:"type:varchar(200);not null;default:'';comment:状态信息"`
	FileSha256       string  `json:"file_sha256" gorm:"type:varchar(64);not null;default:'';comment:文件sha256"`
	ShareModelPath   string  `json:"share_model_path" gorm:"type:varchar(255);not null;default:'';comment:共享模型路径"`
}

// TableName 返回数据库表名
func (*ThirdModelDownloadTask) TableName() string {
	return "T_ThirdModelDownloadTask"
}

// Save 保存或更新记录
func (o *ThirdModelDownloadTask) Save() error {
	return DB.Debug().Save(o).Error
}

// Create 在事务中创建记录
func (o *ThirdModelDownloadTask) Create(tx *gorm.DB) error {
	if tx == nil {
		tx = DB
	}
	return tx.Debug().Create(o).Error
}

// GetById 根据ID查询记录
func (o *ThirdModelDownloadTask) GetById(id uint) error {
	return DB.First(o, id).Error
}

// GetByTaskUuid 根据任务UUID查询记录
func (o *ThirdModelDownloadTask) GetByTaskUuid(taskUuid string) error {
	return DB.Where("task_uuid = ?", taskUuid).First(o).Error
}

// GetByUserId 根据用户ID查询记录
func (o *ThirdModelDownloadTask) GetByUserId(userId uint) error {
	return DB.Where("user_id = ?", userId).First(o).Error
}

// GetByPodUuid 根据Pod UUID查询记录
func (o *ThirdModelDownloadTask) GetByPodUuid(podUuid string) error {
	return DB.Where("pod_uuid = ?", podUuid).First(o).Error
}

// GetList 分页查询列表
func (o *ThirdModelDownloadTask) GetList(dest interface{}, userId uint, status uint, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)

	// 添加查询条件
	if userId > 0 {
		tx = tx.Where("user_id = ?", userId)
	}
	if status > 0 {
		tx = tx.Where("status = ?", status)
	}

	// 计算总数（仅在第一页时计算）
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}

	// 分页查询
	tx = tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	err := tx.Scan(dest).Error
	return total, err
}

// Del 删除记录
func (o *ThirdModelDownloadTask) Del() error {
	return DB.Debug().Delete(o).Error
}

// UpdateStatus 更新状态和消息
func (o *ThirdModelDownloadTask) UpdateStatus(status uint, msg string) error {
	return DB.Model(o).Updates(map[string]interface{}{
		"status": status,
		"msg":    msg,
	}).Error
}

// GetRunningTaskByUserAndPod 查询用户在指定Pod下是否有正在进行的下载任务
func (o *ThirdModelDownloadTask) GetRunningTaskByUserAndPod(userId uint, podUuid string) error {
	return DB.Where("user_id = ? AND pod_uuid = ? AND status = ?", userId, podUuid, enums.ModelDownloadStatusDownloading).First(o).Error
}

func (o *ThirdModelDownloadTask) GetByFileSha256(fileSha256 string) error {
	return DB.Where("file_sha256 = ?", fileSha256).Where("status = ?", enums.ModelDownloadStatusCompleted).Find(o).Error
}

package model

import (
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"gorm.io/plugin/optimisticlock"
	"time"
)

type CardStatusEnum int

const (
	CardStatusInvalid CardStatusEnum = 0
	CardStatusValid                  = 1
	CardStatusPause                  = 2
	CardStatusDestroy                = 4
)

func CardStatusEnumName(i CardStatusEnum) string {
	switch i {
	case CardStatusInvalid:
		return "无效"
	case CardStatusValid:
		return "有效"
	case CardStatusPause:
		return "暂停使用"
	case CardStatusDestroy:
		return "已作废"
	}
	return ""
}

type Card struct {
	gorm.Model
	Uuid        string          `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID"`
	CouponId    uint            `json:"coupon_id" gorm:"type:bigint;not null;default:0;comment:优惠卷ID"`
	Title       string          `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	CardNo      string          `json:"card_no" gorm:"type:varchar(50);not null;default:'';comment:卡号"`
	ValidDays   int             `json:"valid_days" gorm:"type:int;not null;default:0;comment:绑定后有效天数"`
	BindTime    time.Time       `json:"bind_time" gorm:"type:datetime;default:'1900-01-01';comment:绑定时间"`
	ExpireDate  time.Time       `json:"expire_date" gorm:"type:datetime;default:'1900-01-01';comment:过期日期"`
	BuyUserId   uint            `json:"buy_user_id" gorm:"type:bigint;not null;default:0;comment:买家用户ID"`
	BindUserId  uint            `json:"bind_user_id" gorm:"type:bigint;not null;default:0;comment:绑定的用户ID"`
	SalePrice   decimal.Decimal `json:"sale_price" gorm:"type:decimal(16, 8);not null;default:0;comment:销售价格"`
	FacePrice   decimal.Decimal `json:"face_price" gorm:"type:decimal(16, 8);not null;default:0;comment:面值"`
	LeaveAmount decimal.Decimal `json:"leave_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:剩余金额"`
	PodIds      string          `json:"pod_ids" gorm:"type:varchar(1500);not null;default:'';comment:定向使用PodIDs"`
	Pods        string          `json:"pods" gorm:"type:json;comment:定向Pod"`
	Remark      string          `json:"remark" gorm:"type:varchar(200);not null;default:'';comment:用户备注"`
	Status      int             `json:"status" gorm:"type:int;not null;default:0;comment:状态 1有效 2暂停使用 4作废"`
	OrderNo     string          `json:"order_no" gorm:"type:varchar(50);comment:支付订单编号"`
	Version     optimisticlock.Version
}

func (Card) TableName() string {
	return "T_Card"
}

func (o *Card) Save() error {
	return DB.Debug().Save(o).Error
}

// cards []Card  传&cards
func (o *Card) Create(tx *gorm.DB, value interface{}) error {
	return tx.Debug().Create(value).Error
}

func (o *Card) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *Card) GetByUuid(uuid string) error {
	return DB.First(o, "uuid=?", uuid).Error
}

func (o *Card) GetByCardNo(cardNo string) error {
	return DB.First(o, "card_no=?", cardNo).Error
}

func (o *Card) IsCardUser(userId uint) bool {
	if err := DB.First(o, "bind_user_id=?", userId).Error; err != nil {
		return false
	} else {
		return true
	}
}

func (o *Card) CardValid(userId uint, podId uint) (decimal.Decimal, int, error) {

	type Result struct {
		LeaveAmount decimal.Decimal `json:"leave_amount"`
		LeaveCount  int             `json:"leave_count"`
	}
	var dest Result
	currentTime := time.Now()

	// 使用当前年月日创建今天的日期
	today := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, currentTime.Location())

	tx := DB.Debug().Model(o)
	tx.Select("sum(leave_amount) as leave_amount,count(0) as leave_count")
	tx.Where("bind_user_id=?", userId)
	if podId == 0 {
		tx.Where("pod_ids=''")
	} else {
		kw := "%" + fmt.Sprintf("|%d|", podId) + "%"
		tx.Where("(pod_ids='' or pod_ids like?)", kw)
	}

	tx.Where("leave_amount>?", 0)
	tx.Where("status=?", 1)
	tx.Where("expire_date>=?", today)
	tx.Scan(&dest)
	return dest.LeaveAmount, dest.LeaveCount, tx.Error
}

func (o *Card) CardValidAmount(userId uint, podId uint) (decimal.Decimal, error) {

	type Result struct {
		LeaveAmount decimal.Decimal `json:"leave_amount"`
	}
	var dest Result
	currentTime := time.Now()

	// 使用当前年月日创建今天的日期
	today := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, currentTime.Location())

	tx := DB.Debug().Model(o)
	tx.Select("sum(leave_amount) as leave_amount")
	tx.Where("bind_user_id=?", userId)
	if podId == 0 {
		tx.Where("pod_ids=''")
	} else {
		kw := "%" + fmt.Sprintf("|%d|", podId) + "%"
		tx.Where("(pod_ids='' or pod_ids like?)", kw)
	}

	tx.Where("leave_amount>?", 0)
	tx.Where("status=?", 1)
	tx.Where("expire_date>=?", today)
	tx.Scan(&dest)
	return dest.LeaveAmount, tx.Error
}

func (o *Card) CardValidCount(userId uint) (int, error) {

	type Result struct {
		ValidCount int `json:"valid_count"`
	}
	var dest Result
	currentTime := time.Now()

	// 使用当前年月日创建今天的日期
	today := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, currentTime.Location())

	tx := DB.Debug().Model(o)
	tx.Select("count(0) as valid_count")
	tx.Where("bind_user_id=?", userId)
	tx.Where("leave_amount>?", 0)
	tx.Where("status=?", 1)
	tx.Where("expire_date>=?", today)
	tx.Scan(&dest)
	return dest.ValidCount, tx.Error
}

func (o *Card) ListForSettle(dest interface{}, userId uint, podId uint) (int64, error) {
	var total int64

	currentTime := time.Now()

	// 使用当前年月日创建今天的日期
	today := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, currentTime.Location())

	tx := DB.Debug().Model(o)
	tx.Where("bind_user_id=?", userId)
	kw := "%" + fmt.Sprintf("|%d|", podId) + "%"
	if podId == 0 {
		tx.Where("pod_ids=''")
	} else {
		tx.Where("(pod_ids='' or pod_ids like?)", kw)
	}
	tx.Where("leave_amount>?", 0)
	tx.Where("status=?", 1)
	tx.Where("expire_date>=?", today)
	tx.Order("expire_date asc, id asc")

	if dest != nil {
		tx.Scan(dest)
	}
	return total, tx.Error
}

func (o *Card) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["coupon_id"]; ok {
			tx.Where("coupon_id=?", queryParm["coupon_id"])
		}
		if _, ok := queryParm["buy_user_id"]; ok {
			tx.Where("buy_user_id=?", queryParm["buy_user_id"])
		}
		if _, ok := queryParm["bind_user_id"]; ok {
			tx.Where("bind_user_id=?", queryParm["bind_user_id"])
		}
		if _, ok := queryParm["user_id"]; ok {
			tx.Where("buy_user_id=? or bind_user_id = ?", queryParm["user_id"], queryParm["user_id"])
		}
		if _, ok := queryParm["status"]; ok {
			tx.Where("status=?", queryParm["status"])
		}

		tx.Where("status<?", 9)
		if page == 1 {
			if err := tx.Count(&total).Error; err != nil {
				return 0, err
			}
		}
		if _, ok := queryParm["bind_user_id"]; ok {
			tx.Order("CASE WHEN leave_amount >0 AND expire_date>= CURDATE() THEN 1 ELSE 2 END, expire_date asc, id desc").Limit(pageSize).Offset((page - 1) * pageSize)
		} else {
			tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
		}
	}
	if dest != nil {
		tx.Scan(dest)
	}
	return total, tx.Error
}

func (o *Card) Bind(bindUserId uint) error {
	if o.BindUserId > 0 {
		return errors.New("has be bind")
	}
	//expireDate := o.ExpireDate.AddDate(0, 0, o.ValidDays)
	expireDate := time.Now().AddDate(0, 0, o.ValidDays)
	return DB.Model(o).Updates(Card{BindUserId: bindUserId, BindTime: time.Now(), ExpireDate: expireDate}).Error
}

func (o *Card) Updates(m map[string]interface{}) error {
	return DB.Model(o).Debug().Updates(m).Error
}

func (o *Card) DisCard(remark string) error {
	return DB.Model(o).Updates(map[string]interface{}{"status": 4, "remark": remark}).Error
}

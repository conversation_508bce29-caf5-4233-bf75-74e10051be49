package config

import (
	"cpn-file-ai/common/logger"
	"cpn-file-ai/enums"
	"errors"
	"gopkg.in/ini.v1"
	"path/filepath"
	"runtime"
)

var (
	CenterServer string
	Env          string
	HttpPort     string
	JwtKey       string

	PrivateStorage string

	SQL_DSN                 string
	REDIS_CONN_STRING       string
	FileStorageInterfaceUrl string
	TokenCivitai            string
	TokenHuggingface        string
)

func init() {
	file, err := ini.Load("./config/config.ini")
	if err != nil {
		// 获取当前源文件的目录
		if _, filename, _, ok := runtime.Caller(0); ok {
			sourceDir := filepath.Dir(filename)
			configPath := filepath.Join(sourceDir, "config.ini")
			if file, err = ini.Load(configPath); err != nil {
				logger.Error("配置文件读取错误", err)
			}
		}
	}
	if file != nil {
		LoadServer(file)
	}

}

func LoadServer(file *ini.File) {
	CenterServer = file.Section("server").Key("CenterServer").MustString("")
	Env = file.Section("server").Key("Env").MustString("")
	HttpPort = file.Section("server").Key("HttpPort").MustString("")
	TokenCivitai = file.Section("server").Key("TokenCivitai").MustString("")
	TokenHuggingface = file.Section("server").Key("TokenHuggingface").MustString("")
	JwtKey = file.Section("server").Key("JwtKey").MustString("")
	aesKey := file.Section("server").Key("AesKey").MustString("")
	if len(aesKey) != 16 {
		err := errors.New("aesKey位数不正确")
		logger.Fatal(err, "len:", len(aesKey))
		panic(err)
	}

	PrivateStorage = file.Section("server").Key("PrivateStorage").MustString("")
	if PrivateStorage == "" {
		PrivateStorage = "/mnt/user-data/store0/"
	}
	if Env == enums.EnvEnum.DEV {
		PrivateStorage = file.Section("server").Key("PrivateStorage").MustString("")
	}

	FileStorageInterfaceUrl = file.Section("server").Key("FileStorageInterfaceUrl").MustString("")

	SQL_DSN = file.Section("database").Key("SQL_DSN").MustString("")
	REDIS_CONN_STRING = file.Section("database").Key("REDIS_CONN_STRING").MustString("")
}

package utils

import (
	"cpn-file-ai/common/logger"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"math/rand"
	"net"
	"os"
	"strconv"
	"strings"
	"time"
)

func GetLocalIP() string {
	// 获取本地所有网络接口
	interfaces, err := net.Interfaces()
	if err != nil {
		logger.Error(err)
	}

	// 遍历网络接口
	for _, iface := range interfaces {
		// 忽略loopback接口（如lo0）和没有物理地址的接口
		if iface.Flags&net.FlagUp == 0 || iface.Flags&net.FlagLoopback != 0 {
			continue
		}

		// 获取每个接口的地址
		addrs, err := iface.Addrs()
		if err != nil {
			logger.Error(err)
		}

		// 遍历接口的所有地址
		for _, addr := range addrs {
			// 确保地址是IPv4格式
			ipnet, ok := addr.(*net.IPNet)
			if ok && ipnet.IP.To4() != nil {
				return ipnet.IP.String()
			}
		}
	}
	return ""
}

func GetMd5(oMd5Str string) string {

	has := md5.Sum([]byte(oMd5Str))
	md5Str := hex.EncodeToString(has[:])
	if len(md5Str) != 32 {
		logger.Error("err md5位数不正确，", md5Str, "  ", oMd5Str)
		return ""
	}
	return md5Str
}

func GetUUID() string {
	code := uuid.New().String()
	code = strings.Replace(code, "-", "", -1)
	return code
}

const keyChars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"

func init() {
	rand.Seed(time.Now().UnixNano())
}

func GetRandomString(length int) string {
	rand.Seed(time.Now().UnixNano())
	key := make([]byte, length)
	for i := 0; i < length; i++ {
		key[i] = keyChars[rand.Intn(len(keyChars))]
	}
	return string(key)
}

func GetTimeString() string {
	now := time.Now()
	return fmt.Sprintf("%s%d", now.Format("20060102150405"), now.UnixNano()%1e9)
}

func GetOrDefault(env string, defaultValue int) int {
	if env == "" || os.Getenv(env) == "" {
		return defaultValue
	}
	num, err := strconv.Atoi(os.Getenv(env))
	if err != nil {
		logger.Error(fmt.Sprintf("failed to parse %s: %s, using default value: %d", env, err.Error(), defaultValue))
		return defaultValue
	}
	return num
}

func FormatMobileStar(mobile string) string {
	if len(mobile) <= 10 {
		return mobile
	}
	return mobile[:3] + "****" + mobile[7:]
}

func Int2String(num int) string {
	return strconv.Itoa(num)
}
func Uint2String(num uint) string {
	return strconv.FormatUint(uint64(num), 10)
}

func GetJsonFromStruct(m interface{}) string {
	jsonbyte, err := json.Marshal(m)
	if err != nil {
		logger.Error(err)
		return ""
	}
	return string(jsonbyte)
}

func GetStructAryFromJson(ary interface{}, jsonStr string) error { //ary是地址 需要加&  在外面第一结构体数组 var data []struct
	//var data []map[string]interface{}
	// 反序列化 JSON 字符串
	err := json.Unmarshal([]byte(jsonStr), ary)
	if err != nil {
		logger.Error(err, "jsonStr：", jsonStr)
		return err
	}
	return nil
}

func UrlJoin(a string, b string) string {
	if !strings.HasSuffix(a, "/") {
		a = a + "/"
	}
	if strings.HasPrefix(b, "/") {
		b = b[1:len(b)]
	}
	return a + b
}

// DeduplicateStrings 接收 []string 并返回去重后的 []string
func DeduplicateStrings(input []string) []string {
	seen := make(map[string]struct{})
	var result []string

	for _, s := range input {
		if _, exists := seen[s]; !exists {
			seen[s] = struct{}{}
			result = append(result, s)
		}
	}

	return result
}

package constants

import (
	"os"
	"strconv"
	"sync"
	"time"

	"github.com/shopspring/decimal"

	"github.com/google/uuid"
)

var NationalDay = time.Date(1949, time.October, 1, 0, 0, 0, 0, time.Local)

var Version = "v1.1.9" // this hard coding will be replaced automatically when building, no need to manually change
var SystemName = "CPN AI"
var Footer = ""
var Logo = ""
var TopUpLink = ""
var ChatLink = ""
var QuotaPerUnit = 500 * 1000.0 // $0.002 / 1K tokens
var DisplayInCurrencyEnabled = true
var DisplayTokenStatEnabled = true

var ImageStoreFreeSize = decimal.NewFromInt(50 * 1024 * 1024 * 1024) //50G免费的镜像空间

var CloudStoreLimitSize = decimal.NewFromInt(300 * 1024 * 1024 * 1024) //300G封顶的算云空间
var CloudStoreFreeSize = decimal.NewFromInt(50 * 1024 * 1024 * 1024)   //50G免费的算云空间
var CloudStoreHourPrice = decimal.NewFromFloat(0.00036)                //算云存储0.00036元/GB/小时

// Any options with "Secret", "Token" in its key won't be return by GetOptions

var SessionSecret = uuid.New().String()

var OptionMap map[string]string
var OptionMapRWMutex sync.RWMutex

var PasswordLoginEnabled = true
var PasswordRegisterEnabled = true
var EmailVerificationEnabled = false
var GitHubOAuthEnabled = false
var WeChatAuthEnabled = false
var TurnstileCheckEnabled = false
var RegisterEnabled = true

var EmailDomainRestrictionEnabled = false
var EmailDomainWhitelist = []string{
	"gmail.com",
	"163.com",
	"126.com",
	"qq.com",
	"outlook.com",
	"hotmail.com",
	"icloud.com",
	"yahoo.com",
	"foxmail.com",
}

var DebugEnabled = os.Getenv("DEBUG") == "true"

var LogConsumeEnabled = true

var SMTPPort = 587

var QuotaForNewUser = 0
var QuotaForInviter = 0
var QuotaForInvitee = 0
var ChannelDisableThreshold = 5.0
var AutomaticDisableChannelEnabled = false
var QuotaRemindThreshold = 1000
var PreConsumedQuota = 500
var ApproximateTokenEnabled = false
var RetryTimes = 0

var requestInterval, _ = strconv.Atoi(os.Getenv("POLLING_INTERVAL"))

const (
	RequestIdKey = "X-Cpnai-Request-Id"
)

const (
	RoleGuestUser  = 0
	RoleCommonUser = 1
	RoleAdminUser  = 10
	RoleRootUser   = 100
)

var (
	FileUploadPermission    = RoleGuestUser
	FileDownloadPermission  = RoleGuestUser
	ImageUploadPermission   = RoleGuestUser
	ImageDownloadPermission = RoleGuestUser
)

const (
	UserStatusEnabled  = 1 // don't use 0, 0 is the default value!
	UserStatusDisabled = 2 // also don't use 0
	UserStatusDestroy  = 4 //用户注销
)

const (
	EmailContentTypeHtml = "text/html"
	EmailContentTypeText = "text/plain"
)

package middleware

import (
	"cpn-file-ai/common/constants"
	"cpn-file-ai/common/logger"
	"cpn-file-ai/model"
	"github.com/gin-gonic/gin"
	"net/http"
	"strings"
)

func authHelper(c *gin.Context, minRole int) {
	//session := sessions.Default(c)

	var claims *MyClaims
	{
		// Check access token
		urlPath := c.Request.URL.Path
		authorization := c.Request.Header.Get("Authorization")

		if authorization == "" {
			if strings.HasSuffix(urlPath, "/api/train_filedown") || strings.HasSuffix(urlPath, "/api/filedown") || strings.HasSuffix(urlPath, "/api/files/compress_download") {
				if v, err := c.<PERSON>("Authorization"); err != nil {
					logger.Error("获取Cookie失败 err:", err)
				} else {
					logger.Info("获取到Cookie:", v)
					authorization = v
				}
			}
		}

		if authorization == "" {
			c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{
				"code": 2,
				"msg":  "无权进行此操作，未登录且未提供 access token",
			})
			c.Abort()
			return
		}

		jwt := NewJWT()
		// 解析token
		if tmpClaims, err := jwt.ParserToken(authorization); err != nil {
			logger.Error(err)
			c.JSON(http.StatusOK, gin.H{
				"code": 2,
				"msg":  "无权进行此操作，提供的token不正确",
			})
			c.Abort()
			return
		} else {
			if claim, err := GetAccessToken(tmpClaims.AccessToken); err == nil {
				claims = &claim
				//logger.Info("缓存获取登录信息 数量：", CountAccessToken(), " 内容：", utils.GetJsonFromStruct(claims))
			} else {
				user := model.ValidateAccessToken(tmpClaims.AccessToken)
				if user == nil {
					msg := "token不存在"
					logger.Error(msg, "authorization:", authorization, " AccessToken:", tmpClaims.AccessToken, "  RequestURI:", c.Request.RequestURI, "  Referer:", c.Request.Referer())
					c.JSON(http.StatusOK, gin.H{
						"code": 2,
						"msg":  "无权进行此操作，token不存在",
					})
					c.Abort()
					return
				} else if user.ID != tmpClaims.UserId {
					msg := "token不匹配"
					logger.Error(msg, tmpClaims)
					c.JSON(http.StatusOK, gin.H{
						"code": 2,
						"msg":  "无权进行此操作，token不匹配",
					})
					c.Abort()
					return
				} else if user.ID > 0 && user.ID == tmpClaims.UserId {
					claims = &MyClaims{
						UserId:      user.ID,
						Mobile:      user.Mobile,
						Username:    user.Username,
						DisplayName: user.DisplayName,
						Role:        user.Role,
						Status:      user.Status,
						AccessToken: user.AccessToken,
					}
					SetAccessToken(tmpClaims.AccessToken, *claims)
					//session.Set("claims", claims)
					//if err := session.Save(); err != nil {
					//	logger.Error(err)
					//}
				} else {
					logger.Error("无权进行此操作", "authorization:", authorization, " AccessToken:", tmpClaims.AccessToken, "  RequestURI:", c.Request.RequestURI, "  Referer:", c.Request.Referer())
					c.JSON(http.StatusOK, gin.H{
						"code": 1,
						"msg":  "无权进行此操作，access token 无效",
					})
					c.Abort()
					return
				}
			}
		}
	}
	if claims.UserId <= 0 {
		c.JSON(http.StatusOK, gin.H{
			"code": 1,
			"msg":  "未获取到用户信息",
		})
		c.Abort()
		return
	}

	if claims.Status == constants.UserStatusDisabled {
		c.JSON(http.StatusOK, gin.H{
			"code": 1,
			"msg":  "用户已被封禁",
		})
		c.Abort()
		return
	}
	if claims.Role < minRole {
		c.JSON(http.StatusOK, gin.H{
			"code": false,
			"msg":  "无权进行此操作，权限不足",
		})
		c.Abort()
		return
	}

	c.Set("claims", claims)
	c.Next()
}

func UserAuth() func(c *gin.Context) {
	return func(c *gin.Context) {
		authHelper(c, constants.RoleCommonUser)
	}
}

package service

import (
	"center-ai/utils/config"
	"center-ai/utils/logger"
	"time"
)

func RunTimer() {

	//go func() {
	//	ticker := time.NewTicker(30 * time.Second)
	//	defer ticker.Stop()
	//	for range ticker.C {
	//		//logger.Info("开始执行每隔30秒业务逻辑")
	//		LoarService.LoadData(false)
	//		TemplatePrompt.LoadData(false)
	//		logger.Info("每隔30秒业务逻辑执行结束")
	//	}
	//}()

	if config.Bundle == "sd.cyuai.aigc" {

		go func() {
			ticker := time.NewTicker(1 * time.Second)
			defer ticker.Stop()
			for range ticker.C {
				logger.Info("开始执行SdService业务逻辑")
				SdService.Run()
				logger.Info("SdService业务逻辑出错,延迟10秒")
			}
		}()
	}

}

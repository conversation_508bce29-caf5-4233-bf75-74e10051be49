package tools

import (
	"center-ai/utils/logger"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"log"
	"math/rand"
	"net"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"
)

func Struct2Map(obj interface{}) map[string]interface{} {
	t := reflect.TypeOf(obj)
	v := reflect.ValueOf(obj)

	var data = make(map[string]interface{})
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		data[field.Tag.Get("json")] = v.Field(i).Interface()
		data[t.Field(i).Name] = v.Field(i).Interface()
	}
	return data
}

func StructJson2Map(obj interface{}) map[string]interface{} {
	t := reflect.TypeOf(obj)
	v := reflect.ValueOf(obj)

	var data = make(map[string]interface{})
	for i := 0; i < t.NumField(); i++ {
		data[t.Field(i).Tag.Get("json")] = v.Field(i).Interface()
	}
	return data
}

func StructJson2MapForField(obj interface{}, out interface{}) map[string]interface{} {
	t := reflect.TypeOf(obj)
	v := reflect.ValueOf(obj)

	outT := reflect.TypeOf(out)

	var data = make(map[string]interface{})
	for i := 0; i < outT.NumField(); i++ {
		name := outT.Field(i).Name
		field, b := t.FieldByName(name)
		if b == false {
			continue
		}
		jsonName := outT.Field(i).Tag.Get("json")
		if len(jsonName) == 0 {
			jsonName = field.Tag.Get("json")
			if len(jsonName) == 0 {
				jsonName = name
			}
		}
		jsonValue := v.FieldByName(name).Interface()
		ty := reflect.TypeOf(jsonValue)
		if ty.Name() == "Time" {
			var tmp time.Time = jsonValue.(time.Time)
			data[jsonName] = tmp.Format("2006-01-02 15:04:05")
		} else {
			data[jsonName] = jsonValue
		}
	}

	return data
}

func GetInvitationCode() string {
	rand.Seed(time.Now().UnixNano())
	const charset = "ABCDEFGHIJKLMNPQRSTUVWXYZ123456789"
	b := make([]byte, 6)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	invitationCode := string(b)
	return invitationCode
}

func IsMobile(mobile string) bool {
	result, _ := regexp.MatchString(`^(1[3|4|5|6|7|8|9][0-9]\d{4,8})$`, mobile)
	if result {
		return true
	} else {
		return false
	}
}

// 判断是否为整数
func IsInt(s string) bool {
	match, _ := regexp.MatchString(`^[\+-]?\d+$`, s)
	return match
}

func ParseUint(s string) uint {
	num, err := strconv.ParseUint(s, 10, 64)
	if err != nil {
		logger.Error(err)
		return 0
	}
	return uint(num)
}

func ParseTime(n int64) time.Time {
	s := fmt.Sprintf("%d", n)
	if len(s) == 13 {
		n = n / 1000
	}
	sendTime := time.Unix(n, 0)
	return sendTime
}

func GetUuid() string {
	uuid := uuid.New()
	uuidStr := strings.Replace(uuid.String(), "-", "", -1)
	return uuidStr
}

func GetRandom(min int, max int) int { //不包括max
	return rand.Intn(max-min) + min
}

func GetRandUsername(size int) (string, error) {
	charStart := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	charStr := "1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()_+=-><:}{?/"
	outStr := ""

	for i := 0; i < 1; i++ {
		num := rand.Intn(len(charStart))
		outStr += fmt.Sprintf("%c", charStr[num])
	}

	for i := 0; i < size-1; i++ {
		num := rand.Intn(len(charStr))
		outStr += fmt.Sprintf("%c", charStr[num])
	}
	return outStr, nil
}

func FormatMobileStar(mobile string) string {
	if len(mobile) <= 10 {
		return mobile
	}
	return mobile[:3] + "****" + mobile[7:]
}

func CreateCaptcha(num int) string {
	str := "1"
	for i := 0; i < num; i++ {
		str += strconv.Itoa(0)
	}
	str10 := str
	int10, err := strconv.ParseInt(str10, 10, 32)
	if err != nil {
		fmt.Println(err)
		return ""
	} else {
		j := int32(int10)
		return fmt.Sprintf("%0"+strconv.Itoa(num)+"v", rand.New(rand.NewSource(time.Now().UnixNano())).Int31n(j))
	}
}

const TimeFormat = "2006-01-02 15:04:05"

func Now() string {
	return time.Now().Format("2006-01-02 15:04:05")
}

func ParseTimeInChina(timeStr string) (time.Time, error) {

	locationName := "Asia/Shanghai"

	// 获取对应时区的 Location 对象
	loc, err := time.LoadLocation(locationName)
	if err != nil {
		logger.Error(err)
		return time.Time{}, err
	}

	if strings.Contains(timeStr, "Etc/GMT") {
		// 解析时间字符串
		t, err := time.Parse("2006-01-02 15:04:05 Etc/GMT", timeStr)
		if err != nil {
			logger.Error(err)
			return time.Time{}, err
		}
		// 转换时区
		return t.In(loc), nil
	} else {
		// 解析时间字符串
		t, err := time.Parse("2006-01-02 15:04:05", timeStr)
		if err != nil {
			logger.Error(err)
			return time.Time{}, err
		}
		// 转换时区
		return t, nil
	}
}

func IsInitTime(tt time.Time) bool {
	t := time.Time{}
	if t.Equal(tt) {
		return true
	}
	zeroDate := time.Date(1900, 1, 1, 0, 0, 0, 0, time.Local)
	return tt.Equal(zeroDate)
}

func DefaultTime() time.Time {
	zeroDate := time.Date(1900, 1, 1, 0, 0, 0, 0, time.Local)
	return zeroDate
}

func ParseTimeFromStr(str string) time.Time {

	loc, _ := time.LoadLocation("Asia/Shanghai") // 东八区
	t, err := time.ParseInLocation("2006-01-02 15:04:05", str, loc)
	if err != nil {
		logger.Error(err)
	}

	return t
}

func ParseTimeToLocation(timeStr string) time.Time {

	locationName := "Asia/Shanghai"

	// 获取对应时区的 Location 对象
	loc, err := time.LoadLocation(locationName)
	if err != nil {
		logger.Error(err)
		return time.Time{}
	}

	if strings.Contains(timeStr, "Etc/GMT") {
		// 解析时间字符串
		t, err := time.Parse("2006-01-02 15:04:05 Etc/GMT", timeStr)
		if err != nil {
			logger.Error(err)
			return time.Time{}
		}
		// 转换时区
		return t.In(loc)
	} else {
		// 解析时间字符串
		t, err := time.Parse("2006-01-02 15:04:05", timeStr)
		if err != nil {
			logger.Error(err)
			return time.Time{}
		}
		// 转换时区
		return t.In(loc)
	}
}

// PathExists 判断一个文件或文件夹是否存在
// 输入文件路径，根据返回的bool值来判断文件或文件夹是否存在
func PathFileExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

func IsFilePath(path string) bool {
	// 匹配 Linux/Unix 系统的文件路径格式
	linuxPathRegex := regexp.MustCompile("^/.*$")
	if linuxPathRegex.MatchString(path) {
		return true
	}

	// 匹配 Windows 系统的文件路径格式
	windowsPathRegex := regexp.MustCompile("^[a-zA-Z]:\\\\|/[a-zA-Z]/.*$")
	if windowsPathRegex.MatchString(path) {
		return true
	}

	// 如果不符合任何一种文件路径格式，返回 false
	return false
}

func GetMapFromJson(jsonStr string) map[string]interface{} {
	var data map[string]interface{}

	// 反序列化 JSON 字符串
	err := json.Unmarshal([]byte(jsonStr), &data)
	if err != nil {
		logger.Error(err, jsonStr)
		return nil
	}
	return data
}

func GetAryFromJson(jsonStr string) []interface{} {
	var ary []interface{}

	// 反序列化 JSON 字符串
	err := json.Unmarshal([]byte(jsonStr), &ary)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return ary
}

func GetJsonFromMap(m map[string]interface{}) string {
	jsonbyte, err := json.Marshal(m)
	if err != nil {
		logger.Error(err)
		return ""
	}
	return string(jsonbyte)
}

func GetJsonFromStruct(m interface{}) string {
	jsonbyte, err := json.Marshal(m)
	if err != nil {
		logger.Error(err)
		return ""
	}
	return string(jsonbyte)
}

func GetStructFromJson(dest interface{}, jsonStr string) error { //传进来的dest 要加&
	err := json.Unmarshal([]byte(jsonStr), dest)
	if err != nil {
		logger.Error(err, jsonStr)
		return err
	}
	return nil
}

func GetInternalIp() {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		fmt.Println(err)
		return
	}

	for _, address := range addrs {
		// 检查 IP 地址是否为 IPv4 地址，且不是回环地址
		if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				fmt.Println("IPv4: ", ipnet.IP.String())
			}
		}
	}
}

func GetExecPath() string {
	file, err := exec.LookPath(os.Args[0])
	if err != nil {
		return ""
	}
	re, err := filepath.Abs(file)
	if err != nil {
		logger.Error("The eacePath failed: %s\n", err.Error())
		return ""
	}
	return re
}
func GetCurrentDirectory() string {
	dir, err := filepath.Abs(filepath.Dir(os.Args[0])) //返回绝对路径 filepath.Dir(os.Args[0])去除最后一个元素的路径
	if err != nil {
		log.Fatal(err)
	}
	return strings.Replace(dir, "\\", "/", -1) //将\替换成/
}

// 获取指定进程名的 PID
func GetPIDs(processName string) ([]int, error) {
	cmd := exec.Command("pidof", processName)
	output, err := cmd.Output()
	if err != nil {
		return nil, err
	}

	logger.Info("output", string(output))
	// 将字符串按空格分割成多个子字符串
	pidsStr := strings.Fields(string(output))

	// 解析每个子字符串为整数
	pids := make([]int, len(pidsStr))
	for i, pidStr := range pidsStr {
		pid, err := strconv.Atoi(pidStr)
		if err != nil {
			return nil, err
		}
		pids[i] = pid
	}

	return pids, nil
}

func GetPID(processName string) (int, error) {
	cmd := exec.Command("pgrep", processName)
	output, err := cmd.Output()
	if err != nil {
		return 0, err
	}
	pidStr := strings.TrimSpace(string(output))
	pid, err := strconv.Atoi(pidStr)
	if err != nil {
		return 0, err
	}
	return pid, nil
}
func GetProcessInfo(processName string) (int, string, error) {
	cmd := exec.Command("bash", "-c", fmt.Sprintf("ps -ef | grep %s | grep -v grep | awk -F ' ' '{print $2,$8}'", processName))
	output, err := cmd.Output()
	if err != nil {
		return 0, "", err
	}
	// 将输出结果转换成字符串，并去掉空格和换行符
	result := strings.TrimSpace(string(output))
	// 分割 pid 和路径
	parts := strings.Split(result, " ")
	pid, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, "", err
	}
	path := parts[1]
	return pid, path, nil
}

func GetPathByPID(pid int) (string, error) {
	//./design_ai.linux > /dev/null 2>&1 &
	cmd := exec.Command("ps", "-p", strconv.Itoa(pid), "-o", "cmd=")
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(string(output)), nil

}

// 向指定 PID 发送 SIGTERM 信号停止进程
func StopProcess(pid int) error {
	cmd := exec.Command("kill", strconv.Itoa(pid))
	err := cmd.Run()
	if err != nil {
		return err
	}
	return nil
}

func GetClientIp(header http.Header) string {
	xForwardedFor := header.Get("X-Forwarded-For")
	ary := strings.Split(xForwardedFor, ",")
	for _, value := range ary {
		if !strings.HasPrefix(value, "192") {
			return value
		}
	}
	return ""
}

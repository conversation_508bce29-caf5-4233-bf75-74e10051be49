package sd

import (
	"center-ai/service"
	"center-ai/utils/errmsg"
	"center-ai/utils/logger"
	"center-ai/utils/tools"
	"github.com/gin-gonic/gin"
)

func SdTagger(c *gin.Context) {
	var oReq service.SdInput
	if err := c.ShouldBindJSO<PERSON>(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.SdServer == "" {
		i := tools.GetRandom(0, len(service.SdService.SdServer))
		oReq.SdServer = service.SdService.SdServer[i]
	}

	ss, err := service.SdService.SdTagger(oReq)
	if err != nil {
		logger.Error(err, "   ", oReq.SdServer)
		errmsg.Abort(c, errmsg.FAIL, "反推图片信息失败")
		return
	} else {
		c.Data(200, "application/json", []byte(ss))
		return
	}
}

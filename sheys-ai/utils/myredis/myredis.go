package myredis

import (
	"errors"
	"sheys-ai/utils/config"
	"sheys-ai/utils/logger"
	"sync"
	"time"

	"github.com/go-redis/redis"
)

var redisdb *redis.Client
var mutex sync.Mutex

func InitRedis() (err error) {
	redisdb = redis.NewClient(&redis.Options{
		Addr:     config.RedisAddr, // 指定
		Password: config.RedisPassWord,
		DB:       0, // redis一共16个库，指定其中一个库即可
	})
	_, err1 := redisdb.Ping().Result()
	if err1 != nil {
		logger.Fatal("redis 初始化失败", err1)
	}
	//logger.Info("redis 链接：", res)
	return
}

// GetRedis 获取连接
func GetRedis() *redis.Client {
	return redisdb
}

func Set(key string, value interface{}, expiration time.Duration) error {

	return redisdb.Set(key, value, expiration).Err()
}

func Get(key string) string {
	v := redisdb.Get(key)
	return v.Val()
}

func Del(key string) error {
	return redisdb.Del(key).Err()
}

func Incr(key string) (int64, error) {
	return redisdb.Incr(key).Result()
}

func LPush(key string, value string) (int64, error) {
	size, err := redisdb.LPush(key, value).Result()
	if err != nil {
		return 0, err
	}
	return size, err
}

func RPush(key string, value string) (int64, error) {
	size, err := redisdb.RPush(key, value).Result()
	if err != nil {
		return 0, err
	}
	return size, err
}

func LRange(key string, start, stop int64) ([]string, error) {
	sliceCmd, err := redisdb.LRange(key, start, stop).Result()
	if err != nil {
		return nil, err
	}
	return sliceCmd, nil
}

func LLen(key string) (int64, error) {
	sliceCmd, err := redisdb.LLen(key).Result()
	if err != nil {
		return 0, err
	}
	return sliceCmd, nil
}

func Publish(key string, value string) (int64, error) {
	size, err := redisdb.Publish(key, value).Result()
	if err != nil {
		return 0, err
	}
	return size, err
}

func BRPop(key string) (string, error) {
	value, err := redisdb.BRPop(5*time.Second, key).Result()
	if err == redis.Nil {
		// 查询不到数据
		return "", nil
	}
	if len(value) == 2 {
		return value[1], nil
	}
	if err != nil {
		// 查询出错
		return "", err
	}
	return "", errors.New("未知错误")
}

func HSet(key string, field string, value string) (bool, error) {
	result, err := redisdb.HSet(key, field, value).Result()
	if err == redis.Nil {
		return result, nil
	}
	return result, err
}

func HGet(key string, field string) (string, error) {
	value, err := redisdb.HGet(key, field).Result()
	if err == redis.Nil {
		return value, nil
	}
	return value, err
}

func HDel(key string, field string) (int64, error) {
	value, err := redisdb.HDel(key, field).Result()
	if err == redis.Nil {
		return value, nil
	}
	return value, err
}

func HExist(key string, field string) (bool, error) {
	return redisdb.HExists(key, field).Result()
}

func HKeys(key string) ([]string, error) {
	return redisdb.HKeys(key).Result()
}

func HGetAll(key string) (map[string]string, error) {
	return redisdb.HGetAll(key).Result()
}

// 加锁
func Lock(key string, timeoutMillis int64) bool {
	// ex:设置默认过期时间10秒，防止死锁
	//ex := 10 * time.Second
	ex := time.Duration(timeoutMillis) * time.Millisecond
	mutex.Lock()
	defer mutex.Unlock()
	bool, err := redisdb.SetNX(key, `{"lock":1}`, ex).Result()
	if err != nil {
		logger.Error(key, " Lock失败：", err)
		return bool
	}
	//logger.Info("Lock:", bool, "   ", nil, "  end")
	return bool
}

func IsLocked(key string) (bool, time.Duration, error) {
	// 获取锁的过期时间
	ttl, err := redisdb.TTL(key).Result()
	if err != nil {
		return false, 0, err
	}

	if ttl > 0 {
		// 锁还未过期，锁被锁住
		return true, ttl, nil
	}

	// 锁已过期，锁未被锁住
	return false, 0, nil
}

// 解锁
func UnLock(key string) int64 {
	nums, err := redisdb.Del(key).Result()
	if err != nil {
		logger.Error("解锁失败", err)
		return 0
	}
	isLock, timeD, errLock := IsLocked(key)
	logger.Info("UnLock key:", key, "  isLock:", isLock, " timeD:", timeD.Milliseconds(), " errLock:", errLock)
	if errLock == nil && isLock {
		logger.Error("解锁失败", key, "还是为锁住状态,timeD:", timeD.Milliseconds())
	}
	return nums
}

package manage

import (
	"net/http"
	"sheys-ai/middleware"
	"sheys-ai/model"
	"sheys-ai/utils/errmsg"
	"sheys-ai/utils/logger"

	"github.com/gin-gonic/gin"
)

type rechargeQueryReq struct {
	User     uint `json:"user"`
	State    int  `json:"state"`
	Page     int  `json:"page"`
	PageSize int  `json:"page_size"`
}

func GetRechargeList(c *gin.Context) {
	var code int
	var req rechargeQueryReq
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var recharge model.Recharge
	arr, total, err := recharge.GetListByManager(req.User, req.State, req.Page, req.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "充值列表获取失败")
		return
	}
	result := make(map[string]interface{})
	result["recharges"] = arr
	result["total"] = total
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}
}

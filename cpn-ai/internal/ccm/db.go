package ccm

import (
	"errors"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var DB *gorm.DB

const TablePrefix = "ccm"

type Model struct {
	ID        int            `json:"id,omitempty" gorm:"column:id;type:INTEGER;not null;primarykey;autoIncrement"`
	CreatedAt time.Time      `json:"created_at,omitempty" gorm:"column:created_at;type:TIMESTAMP;not null;autoCreateTime"`
	UpdatedAt time.Time      `json:"updated_at,omitempty" gorm:"column:updated_at;type:TIMESTAMP;not null;autoUpdateTime"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"column:deleted_at;type:TIMESTAMP;default:null;index"`
}

// primarykey or uniqueIndex
func GetRow[Model any](c, v any) *Model {
	return FirstRow[Model](DB.Where(Eq(c, v)))
}

func FirstRow[Model any](db *gorm.DB) *Model {
	row := new(Model)
	err := db.First(row).Error
	if err == nil {
		return row
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil
	}
	panic(err)
}

func FindRows[Model any](db *gorm.DB) (rows []*Model) {
	MustNoError(db.Find(&rows).Error)
	return
}

func Eq(c, v any) clause.Eq {
	return clause.Eq{Column: c, Value: v}
}
func Neq(c, v any) clause.Neq {
	return clause.Neq{Column: c, Value: v}
}
func IN[T any](c any, a []T) clause.IN {
	return clause.IN{Column: c, Values: Slice2A(a)}
}

func orderByColumn(c any, desc bool) clause.OrderByColumn {
	var col clause.Column
	if s, ok := c.(string); ok {
		col.Name = s
	} else {
		col, ok = c.(clause.Column)
		Must(ok)
	}
	return clause.OrderByColumn{Column: col, Desc: desc}
}
func Asc(c any) clause.OrderByColumn {
	return orderByColumn(c, false)
}
func Desc(c any) clause.OrderByColumn {
	return orderByColumn(c, true)
}

func NewUuid() string {
	return strings.ReplaceAll(uuid.NewString(), "-", "")
}

package request

type FlashUploadCheckReq struct {
	FileHashHeader string `json:"file_hash_header"`
	FileHashMiddle string `json:"file_hash_middle"`
	FileHashTail   string `json:"file_hash_tail"`
}

type FlashUploadReq struct {
	Filename       string `json:"filename"`
	Path           string `json:"path"`
	FileHashHeader string `json:"file_hash_header"`
	FileHashMiddle string `json:"file_hash_middle"`
	FileHashTail   string `json:"file_hash_tail"`
}

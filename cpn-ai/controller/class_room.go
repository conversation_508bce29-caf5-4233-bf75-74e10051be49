package controller

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"strings"
)

type classRoomApi_ struct {
}

var ClassRoomApi classRoomApi_

type classRoomReq struct {
	ClassRoomUuid string `json:"class_room_uuid"`
	Kw            string `json:"kw"`
	Page          int    `json:"page"`
	PageSize      int    `json:"page_size"`
}

type classRoomResp struct {
	ID            uint              `json:"-"`
	Uuid          string            `json:"uuid"`
	UserId        uint              `json:"-"`
	Title         string            `json:"title"`
	Des           string            `json:"des"`
	Markdown      string            `json:"markdown"`
	Logo          string            `json:"logo"`
	Cover         string            `json:"cover"`
	UserStatus    int               `json:"user_status"`
	UserStatusTxt string            `json:"user_status_txt"`
	Status        int               `json:"status"`
	StatusTxt     string            `json:"status_txt"`
	CreatedAt     jsontime.JsonTime `json:"created_at"`
	UpdatedAt     jsontime.JsonTime `json:"updated_at"`
}

func (obj classRoomApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq classRoomReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page < 1 {
		oReq.Page = 1
	}
	if oReq.PageSize < 1 || oReq.PageSize > 50 {
		oReq.PageSize = 50
	}
	oReq.Page = 1
	oReq.PageSize = 100

	var classRoom model.ClassRoom
	if oReq.ClassRoomUuid != "" {
		if err := classRoom.GetByUuid(oReq.ClassRoomUuid); err != nil {
			msg = "记录不存在"
			logger.Error(msg, err, oReq)
			return
		}
	}

	queryParm := utils.MakeInterfaceMap()
	qParm := make(map[string]interface{})
	qParm["stu_user_id"] = claims.UserId
	qParm["status"] = ">0"

	mapStuUserStatus := make(map[uint]int)
	aryClassUser := make([]model.ClassUser, 0)
	var classUser model.ClassUser
	if _, err := classUser.List(&aryClassUser, qParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(qParm))
		return
	} else {
		classRoomIds := make([]uint, 0)
		for _, tmp := range aryClassUser {
			ary := strings.Split(tmp.ClassRoomIds, "|")
			for _, a := range ary {
				if i := utils.String2Uint(a); i > 0 {
					classRoomIds = append(classRoomIds, i)
				}
			}
			mapStuUserStatus[tmp.UserId] = tmp.Status
		}
		queryParm["class_room_ids"] = classRoomIds
	}
	queryParm["status"] = ">0"
	var ary = make([]classRoomResp, 0)
	if total, err := classRoom.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(ary); i++ {

			if ary[i].Logo != "" {
				ary[i].Logo = fmt.Sprintf("%s%s", config.DiffusionDomain, ary[i].Logo)
			}
			if ary[i].Cover != "" {
				ary[i].Cover = fmt.Sprintf("%s%s", config.DiffusionDomain, ary[i].Cover)
			}

			if tmp, ok := mapStuUserStatus[ary[i].UserId]; ok {
				ary[i].UserStatus = tmp
				ary[i].UserStatusTxt = enums.ClassRoomEnum.StuUserStatusName(tmp)
			}
			ary[i].StatusTxt = enums.ClassRoomEnum.StatusName(ary[i].Status)

		}
		result["class_rooms"] = ary
		result["total"] = total
	}

	msg = ""
	code = 0
}

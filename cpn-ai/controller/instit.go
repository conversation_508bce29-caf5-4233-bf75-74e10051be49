package controller

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"net/http"

	"github.com/gin-gonic/gin"
)

type institApi struct {
}

var InstitApi institApi

type institReq struct {
	InstitUuid string `json:"instit_uuid"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}

type institResp struct {
	ID        uint              `json:"-"`
	Uuid      string            `json:"uuid"`
	UserId    uint              `json:"-"`
	Name      string            `json:"name"`
	Intro     string            `json:"intro"`
	Detail    string            `json:"detail"`
	Logo      string            `json:"logo"`
	Cover     string            `json:"cover"`
	Status    int               `json:"status"`
	StatusTxt string            `json:"status_txt"`
	CreatedAt jsontime.JsonTime `json:"created_at"`
	UpdatedAt jsontime.JsonTime `json:"updated_at"`
}

func (obj institApi) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(model.M)
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var req institReq
	if err := c.ShouldBindJSON(&req); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 100
	}

	queryParams := make(model.M)
	queryParams["status"] = 2

	var instit model.Instit
	if req.InstitUuid != "" {
		if err := instit.GetByUuid(req.InstitUuid); err != nil {
			msg = "记录不存在"
			logger.Error(msg, err, req)
			return
		}
		queryParams["id"] = instit.ID
	}

	var rows = make([]institResp, 0)
	if total, err := instit.List(&rows, queryParams, req.Page, req.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(rows); i++ {
			rows[i].Logo = model.InstitLogoUrl(rows[i].Logo)
			rows[i].Cover = model.InstitCoverUrl(rows[i].Cover)
			rows[i].StatusTxt = enums.InstitStatusTxt[rows[i].Status]
		}

		result["data"] = rows
		result["total"] = total
	}

	msg = ""
	code = 0
}

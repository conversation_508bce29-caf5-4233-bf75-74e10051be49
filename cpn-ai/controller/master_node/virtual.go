package master_node

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
)

type virtualApi_ struct {
}

var VirtualApi virtualApi_

type virtualReq struct {
	VirtualId   uint   `json:"virtual_id"`
	VirtualUuid string `json:"virtual_uuid"`
	GpuModelId  int    `json:"gpu_model_id"`
	Status      int    `json:"status"`
	Page        int    `json:"page"`
	PageSize    int    `json:"page_size"`
}

type reportTimeoutReq struct {
	VirtualId uint  `json:"virtual_id"`
	TimeoutAt int64 `json:"timeout_at"`
}

type reportReq struct {
	VirtualId     uint   `json:"virtual_id"`
	GpuFrees      int    `json:"gpu_frees"`
	TotalGpus     int    `json:"total_gpus"` //[0,0,1,0,1,0]
	TotalInstance int    `json:"total_instance"`
	InitAt        int64  `json:"init_at"`         //初始化开始时间
	LastCheckTime string `json:"last_check_time"` //最后检测时间
	DockerAt      int64  `json:"docker_at"`       //启动docker时间戳,0为没有在启动
	TimeoutAt     int64  `json:"timeout_at"`
}

type virtualResp struct {
	ID            uint              `json:"id"`
	Region        int               `json:"region"`
	Host          string            `json:"host"`
	Port          int               `json:"port"`
	FreeGpus      int               `json:"free_gpus"`
	GpuModelId    int               `json:"gpu_model_id"`
	GpuModelName  string            `json:"gpu_model_name"`
	Status        int               `json:"status"`
	StatusTxt     string            `json:"status_txt"`
	DockerAt      int64             `json:"docker_at"`
	DockerAtTime  jsontime.JsonTime `json:"docker_at_time"`
	LastCheckTime jsontime.JsonTime `json:"lastCheckTime"`
	CreatedAt     jsontime.JsonTime `json:"created_at"`
}

func (obj virtualApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var virtual model.Virtual

	var ary = make([]virtualResp, 0)
	if total, err := virtual.List(&ary, 0, -1, 1, 100); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(ary); i++ {
			if ary[i].Status == 1 {
				ary[i].StatusTxt = "有效"
			} else {
				ary[i].StatusTxt = "无效"
			}
		}
		result["virtuals"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj virtualApi_) Report(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq reportReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var virtual model.Virtual
	if err := virtual.GetById(oReq.VirtualId); err != nil {
		msg = "查询虚拟机出错"
		logger.Error(msg, err, oReq)
		return
	}

	m := map[string]interface{}{"free_gpus": oReq.GpuFrees, "total_gpus": oReq.TotalGpus, "total_instance": oReq.TotalInstance, "last_check_time": time.Now(), "timeout_at": oReq.TimeoutAt, "inited_at": oReq.InitAt, "docker_at": oReq.DockerAt}
	if err := virtual.Updates(m); err != nil {
		msg = "更新上报数据出错"
		logger.Error(msg, err, utils.GetJsonFromStruct(oReq))
		return
	} else {
		logger.Info("更新虚拟机数据完成", utils.GetJsonFromStruct(oReq))
	}
	result["virtual_id"] = oReq.VirtualId
	result["virtual_host"] = virtual.Host
	msg = "上报成功"
	code = 0
}

func (obj virtualApi_) ReportTimeout(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq reportTimeoutReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var virtual model.Virtual
	if err := virtual.GetById(oReq.VirtualId); err != nil {
		msg = "查询虚拟机出错"
		logger.Error(msg, err, oReq)
		return
	}
	if err := virtual.SetTimeoutAt(oReq.TimeoutAt); err != nil {
		msg = "更新超时时间出错"
		logger.Error(msg, err, oReq)
		return
	}
	msg = "上报成功"
	code = 0
}

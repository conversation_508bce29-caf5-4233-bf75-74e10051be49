package kol

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/myimg"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"cpn-ai/service/tasklog"
	"cpn-ai/structs"
	"fmt"
	"github.com/shopspring/decimal"
	"net/http"
	"os"
	"path"
	"runtime"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type podApi_ struct {
}

var PodApi podApi_

type savePodReq struct {
	PodUuid       string `json:"pod_uuid"`
	PodCategory   int    `json:"pod_category"`
	ClassType     int    `json:"class_type"`
	Title         string `json:"title"`
	PodName       string `json:"pod_name"`
	Desc          string `json:"desc"`
	NeedGpus      int    `json:"need_gpus"`
	StartupElapse int    `json:"startup_elapse"`
	Price         string `json:"price"`
	PortMaps      string `json:"port_maps"`
	ImageName     string `json:"image_name"`
	ImageTag      string `json:"image_tag"`
	Command       string `json:"command"`
	UserId        uint   `json:"user_id"`
	Remark        string `json:"remark"`
	Status        int    `json:"status"`
	Suggest       int    `json:"suggest"`
	Popular       int    `json:"popular"`
	Insider       int    `json:"insider"`
	CoverBase64   string `json:"cover_base64"`
	LogoBase64    string `json:"logo_base64"`
}

type applyPodReq struct {
	PodUuid                string             `json:"pod_uuid"`
	ClassType              int                `json:"class_type"`
	Title                  string             `json:"title"`
	Desc                   string             `json:"desc"`
	Markdown               string             `json:"markdown"`
	NeedMemory             int                `json:"need_memory"`
	NeedGpus               int                `json:"need_gpus"`
	StartupElapse          int                `json:"startup_elapse"`
	Price                  string             `json:"price"`
	PriceHour              decimal.Decimal    `json:"price_hour"`
	PortMaps               string             `json:"port_maps"`
	CoverBase64            string             `json:"cover_base64"`
	LogoBase64             string             `json:"logo_base64"`
	Catalogue              string             `json:"catalogue"`
	ShareInstanceInputDir  enums.DirShareType `json:"share_instance_input_dir"`
	ShareInstanceOutputDir enums.DirShareType `json:"share_instance_output_dir"`
}

type podReq struct {
	PodId       uint   `json:"pod_id"`
	PodUuid     string `json:"pod_uuid"`
	PodCategory int    `json:"pod_category"`
	Status      int    `json:"status"`
	AuditStatus int    `json:"audit_status"`
	Suggest     int    `json:"suggest"`
	Popular     int    `json:"popular"`
	Insider     int    `json:"insider"`
	Kw          string `json:"kw"`
	Page        int    `json:"page"`
	PageSize    int    `json:"page_size"`
}

type auditReq struct {
	PodUuid      string `json:"pod_uuid"`
	RejectReason string `json:"reject_reason"`
}

type podResp struct {
	ID                     uint                        `json:"-"`
	Uuid                   string                      `json:"uuid"`
	Title                  string                      `json:"title"`
	ClassType              int                         `json:"class_type"`
	Desc                   string                      `json:"desc"`
	Markdown               string                      `json:"markdown"`
	Logo                   string                      `json:"logo"`
	Cover                  string                      `json:"cover"`
	Price                  string                      `json:"-"`
	PriceHour              decimal.Decimal             `json:"price_hour"`
	PortMaps               string                      `json:"-"`
	PortMapsMap            []map[string]interface{}    `json:"port_maps"`
	Catalogue              string                      `json:"catalogue"`
	Catalogues             []service.CatalogueListItem `json:"catalogues" gorm:"-"`
	ImageName              string                      `json:"image_name"`
	ImageTag               string                      `json:"image_tag"`
	ImageTags              string                      `json:"image_tags"`
	NeedGpus               int                         `json:"need_gpus"`
	NeedMemory             int                         `json:"need_memory"`
	StartupElapse          uint                        `json:"startup_elapse"`
	Status                 int                         `json:"status"`
	StatusTxt              string                      `json:"status_txt"`
	AuditStatus            int                         `json:"audit_status"`
	AuditStatusTxt         string                      `json:"audit_status_txt"`
	AuditContent           string                      `json:"-"`
	AuditContentMap        map[string]interface{}      `json:"audit_content"`
	InstanceUuid           string                      `json:"instance_uuid"`
	Instance               map[string]interface{}      `json:"instance"`
	Suggest                int                         `json:"suggest"`
	Popular                int                         `json:"popular"`
	AccessCount            int                         `json:"access_count"`
	UsageCount             int                         `json:"usage_count"`
	UsageDuration          uint                        `json:"usage_duration"`
	CreatedAt              jsontime.JsonTime           `json:"created_at"`
	UpdatedAt              jsontime.JsonTime           `json:"updated_at"`
	ShareInstanceInputDir  enums.DirShareType          `json:"share_instance_input_dir"`
	ShareInstanceOutputDir enums.DirShareType          `json:"share_instance_output_dir"`
}

func (obj podApi_) Apply(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		msg = "获取用户信息失败"
		logger.Error(err)
		return
	}
	if user.UserType != enums.UserTypeEnum.Kol {
		msg = "不是KOL用户，请联系申请成为KOL"
		return
	}

	var oReq applyPodReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.PodUuid == "" {
		if oReq.Title == "" {
			msg = "请输入标题"
			return
		}
		if oReq.Desc == "" {
			msg = "请输入简介"
			return
		}
	}

	if oReq.NeedMemory <= 0 || oReq.NeedMemory > 48 {
		msg = "最低单卡显存在8-48之间"
		return
	}

	if oReq.NeedGpus <= 0 || oReq.NeedGpus > 2 {
		msg = "显卡数量请选择1-2张"
		return
	}

	if oReq.PriceHour.GreaterThanOrEqual(decimal.NewFromInt(10)) {
		msg = "Pod服务费单价不能大于10元"
		return
	}

	if oReq.StartupElapse <= 0 {
		oReq.StartupElapse = 300
	}
	if oReq.PortMaps == "" {
		oReq.PortMaps = "[]"
	}

	newPortMapStr := ""
	if oReq.PortMaps != "" { //webui 88 jupyter 89 api 33
		//[{"title":"webui","container_port":"7860"},{"title":"jupyter","container_port":"7888"},{"title":"api","container_port":"32323"},{"title":"webui2","container_port":"7877"}]
		ary := utils.GetMapAryFromJson(oReq.PortMaps)
		if ary == nil || len(ary) == 0 {
			msg = "请检查映射端口"
			return
		}
		pass := false
		portIndex := 70
		newAry := make([]map[string]interface{}, 0)
		logger.Info("oReq.PortMaps:", oReq.PortMaps)
		logger.Info("newAry:", utils.GetJsonFromStruct(ary))
		for i := 0; i < len(ary); i++ {
			if ary[i]["container_port"].(string) == "" {
				continue
			}
			if utils.String2Int(ary[i]["container_port"].(string)) == 0 {
				msg = ary[i]["title"].(string) + "映射端口不正确"
				return
			}
			if ary[i]["title"].(string) == "webui" {
				ary[i]["host_port"] = "88"
				pass = true
			} else if ary[i]["title"].(string) == "jupyter" {
				ary[i]["host_port"] = "89"
				pass = true
			} else if ary[i]["title"].(string) == "webos" {
				ary[i]["host_port"] = "87"
				pass = true
			} else if ary[i]["title"].(string) == "api" {
				ary[i]["host_port"] = "33"
				pass = true
			} else {
				portIndex++
				ary[i]["host_port"] = fmt.Sprintf("%d", portIndex)
			}
			newAry = append(newAry, ary[i])
		}
		if pass == false {
			msg = "webui api端口至少设置一个"
			return
		}
		newPortMapStr = utils.GetJsonFromStruct(newAry)
		if newPortMapStr == "" {
			msg = "请检查映射端口1"
			logger.Error(newAry)
			return
		}
	}

	if newPortMapStr == "" {
		msg = "请输入映射端口"
		return
	}

	//podAudit := structs.PodAudit{Title: oReq.Title, Desc: oReq.Desc}
	var podAuditContent structs.PodAuditContent
	var pod model.Pod

	count := pod.CountByUserId(claims.UserId)
	if count < 0 {
		msg = "查询出错"
		logger.Error(msg, "userId:", claims.UserId)
		return
	}

	if count > 5000 {
		msg = "单人Pod制作超过限制5000"
		return
	}

	if oReq.PodUuid != "" {
		if err := pod.GetByUuid(oReq.PodUuid); err != nil {
			msg = "pod信息获取失败"
			logger.Error(msg, err)
			return
		}
		if pod.AuditContent != "" {
			if err := utils.GetStructFromJson(&podAuditContent, pod.AuditContent); err != nil {
				logger.Error(err)
			}
		}
	}

	if oReq.Title != "" && oReq.Title != pod.Title {
		podAuditContent.Title = oReq.Title
	}
	if oReq.Desc != "" && oReq.Desc != pod.Desc {
		podAuditContent.Desc = oReq.Desc
	}

	if oReq.Markdown != "" && oReq.Markdown != pod.Markdown {
		podAuditContent.Markdown = oReq.Markdown
	}

	//podAuditContent.NeedMemory = oReq.NeedMemory
	//podAuditContent.NeedGpus = oReq.NeedGpus
	//podAuditContent.StartupElapse = oReq.StartupElapse

	logoDeletePath := ""
	coverDeletePath := ""
	if oReq.LogoBase64 != "" {
		logger.Info(pod.ID, "开始更新logo图")
		//oMd5Str := fmt.Sprintf("%d,%s", pod.ID, pod.CreatedAt.Format("2006-01-02 15:04:05.000"))

		logoUuid := utils.GetUUID()
		pathImage := "cpn/pod_logo/" + logoUuid + ".jpg"

		absolutePath := path.Join(config.DiffusionFilePath, pathImage)
		//directory := filepath.Dir(absolutePath) // 获取目录路径
		//// 创建目录，存在则不创建，不存在则创建
		//if err := os.MkdirAll(directory, 0755); err != nil {
		//	msg = "目录创建失败"
		//	logger.Error(msg, err, "directory:", directory, "    absolutePath:", absolutePath)
		//	return
		//}

		if img, err := myimg.Base64ToImg(oReq.LogoBase64); err != nil {
			//logger.Error(err, oReq.CoverBase64)
			msg = "base64转图片失败"
			logger.Error(msg, err)
			return
		} else {
			logger.Info(pod.ID, "   x:", img.Bounds().Size().X, "   y:", img.Bounds().Size().Y)
			x := img.Bounds().Size().X
			y := img.Bounds().Size().Y
			if x != y {
				msg = "Logo图片尺寸为1:1"
			} else {
				small := myimg.ResizeImg(180, 180, img, true)
				if err := myimg.ImgToFile(small, absolutePath); err != nil {
					msg = "缩略图保存失败"
					logger.Error(msg, err)
				} else {
					logger.Info(pod.ID, "  缩略图生成成功")
					logoDeletePath = podAuditContent.Logo
					podAuditContent.Logo = pathImage
				}
			}
		}

	}

	if oReq.CoverBase64 != "" {
		logger.Info(pod.ID, "开始更新封面图")
		//oMd5Str := fmt.Sprintf("%d,%s", pod.ID, pod.CreatedAt.Format("2006-01-02 15:04:05.000"))
		converUuid := utils.GetUUID()
		pathImage := "cpn/pod_logo/" + converUuid + "_cover.jpg"

		absolutePath := path.Join(config.DiffusionFilePath, pathImage)
		//directory := filepath.Dir(absolutePath) // 获取目录路径
		//// 创建目录，存在则不创建，不存在则创建
		//if err := os.MkdirAll(directory, 0755); err != nil {
		//	msg = "目录创建失败"
		//	logger.Error(msg, err, "directory:", directory, "    absolutePath:", absolutePath)
		//	return
		//}

		if img, err := myimg.Base64ToImg(oReq.CoverBase64); err != nil {
			//logger.Error(err, oReq.CoverBase64)
			msg = "base64转图片失败"
			logger.Error(msg, err)
			return
		} else {

			x := float64(img.Bounds().Size().X)
			y := float64(img.Bounds().Size().Y)
			if x/y < 1.51 {
				msg = "封面图片需要是长方形的"
				logger.Error(msg, "  x:", x, "   y:", y, "   x/y:", x/y)
			} else {
				if err := myimg.ImgToFile(img, absolutePath); err != nil {
					msg = "封面图保存失败"
					logger.Error(msg, err)
				} else {
					logger.Info(pod.ID, "  封面图生成成功")
					coverDeletePath = podAuditContent.Cover
					podAuditContent.Cover = pathImage
				}
			}
		}

	}

	podAuditContentStr := utils.GetJsonFromStruct(podAuditContent)
	if podAuditContentStr == "" {
		msg = "保存审核数据失败"
		logger.Error(msg, podAuditContentStr)
		return
	}

	if pod.ID == 0 {
		pod.Uuid = utils.GetUUID()
		pod.Category = enums.PodCategoryEnum.PodInstance
		pod.ClassType = oReq.ClassType
		pod.AuthorName = user.DisplayName
		pod.UserId = claims.UserId
		pod.Price = `{"hour":0,"day":0,"week":0,"month":0}`

		if oReq.PriceHour.GreaterThan(decimal.Zero) {
			hour, _ := oReq.PriceHour.Round(2).Float64()
			day, _ := oReq.PriceHour.Mul(decimal.NewFromInt(24)).Round(2).Float64()
			week, _ := oReq.PriceHour.Mul(decimal.NewFromInt(24 * 7)).Round(2).Float64()
			month, _ := oReq.PriceHour.Mul(decimal.NewFromInt(24 * 30)).Round(2).Float64()
			mPrice := map[string]float64{
				"hour":  hour,
				"day":   day,
				"week":  week,
				"month": month,
			}
			pod.Price = utils.GetJsonFromStruct(mPrice)
		}

		pod.PortMaps = newPortMapStr
		pod.NeedMemory = oReq.NeedMemory
		pod.NeedGpus = oReq.NeedGpus
		pod.StartupElapse = oReq.StartupElapse
		pod.AuditContent = podAuditContentStr
		pod.AuditStatus = enums.PodAuditStatusEnum.Makeing

		ary := strings.Split(oReq.Catalogue, ",")
		ary1 := make([]string, 0)
		for _, tmp := range ary {
			if service.UserCatalogue.GetListItem(tmp) != nil {
				ary1 = append(ary1, tmp)
				switch tmp {
				case service.DirKeyComfyAppdataInput, service.DirKeySdAppdataInput, service.DirKeyForgeAppdataInput:
					if oReq.ShareInstanceInputDir != enums.DirShareStatusShare {
						ary1 = append(ary1, service.DirKeyUnShareInput)
					}
				case service.DirKeyComfyAppdataOutput, service.DirKeySdAppdataOutput, service.DirKeyForgeAppdataOutput:
					if oReq.ShareInstanceOutputDir != enums.DirShareStatusShare {
						ary1 = append(ary1, service.DirKeyUnShareOutput)
					}
				}
			}
		}
		pod.Catalogue = strings.Join(ary1, ",")

		if err := pod.Save(); err != nil {
			msg = "保存失败"
			logger.Error(msg, err, oReq)
			return
		}
	} else {
		if pod.UserId != claims.UserId {
			msg = "无权限"
			return
		}
		mm := make(map[string]interface{})

		if newPortMapStr != pod.PortMaps {
			mm["port_maps"] = newPortMapStr
		}

		if oReq.ClassType != pod.ClassType {
			mm["class_type"] = oReq.ClassType
		}

		if user.DisplayName != "" && user.DisplayName != pod.AuthorName {
			mm["author_name"] = user.DisplayName
		}

		if oReq.NeedMemory != pod.NeedMemory {
			mm["need_memory"] = oReq.NeedMemory
		}

		if oReq.NeedGpus != pod.NeedGpus {
			mm["need_gpus"] = oReq.NeedGpus
		}

		if oReq.StartupElapse != pod.StartupElapse {
			mm["startup_elapse"] = oReq.StartupElapse
		}

		if pod.AuditStatus != enums.PodAuditStatusEnum.Makeing {
			mm["audit_status"] = enums.PodAuditStatusEnum.Makeing
		}

		if pod.AuditContent != podAuditContentStr {
			mm["audit_content"] = podAuditContentStr
		}
		//if pod.Price == "" {
		//	mm["price"] = `{"hour":0,"day":0,"week":0,"month":0}`
		//}
		if oReq.PriceHour.GreaterThan(decimal.Zero) {
			hour, _ := oReq.PriceHour.Round(2).Float64()
			day, _ := oReq.PriceHour.Mul(decimal.NewFromInt(24)).Round(2).Float64()
			week, _ := oReq.PriceHour.Mul(decimal.NewFromInt(24 * 7)).Round(2).Float64()
			month, _ := oReq.PriceHour.Mul(decimal.NewFromInt(24 * 30)).Round(2).Float64()
			mPrice := map[string]float64{
				"hour":  hour,
				"day":   day,
				"week":  week,
				"month": month,
			}
			mm["price"] = utils.GetJsonFromStruct(mPrice)
		} else {
			mm["price"] = `{"hour":0,"day":0,"week":0,"month":0}`
		}

		ary := strings.Split(oReq.Catalogue, ",")
		ary1 := make([]string, 0)
		for _, tmp := range ary {
			if service.UserCatalogue.GetListItem(tmp) != nil {
				ary1 = append(ary1, tmp)
				switch tmp {
				case service.DirKeyComfyAppdataInput, service.DirKeySdAppdataInput, service.DirKeyForgeAppdataInput:
					if oReq.ShareInstanceInputDir != enums.DirShareStatusShare {
						ary1 = append(ary1, service.DirKeyUnShareInput)
					}
				case service.DirKeyComfyAppdataOutput, service.DirKeySdAppdataOutput, service.DirKeyForgeAppdataOutput:
					if oReq.ShareInstanceOutputDir != enums.DirShareStatusShare {
						ary1 = append(ary1, service.DirKeyUnShareOutput)
					}
				}
			}
		}
		mm["catalogue"] = strings.Join(ary1, ",")

		if err := pod.Updates(mm); err != nil {
			msg = "保存失败"
			logger.Error(msg, err, oReq)
			return
		}
	}

	//if err := pod.SetAuditContent(str); err != nil {
	//	msg = "更新数据失败"
	//	logger.Error(msg, err)
	//	return
	//}

	if logoDeletePath != "" && logoDeletePath != pod.Logo {
		absolutePath := path.Join(config.DiffusionFilePath, logoDeletePath)
		if err := os.Remove(absolutePath); err != nil {
			logger.Error(err, absolutePath)
		}
	}

	if coverDeletePath != "" && coverDeletePath != pod.Cover {
		absolutePath := path.Join(config.DiffusionFilePath, coverDeletePath)
		if err := os.Remove(absolutePath); err != nil {
			logger.Error(err, absolutePath)
		}
	}
	result["pod_uuid"] = pod.Uuid
	msg = "保存成功 " + msg
	code = 0
}

func (obj podApi_) Auditing(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq auditReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.PodUuid == "" {
		msg = "参数错误"
		return
	}

	var pod model.Pod
	if err := pod.GetByUuid(oReq.PodUuid); err != nil {
		msg = "pod信息获取失败"
		logger.Error(msg, err, oReq)
		return
	}

	if pod.AuditStatus == enums.PodAuditStatusEnum.AuditPass {
		if err := pod.SetAuditStatus(enums.PodAuditStatusEnum.Auditing); err != nil {
			msg = "设置审核状态失败"
			logger.Error(msg, err)
			return
		} else {
			code = 0
			msg = "审核申请已提交，请等待审核"
			return
		}
		msg = "审核已通过，请勿重复操作"
		return
	}

	if pod.AuditStatus == enums.PodAuditStatusEnum.Auditing {
		msg = "审核申请已提交，请勿重复操作"
		return
	}

	if pod.AuditStatus != enums.PodAuditStatusEnum.Makeing {
		msg = "审核状态不正确"
		logger.Error(msg, oReq, "  pod.AuditStatus：", pod.AuditStatus)
		return
	}

	if err := pod.SetAuditStatus(enums.PodAuditStatusEnum.Auditing); err != nil {
		msg = "设置审核状态失败"
		logger.Error(msg, err)
		return
	} else {
		code = 0
		msg = "审核申请已提交，请等待审核"
		return
	}
}

func (obj podApi_) Audited(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq auditReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.PodUuid == "" {
		msg = "参数错误"
		return
	}
	if oReq.RejectReason == "" {
		msg = "请输入审核内容"
		return
	}

	var pod model.Pod
	if err := pod.GetByUuid(oReq.PodUuid); err != nil {
		msg = "pod信息获取失败"
		logger.Error(msg, err, oReq)
		return
	}
	if pod.AuditStatus != enums.PodAuditStatusEnum.Auditing {
		msg = "未提交审核申请"
		logger.Error(msg, oReq, "  pod.AuditStatus：", pod.AuditStatus)
		return
	}

	var podAuditContent structs.PodAuditContent
	if err := utils.GetStructFromJson(&podAuditContent, pod.AuditContent); err != nil {
		msg = "获取修改信息失败"
		logger.Error(msg, err, oReq)
		return
	}

	if oReq.RejectReason != "审核通过" {
		podAuditContent.RejectReason = oReq.RejectReason
		str := utils.GetJsonFromStruct(podAuditContent)
		if str == "" {
			msg = "序列化审核数据失败"
			logger.Error(msg, str)
			return
		}
		if err := pod.SetAuditContent(str); err != nil {
			msg = "保存审核结果失败"
			logger.Error(msg, err)
			return
		}

		//var podImage model.PodImage
		//
		//queryParm := make(map[string]interface{})
		//queryParm["user_id"] = pod.UserId
		//queryParm["pod_id"] = pod.ID
		//queryParm["audit_status"] = enums.PodAuditStatusEnum.Auditing
		//
		//var ary = make([]model.PodImage, 0)
		//if _, err := podImage.ListPro(&ary, queryParm, 1, 100); err != nil {
		//	msg = "查询失败"
		//	return
		//} else {
		//	if len(ary) > 0 && ary[0].UserId > 0 {
		//		if err := ary[0].SetAuditPass(); err != nil {
		//			logger.Error(err)
		//			msg = "更新镜像状态失败"
		//			return
		//		}
		//	}
		//}

		if err := pod.SetAuditStatus(enums.PodAuditStatusEnum.AuditReject); err != nil {
			msg = "设置审核状态失败"
			logger.Error(msg, err)
			return
		} else {
			code = 0
			msg = "审核驳回完成"
			return
		}
	}

	pod.AuditContent = "{}"
	logJson := utils.GetJsonFromStruct(pod)
	operationLog := model.OperationLog{
		OperatorUserId: claims.UserId,
		LogType:        enums.OperationLogTypeEnum.ModifySdParm,
		OrigWhere:      enums.OperationOrigWhereEnum.Pod,
		OrigId:         pod.ID,
		Ip:             utils.GetClientIp(c.Request.Header),
		LogJson:        logJson,
		UserEnv:        "{}",
	}
	if err := operationLog.Save(); err != nil {
		msg = "备份信息失败"
		logger.Error(msg, err)
		return
	}
	//pod.AuditStatus = enums.PodAuditStatusEnum.AuditPass

	mm := make(map[string]interface{})
	if podAuditContent.Title != "" && podAuditContent.Title != pod.Title {
		mm["title"] = podAuditContent.Title
	}

	if podAuditContent.Desc != "" && podAuditContent.Desc != pod.Desc {
		mm["desc"] = podAuditContent.Desc
	}

	//logger.Info("markdown:", podAuditContent.Markdown, "    ", utils.GetJsonFromStruct(podAuditContent))
	//logger.Info("pod.Markdown:", pod.Markdown, "    ", utils.GetJsonFromStruct(podAuditContent))
	if podAuditContent.Markdown != "" && podAuditContent.Markdown != pod.Markdown {
		mm["markdown"] = podAuditContent.Markdown
	}

	if podAuditContent.Cover != "" && podAuditContent.Cover != pod.Cover {
		mm["cover"] = podAuditContent.Cover
	}

	if podAuditContent.Logo != "" && podAuditContent.Logo != pod.Logo {
		mm["logo"] = podAuditContent.Logo
	}

	mm["audit_content"] = "{}"
	mm["audit_status"] = enums.PodAuditStatusEnum.AuditPass

	var podImage model.PodImage

	{
		queryParm := make(map[string]interface{})
		queryParm["image_type"] = enums.ImageTypeEnum.Public
		queryParm["pod_id"] = pod.ID
		//queryParm["user_id"] = pod.UserId
		queryParm["audit_status"] = enums.ImageAuditStatusEnum.PushSuccess

		var ary = make([]model.PodImage, 0)
		if _, err := podImage.ListPro(&ary, queryParm, 1, 100); err != nil {
			msg = "查询失败"
			logger.Error(msg, err)
			return
		} else {
			if len(ary) > 0 && ary[0].UserId > 0 {
				if err := ary[0].SetAuditPass(); err != nil {
					logger.Error(err)
					msg = "更新镜像状态失败"
					return
				}
			}
		}
	}

	{
		queryParm := make(map[string]interface{})
		//queryParm["user_id"] = pod.UserId
		queryParm["image_type"] = enums.ImageTypeEnum.Public
		queryParm["pod_id"] = pod.ID
		queryParm["status"] = 1
		queryParm["audit_status"] = enums.ImageAuditStatusEnum.AuditPass
		var aryPass = make([]model.PodImage, 0)
		if _, err := podImage.ListPro(&aryPass, queryParm, 1, 100); err != nil {
			msg = "查询失败"
			logger.Error(msg, err)
			return
		} else {
			tags := ""
			for _, tmp := range aryPass {
				tags += tmp.ImageTag + " "
			}
			tags = strings.Trim(tags, " ")
			mm["image_tags"] = tags
		}
	}

	logoDeletePath := ""
	coverDeletePath := ""
	if podAuditContent.Logo != "" && podAuditContent.Logo != pod.Logo {
		logoDeletePath = pod.Logo
	}
	if podAuditContent.Cover != "" && podAuditContent.Cover != pod.Cover {
		coverDeletePath = pod.Cover
	}
	if err := pod.Updates(mm); err != nil {
		msg = "保存失败"
		logger.Error(msg, err, oReq, "  ", utils.GetJsonFromStruct(mm))
		return
	}

	if logoDeletePath != "" {
		absolutePath := path.Join(config.DiffusionFilePath, logoDeletePath)
		if err := os.Remove(absolutePath); err != nil {
			logger.Error(err, absolutePath)
		}
	}

	if coverDeletePath != "" {
		absolutePath := path.Join(config.DiffusionFilePath, coverDeletePath)
		if err := os.Remove(absolutePath); err != nil {
			logger.Error(err, absolutePath)
		}
	}
	msg = "审核通过"
	code = 0
}

func (obj podApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq podReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	oReq.PodCategory = enums.PodCategoryEnum.PodInstance
	oReq.Suggest = 0
	oReq.Popular = 0
	oReq.Insider = 0
	if oReq.Page < 1 {
		oReq.Page = 1
	}
	if oReq.PageSize < 1 || oReq.PageSize > 50 {
		oReq.PageSize = 50
	}

	var pod model.Pod
	if oReq.PodUuid != "" {
		if err := pod.GetByUuid(oReq.PodUuid); err != nil {
			msg = "记录不存在"
			logger.Error(msg, err, oReq)
			return
		}
	}

	var ary = make([]podResp, 0)
	if total, err := pod.List(&ary, pod.ID, claims.UserId, oReq.PodCategory, oReq.Suggest, oReq.Popular, oReq.Insider, oReq.Kw, oReq.Status, oReq.AuditStatus, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(ary); i++ {
			//if ary[i].AuditStatus != enums.PodAuditStatusEnum.AuditPass {
			//	var auditContent structs.PodAuditContent
			//	if err := utils.GetStructAryFromJson(&auditContent, ary[i].AuditContent); err != nil {
			//		logger.Error(err)
			//	}
			//	if err := utils.Scan(auditContent, &ary[i]); err != nil {
			//		logger.Error(err)
			//	}
			//}
			pricePod := model.Pod{Price: ary[i].Price}
			ary[i].PriceHour = pricePod.UnitPrice(enums.ChargingTypeEnum.Usage)

			if ary[i].AuditContent != "" {
				mm := utils.GetMapFromJson(ary[i].AuditContent)
				if mm != nil {
					if _, ok := mm["logo"]; ok {
						tmp := mm["logo"].(string)
						if tmp != "" {
							mm["logo"] = config.DiffusionDomain + tmp
						}
					}
					if _, ok := mm["cover"]; ok {
						tmp := mm["cover"].(string)
						if tmp != "" {
							mm["cover"] = config.DiffusionDomain + tmp
						}
					}
					if _, ok := mm["title"]; ok {
						tmp := mm["title"].(string)
						if tmp != "" && ary[i].Title == "" {
							ary[i].Title = tmp
						}
					}
					if _, ok := mm["desc"]; ok {
						tmp := mm["desc"].(string)
						if tmp != "" && ary[i].Desc == "" {
							ary[i].Desc = tmp
						}
					}
				}
				ary[i].AuditContentMap = mm
			}

			if ary[i].InstanceUuid != "" {
				var instance model.Instance
				setEmpty := false
				if err := instance.GetByUuid(ary[i].InstanceUuid); err != nil {
					if err == gorm.ErrRecordNotFound {
						setEmpty = true
					}
				} else {
					if instance.Status == enums.InstanceStatusEnum.ShutdownComplete || instance.Status == enums.InstanceStatusEnum.Hidden {
						setEmpty = true
					}
					if instance.Status == enums.InstanceStatusEnum.BootInProgress || instance.Status == enums.InstanceStatusEnum.Running || instance.Status == enums.InstanceStatusEnum.ShutdownInProgress {
						var podImage model.PodImage
						if err := podImage.GetById(instance.ImageId); err != nil {
							logger.Error(err)
						} else {

						}
						mm := make(map[string]interface{})

						mm["no_card"] = instance.NoCard

						imageName := ""
						if ary[i].ID == instance.PodId {
							imageName = ary[i].Title
						} else {
							imageName = podImage.ImageName
						}
						//mm["image_tag"] = instance.ImageTag

						if instance.ImageType == enums.ImageTypeEnum.Base {
							mm["image_txt"] = instance.ImageName + ":" + instance.ImageTag + "(基础镜像)"
						} else if instance.ImageType == enums.ImageTypeEnum.Private {
							mm["image_txt"] = imageName + ":" + instance.ImageTag + "(私有)"
						} else if instance.ImageType == enums.ImageTypeEnum.Public {
							mm["image_txt"] = imageName + ":" + instance.ImageTag + "(Pod)"
						}

						//mm["image_tag"] = instance.ImageTag
						mm["status"] = instance.Status
						mm["status_txt"] = enums.InstanceStatusEnum.Name(instance.Status)
						mm["startup_time"] = instance.StartupTime.Format(jsontime.TimeFormat)

						if service.GetSiteDomain(c.Request.Referer()) == "chenyu.cn" {
							instance.StartupMaps = strings.Replace(instance.StartupMaps, "suanyun.cn", "chenyu.cn", -1)
						}

						if instance.StartupMaps != "" {
							maps := utils.GetMapAryFromJson(instance.StartupMaps)
							if maps != nil {
								mm["startup_maps"] = maps
							} else {
								logger.Error("instance.StartupMaps:", instance.StartupMaps, " uuid:", instance.Uuid)
							}

						}

						if instance.Status == enums.InstanceStatusEnum.BootInProgress {
							if instance.StartupMark != "" {
								//if mmm, err := service.StartupLog.Last(instance.StartupMark); err != nil {
								//	logger.Error(err)
								//} else if _, ok := mmm["msg"]; ok {
								//	mm["startup_txt"] = mmm["msg"].(string)
								//	mm["startup_log_time"] = mmm["time"].(string)
								//}

								if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, instance.StartupMark); err != nil {
									logger.Error(err)
								} else {
									if item, _, err := tasklog.Last(logKey); err != nil {
										if err != gorm.ErrEmptySlice {
											logger.Error(err, "StartupMark: ", instance.StartupMark)
										}
									} else {
										mm["startup_txt"] = item.Msg
										if item.Percent > 0 && item.Percent <= 0.9999 {
											mm["startup_txt"] = fmt.Sprintf("%s%.2f%%", item.Msg, item.Percent*100)
										}
										mm["startup_log_time"] = item.Time.String()
									}
								}

							}
						}
						ary[i].Instance = mm
					} else {
						ary[i].InstanceUuid = ""
					}
				}
				if setEmpty {
					var tmpPod model.Pod
					if err := tmpPod.SetInstanceUuidEmpty(ary[i].ID, ary[i].InstanceUuid); err != nil {
						logger.Error(err)
					}
				}
			}

			if ary[i].PortMaps != "" {
				ary[i].PortMapsMap = utils.GetMapAryFromJson(ary[i].PortMaps)
			}

			tmpAry1 := make([]service.CatalogueListItem, 0)
			ary[i].ShareInstanceInputDir = enums.DirShareStatusShare
			ary[i].ShareInstanceOutputDir = enums.DirShareStatusShare
			if ary[i].Catalogue != "" {
				tmpAry := strings.Split(ary[i].Catalogue, ",")
				for _, val := range tmpAry {
					if aa := service.UserCatalogue.GetListItem(val); aa != nil {
						if power, err := service.UserCatalogue.GetNeedPrivilege(val); err != nil {
							logger.Error(err)
						} else {
							if power == enums.CataLoguePrivilegeEnum.USER {
								tmpAry1 = append(tmpAry1, *aa)
							} else if power == enums.CataLoguePrivilegeEnum.KOL && claims.UserId == pod.UserId {
								tmpAry1 = append(tmpAry1, *aa)
							}
						}
					}
					// 设置输入/输出目录共享状态
					if val == service.DirKeyUnShareInput {
						ary[i].ShareInstanceInputDir = enums.DirShareStatusUnShare
					}
					if val == service.DirKeyUnShareOutput {
						ary[i].ShareInstanceOutputDir = enums.DirShareStatusUnShare
					}
				}
				sort.Slice(tmpAry1, func(i, j int) bool {
					return tmpAry1[i].Order < tmpAry1[j].Order
				})

			}
			ary[i].Catalogues = tmpAry1

			ary[i].AuditStatusTxt = enums.PodAuditStatusEnum.Name(ary[i].AuditStatus)

			if ary[i].Status == 1 {
				ary[i].StatusTxt = "已上架"
			} else {
				ary[i].StatusTxt = "未上架"
			}

			if ary[i].Logo != "" {
				ary[i].Logo = fmt.Sprintf("%s%s", config.DiffusionDomain, ary[i].Logo)
			}
			if ary[i].Cover != "" {
				ary[i].Cover = fmt.Sprintf("%s%s", config.DiffusionDomain, ary[i].Cover)
			}
		}
		result["pods"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj podApi_) Catalogues(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	aryComfy := make([]service.CatalogueListItem, 0)
	arySd := make([]service.CatalogueListItem, 0)
	aryForge := make([]service.CatalogueListItem, 0)
	for key, val := range service.CatalogueMap {
		aa := service.CatalogueListItem{
			Key: key,
			CatalogueItem: service.CatalogueItem{ // 将 CatalogueItem 字段嵌入
				Title: val.Title,
				Path:  val.Path,
				Cat:   val.Cat,
				Group: val.Group,
				Order: val.Order,
			},
		}
		if aa.Cat == "comfy" {
			aryComfy = append(aryComfy, aa)
		} else if aa.Cat == "sd" {
			arySd = append(arySd, aa)
		} else if aa.Cat == "forge" {
			aryForge = append(aryForge, aa)
		}
	}

	sort.Slice(aryComfy, func(i, j int) bool {
		return aryComfy[i].Order < aryComfy[j].Order
	})
	sort.Slice(arySd, func(i, j int) bool {
		return arySd[i].Order < arySd[j].Order
	})
	sort.Slice(aryForge, func(i, j int) bool {
		return aryForge[i].Order < aryForge[j].Order
	})

	result["comfy"] = aryComfy
	result["sd"] = arySd
	result["forge"] = aryForge
	code = 0

}

func (obj podApi_) OnSale(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq podReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.PodUuid == "" {
		msg = "参数错误"
		return
	}

	var pod model.Pod
	if err := pod.GetByUuid(oReq.PodUuid); err != nil {
		msg = "pod信息获取失败"
		logger.Error(msg, err, oReq)
		return
	}
	if pod.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	if pod.Status == 1 {
		msg = "已经是上架状态"
		return
	}

	if pod.AuditStatus != enums.PodAuditStatusEnum.AuditPass {
		msg = "只有审核通过才能进行此操作"
		return
	}

	if pod.ImageTags == "" {
		msg = "无可用的镜像版本，请先制作"
		return
	}

	if err := pod.SetStatus(1); err != nil {
		msg = "上架失败"
		logger.Error(msg, err)
		return
	} else {
		code = 0
		msg = "上架成功"
		return
	}
}

func (obj podApi_) OffSale(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq podReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.PodUuid == "" {
		msg = "参数错误"
		return
	}

	var pod model.Pod
	if err := pod.GetByUuid(oReq.PodUuid); err != nil {
		msg = "pod信息获取失败"
		logger.Error(msg, err, oReq)
		return
	}

	if pod.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	if pod.Status == 0 {
		msg = "已经是下架状态"
		return
	}

	if err := pod.SetStatus(0); err != nil {
		msg = "下架失败"
		logger.Error(msg, err)
		return
	} else {
		code = 0
		msg = "下架成功"
		return
	}
}

func (obj podApi_) Hidden(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq podReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.PodUuid == "" {
		msg = "参数错误"
		return
	}

	var pod model.Pod
	if err := pod.GetByUuid(oReq.PodUuid); err != nil {
		msg = "pod信息获取失败"
		logger.Error(msg, err, oReq)
		return
	}
	if pod.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	if pod.Status == 1 {
		msg = "上架状态，不能删除"
		return
	}
	if pod.InstanceUuid != "" {
		var instance model.Instance
		if err := instance.GetByUuid(pod.InstanceUuid); err != nil {
			if err != gorm.ErrRecordNotFound {
				msg = "查询实例失败"
				logger.Error(msg, err)
				return
			}
		} else {
			if instance.Status == enums.InstanceStatusEnum.Running {
				msg = "装机实例正在运行中，请先释放后再删除"
				return
			}
			if instance.Status == enums.InstanceStatusEnum.BootInProgress {
				msg = "装机实例正在开机中，请稍后再操作"
				return
			}
		}
	}
	{
		queryParm := make(map[string]interface{})
		queryParm["user_id"] = claims.UserId
		queryParm["pod_id"] = pod.ID
		queryParm["image_type"] = enums.ImageTypeEnum.Public
		queryParm["order"] = "pod_id desc, id desc"
		var podImagePrivate model.PodImage
		var aryPublic = make([]podImageResp, 0)
		if _, err := podImagePrivate.ListPro(&aryPublic, queryParm, 1, 1); err != nil {
			msg = "查询失败"
			logger.Error(msg, err)
			return
		} else {
			if len(aryPublic) > 0 {
				msg = "请先删除该POD下的镜像"
				return
			}
		}

	}

	if err := pod.SetStatus(9); err != nil {
		msg = "删除失败"
		logger.Error(msg, err)
		return
	} else {
		code = 0
		msg = "删除成功"
		return
	}
}

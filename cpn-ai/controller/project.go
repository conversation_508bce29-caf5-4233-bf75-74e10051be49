package controller

import (
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/service"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"os/exec"
	"strings"
	"time"
)

type _projectApi struct {
}

func (obj _projectApi) Publish(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	logger.Info("开始发布项目")

	//claims := c.Value("center_claims").(*middleware.CenterClaims)
	//if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
	//	logger.Error("权限不足")
	//	return
	//}

	f, err := c.FormFile("file")
	if err != nil {
		logger.Error(err)
		return
	}

	projectName, _ := c.GetPostForm("project_name")

	filePath := ""
	if projectName == "aigc_api_4005" {
		filePath = "/www/wwwroot/dev.cyuai.com/api/design_ai.linux"
	} else if projectName == "sheys_api_5021" {
		filePath = "/www/wwwroot/sheys.cn/api/sheys_ai.linux"
	} else if projectName == "design_api_5001" {
		filePath = "/www/wwwroot/design.cyuai.com/api/design_ai.linux"
	} else if projectName == "design_api_online" {
		filePath = "/www/wwwroot/design.cyuai.com/api_online/design_ai.linux"
	} else if projectName == "aigc_api_4003" {
		filePath = "/www/wwwroot/aigc.cyuai.com/api/zcloud_ai.linux"
	} else if projectName == "aigc_api_online" {
		filePath = "/www/wwwroot/aigc.cyuai.com/api_online/zcloud_ai.linux"
	} else if projectName == "center_api_test" {
		filePath = "/www/wwwroot/center.cyuai.com/api_test/center_ai.linux"
	} else if projectName == "center_api_online" {
		filePath = "/www/wwwroot/center.cyuai.com/api_online/center_ai.linux"
	} else if projectName == "center_api" {
		filePath = "/www/wwwroot/center.cyuai.com/api/center_ai.linux"
	} else if projectName == "camera_api" {
		filePath = "/www/wwwroot/camera.cyuai.com/api/camera_ai.linux"
	} else if projectName == "camera_api_online" {
		filePath = "/www/wwwroot/camera.cyuai.com/api_online/camera_ai.linux"
	} else if projectName == "camera_api_test" {
		filePath = "/www/wwwroot/camera.cyuai.com/api_test/camera_ai.linux"
	} else if projectName == "douyin_api_test" {
		filePath = "/www/wwwroot/douyin.cyuai.com/api_test/douyin_ai.linux"
	} else if projectName == "douyin_api" {
		filePath = "/www/wwwroot/douyin.cyuai.com/api/douyin_ai.linux"
	} else if projectName == "sd_api_online" {
		filePath = "/www/wwwroot/sd.cyuai.com/api_online/sd_ai.linux"
	} else if projectName == "sd_api" {
		filePath = "/www/wwwroot/sd.cyuai.com/api/sd_ai.linux"
	} else if projectName == "cpn_sched" {
		filePath = "/www/wwwroot/www.suanyun.cn/api/cpn_sched.linux"
	} else if projectName == "cpn_node_hz01" {
		filePath = "/www/wwwroot/hz01.suanyun.cn/api/cpn_node.linux"
	} else if projectName == "cpn_node_dist" {
		filePath = "/www/wwwroot/www.suanyun.cn/dist/"
	}

	logger.Info("filePath：", filePath)
	if exists, err := utils.PathFileExists(filePath); err != nil {
		logger.Error(err, filePath)
		msg = "文件检查出错"
		return
	} else if exists == false {
		msg = "文件不存在"
		logger.Error("文件不存在", filePath)
		return
	}

	//备份运行中的程序文件
	backFilepath := filePath + "_back" + time.Now().Format("20060102150405")
	cmd1 := exec.Command("cp", filePath, backFilepath)
	if err := cmd1.Run(); err != nil {
		msg = "备份执行文件命令失败"
		logger.Error(err)
		return
	}
	logger.Info("备份文件成功", backFilepath)

	//删除运行的程序文件
	cmd := exec.Command("rm", "-f", filePath)
	if err := cmd.Run(); err != nil {
		msg = "执行删除命令失败"
		logger.Error(err)
		return
	}
	logger.Info("删除文件成功", filePath)

	//保存上传的程序文件
	if err := c.SaveUploadedFile(f, filePath); err != nil {
		msg = "程序文件保存失败"
		logger.Error(err)
		return
	}
	logger.Info("文件上传成功并覆盖原文件", filePath)
	//设置程序文件权限
	if err := os.Chmod(filePath, 0777); err != nil {
		msg = "设置文件权限失败"
		logger.Error(err)
		return
	}

	resMap := service.BaoTaService.RestartProject(projectName)
	if resMap == nil {
		msg = "重启失败，请手动重启," + projectName
		logger.Error(msg, err)
		return
	} else {
		logger.Info("resMap:", resMap)
	}
	time.Sleep(time.Duration(2) * time.Second)
	if resMap["data"] == "项目未启动" {
		logger.Error("项目未启动,发送启动命令")
		resMap = service.BaoTaService.StartProject(projectName)
		if resMap == nil {
			msg = "启动项目失败，请手动重启," + projectName
			logger.Error(err)
			return
		} else {
			logger.Info("启动resMap:", resMap)
		}
	}
	code = 0
	msg = "发布成功"
	result["occur_time"] = time.Now().Format("2006-01-02 15:04:05")
	result["res_map"] = resMap
}

func (obj _projectApi) Deploy(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	logger.Info("开始部署项目")

	//claims := c.Value("center_claims").(*middleware.CenterClaims)
	//if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
	//	logger.Error("权限不足")
	//	return
	//}

	f, err := c.FormFile("file")
	if err != nil {
		logger.Error(err)
		return
	}

	projectName, _ := c.GetPostForm("project_name")

	filePath := ""
	dirPath := ""
	if projectName == "aigc_api_4005" {
		filePath = "/www/wwwroot/dev.cyuai.com/api/design_ai.linux"
	} else if projectName == "sheys_api_5021" {
		filePath = "/www/wwwroot/sheys.cn/api/sheys_ai.linux"
	} else if projectName == "design_api_5001" {
		filePath = "/www/wwwroot/design.cyuai.com/api/design_ai.linux"
	} else if projectName == "design_api_online" {
		filePath = "/www/wwwroot/design.cyuai.com/api_online/design_ai.linux"
	} else if projectName == "aigc_api_4003" {
		filePath = "/www/wwwroot/aigc.cyuai.com/api/zcloud_ai.linux"
	} else if projectName == "aigc_api_online" {
		filePath = "/www/wwwroot/aigc.cyuai.com/api_online/zcloud_ai.linux"
	} else if projectName == "center_api_test" {
		filePath = "/www/wwwroot/center.cyuai.com/api_test/center_ai.linux"
	} else if projectName == "center_api_online" {
		filePath = "/www/wwwroot/center.cyuai.com/api_online/center_ai.linux"
	} else if projectName == "center_api" {
		filePath = "/www/wwwroot/center.cyuai.com/api/center_ai.linux"
	} else if projectName == "camera_api" {
		filePath = "/www/wwwroot/camera.cyuai.com/api/camera_ai.linux"
	} else if projectName == "camera_api_online" {
		filePath = "/www/wwwroot/camera.cyuai.com/api_online/camera_ai.linux"
	} else if projectName == "camera_api_test" {
		filePath = "/www/wwwroot/camera.cyuai.com/api_test/camera_ai.linux"
	} else if projectName == "douyin_api_test" {
		filePath = "/www/wwwroot/douyin.cyuai.com/api_test/douyin_ai.linux"
	} else if projectName == "douyin_api" {
		filePath = "/www/wwwroot/douyin.cyuai.com/api/douyin_ai.linux"
	} else if projectName == "sd_api_online" {
		filePath = "/www/wwwroot/sd.cyuai.com/api_online/sd_ai.linux"
	} else if projectName == "sd_api" {
		filePath = "/www/wwwroot/sd.cyuai.com/api/sd_ai.linux"
	} else if projectName == "cpn_sched" {
		filePath = "/www/wwwroot/www.suanyun.cn/api/cpn_sched.linux"
	} else if projectName == "cpn_node_hz01" {
		filePath = "/www/wwwroot/hz01.suanyun.cn/api/cpn_node.linux"
	} else if projectName == "cpn_node_dist" {
		filePath = fmt.Sprintf("/www/wwwroot/www.suanyun.cn/dist_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/www.suanyun.cn/dist"
	}

	//保存上传的程序文件
	if err := c.SaveUploadedFile(f, filePath); err != nil {
		msg = "程序文件保存失败"
		logger.Error(err)
		return
	}
	logger.Info("文件上传成功", filePath)

	tmpAry := strings.Split(dirPath, "/")
	if strings.HasPrefix(dirPath, "/www/wwwroot/") && len(tmpAry) >= 5 {
		//if err = os.RemoveAll(dirPath); err != nil {
		//	msg = "删除文件夹出错"
		//	logger.Error(msg, err)
		//	return
		//}
		newPath := fmt.Sprintf("%s_back%s", dirPath, time.Now().Format("20060102150405"))
		if err = os.Rename(dirPath, newPath); err != nil {
			msg = "文件夹重命名出错"
			logger.Error(msg, err)
			return
		}
		logger.Info("备份文件成功", newPath)
	} else {
		msg = "移除路径不正确"
		logger.Error(msg, dirPath)
		return
	}

	// 执行 unzip 命令解压文件
	cmd := exec.Command("unzip", filePath, "-d", dirPath)
	if err := cmd.Run(); err != nil {
		fmt.Println("解压失败:", err)
		msg = "解压失败"
		logger.Error(msg, err)
		return
	}

	code = 0
	msg = "发布成功"
	result["occur_time"] = time.Now().Format("2006-01-02 15:04:05")
}

var ProjectApi _projectApi

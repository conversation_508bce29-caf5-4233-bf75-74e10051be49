package controller

import (
	"cpn-ai/common/verifycode"
	"github.com/gin-gonic/gin"
	"net/http"
)

// GetVerifyCode 获取滑块验证码
func GetVerifyCode(c *gin.Context) {
	code := 0
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	codeId, masterImage, tileImage, y, err := verifycode.GetNewCode()
	if err != nil {
		code = 1
		msg = "验证码获取失败"
		return
	}

	result["code_id"] = codeId
	result["master_image"] = masterImage
	result["tile_image"] = tileImage
	result["y"] = y
}

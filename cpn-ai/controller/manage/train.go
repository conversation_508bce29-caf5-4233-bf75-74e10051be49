package manage

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
)

type trainApi_ struct {
}

var TrainApi trainApi_

type trainReq struct {
	QTaskId  string `json:"q_task_id"`
	JobsUuid string `json:"jobs_uuid"`
}

type trainModelParamReq struct {
	ModelName string           `json:"model_name"`
	Param     []map[string]any `json:"param"`
}

type trainModelsReq struct {
	Mobile   string `json:"mobile"`
	UserId   uint   `json:"user_id"`
	Kw       string `json:"kw"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type trainModelsResp struct {
	Id        uint              `json:"id"`
	ModelType string            `json:"model_type"`
	Title     string            `json:"title"`
	Name      string            `json:"name"`
	Path      string            `json:"path"`
	Param     string            `json:"-"`
	ParamObj  []map[string]any  `json:"param"`
	Index     int               `json:"index"`
	UseCount  int               `json:"use_count"`
	LastUseAt jsontime.JsonTime `json:"last_use_at"`
	Remark    string            `json:"remark"`
	Status    int               `json:"status"`
	CreatedAt jsontime.JsonTime `json:"created_at"`
}

func (obj trainApi_) ModelsList(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq trainModelsReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var trainModels model.TrainModels
	ary := make([]trainModelsResp, 0)
	queryParm := make(map[string]interface{})

	if _, err := trainModels.List(&ary, queryParm, 1, 100); err != nil {
		msg = "查询失败"
		return
	} else {
		for i := 0; i < len(ary); i++ {
			ary[i].ParamObj = utils.GetMapAryFromJson(ary[i].Param)
		}
		result["models"] = ary
		code = 0
	}
}

func (obj trainApi_) GetJob(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq trainReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.QTaskId == "" {
		var trainJobs model.TrainJobs
		if err := trainJobs.GetByUuid(oReq.JobsUuid); err != nil {
			msg = err.Error()
			logger.Error(msg)
			return
		}
		oReq.QTaskId = trainJobs.QTaskId
	}

	if oReq.QTaskId == "" {
		msg = "参数错误"
		return
	}

	if task, err := service.TrainService.JobItem(oReq.QTaskId); err != nil {
		msg = err.Error()
		logger.Error(msg)
		return
	} else {
		result["task"] = task
		code = 0
		return
	}

}

func (obj trainApi_) ModelsParam(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq trainModelParamReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var trainModels model.TrainModels
	if err := trainModels.GetByName(oReq.ModelName); err != nil {
		msg = "参数错误"
		if errors.Is(err, gorm.ErrRecordNotFound) {
			msg = "模型不存在"
		}
		logger.Error(msg, err)
		return
	}
	if len(oReq.Param) == 0 {
		msg = "oReq.Param参数错误"
		return
	}

	if trainModels.Param != "" {
		logJson := trainModels.Param
		operationLog := model.OperationLog{
			OperatorUserId: claims.UserId,
			LogType:        enums.OperationLogTypeEnum.ModifySdParm,
			OrigWhere:      enums.OperationOrigWhereEnum.TrainModels,
			OrigId:         trainModels.ID,
			Ip:             utils.GetClientIp(c.Request.Header),
			LogJson:        logJson,
			UserEnv:        "{}",
		}
		if err := operationLog.Save(); err != nil {
			msg = "备份信息失败"
			logger.Error(msg, err)
			return
		}
	}

	if err := trainModels.SetParam(utils.GetJsonFromStruct(oReq.Param)); err != nil {
		msg = "设置默认参数失败"
		logger.Error(msg, err)
	}

	msg = "默认参数设置成功"
	code = 0
}

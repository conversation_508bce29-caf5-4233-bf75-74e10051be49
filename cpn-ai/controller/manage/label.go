package manage

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	"strings"
)

type labelApi_ struct {
}

var LabelApi labelApi_

type saveLabelReq struct {
	LabelId    uint    `json:"label_id"`
	ParentId   uint    `json:"parent_id"`
	Title      string  `json:"title"`
	Status     int     `json:"status"`
	OrderIndex float64 `json:"order_index"`
}
type listLabelReq struct {
	LabelId  uint   `json:"label_id"`
	ParentId uint   `json:"parent_id"`
	Title    string `json:"title"`
}

type labelResp struct {
	Id         uint              `json:"id"`
	ParentId   uint              `json:"parent_id"`
	Title      string            `json:"title"`
	OrderIndex float64           `json:"order_index"`
	Status     int               `json:"status"`
	CreatedAt  jsontime.JsonTime `json:"created_at"`
}

type selectResp struct {
	labelResp
	Childs []*selectResp `json:"childs"`
}

func (obj labelApi_) Save(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq saveLabelReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	oReq.Title = strings.Trim(oReq.Title, " ")
	if len(oReq.Title) < 2 {
		msg = "标签至少两个字符"
		logger.Error(msg, oReq)
		return
	}
	var label model.Label
	if oReq.LabelId > 0 {
		if err := label.GetById(oReq.LabelId); err != nil {
			logger.Error(err)
			msg = "获取Label信息失败"
			return
		}
		oReq.ParentId = label.ParentId
	}
	{
		var labelCheck model.Label
		if err := labelCheck.GetByTitle(oReq.ParentId, oReq.Title); err != nil {
			if err != gorm.ErrRecordNotFound {
				logger.Error(err)
				msg = "获取Label信息失败"
				return
			}
		} else {
			if labelCheck.ID == label.ID {

			} else {
				msg = "该标签名称已经存在"
				return
			}
		}
	}
	label.Title = oReq.Title
	label.ParentId = oReq.ParentId
	label.Status = oReq.Status
	label.OrderIndex = oReq.OrderIndex
	if err := label.Save(); err != nil {
		msg = "保存失败"
		logger.Error(msg, err, oReq)
		return
	} else {
		code = 0
		msg = "保存成功"
	}

}

func (obj labelApi_) SetIndex(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq saveLabelReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	oReq.Title = strings.Trim(oReq.Title, " ")
	if len(oReq.Title) < 2 {
		msg = "标签至少两个字符"
		logger.Error(msg, oReq)
		return
	}
	var label model.Label
	if oReq.LabelId > 0 {
		if err := label.GetById(oReq.LabelId); err != nil {
			logger.Error(err)
			msg = "获取Label信息失败"
			return
		}
		oReq.ParentId = label.ParentId
	}
	{
		var labelCheck model.Label
		if err := labelCheck.GetByTitle(oReq.ParentId, oReq.Title); err != nil {
			if err != gorm.ErrRecordNotFound {
				logger.Error(err)
				msg = "获取Label信息失败"
				return
			}
		} else {
			if labelCheck.ID == label.ID {

			} else {
				msg = "该标签名称已经存在"
				return
			}
		}
	}
	label.Title = oReq.Title
	label.ParentId = oReq.ParentId
	label.Status = oReq.Status
	if err := label.Save(); err != nil {
		msg = "保存失败"
		logger.Error(msg, err, oReq)
		return
	} else {
		code = 0
		msg = "保存成功"
	}

}

func (obj labelApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq listLabelReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var label model.Label
	ary := make([]labelResp, 0)
	queryParm := make(map[string]interface{})
	if oReq.ParentId > 0 {
		queryParm["parent_id"] = oReq.ParentId
	}

	if _, err := label.List(&ary, queryParm, 1, 100); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {
		result["labels"] = ary
		code = 0
	}
}

func (obj labelApi_) Select(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq listLabelReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var label model.Label
	ary := make([]labelResp, 0)
	queryParm := make(map[string]interface{})
	if oReq.ParentId > 0 {
		queryParm["parent_id"] = oReq.ParentId
	}

	queryParm["order"] = "select"

	if _, err := label.List(&ary, queryParm, 1, 1000); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {

		// 创建一个map来存储所有的selectResp
		aryMap := make(map[uint]*selectResp)
		for _, item := range ary {
			aryMap[item.Id] = &selectResp{
				labelResp: item,
				Childs:    []*selectResp{},
			}
		}

		respAry := make([]*selectResp, 0)

		// 重新组织数据
		for _, item := range ary {
			if item.ParentId > 0 {
				if parent, ok := aryMap[item.ParentId]; ok {
					parent.Childs = append(parent.Childs, aryMap[item.Id])
				}
			}
		}

		for _, item := range ary {
			if item.ParentId == 0 {
				respAry = append(respAry, aryMap[item.Id])
			}
		}

		// 对respAry按Item.Index排序  不用排序了，从数据库拉取数据的时候已经排序过了
		//sort.Slice(respAry, func(i, j int) bool {
		//	return respAry[i].OrderIndex > respAry[j].OrderIndex
		//})

		// 对每个selectResp的Childs按Index排序
		//for _, resp := range respAry {
		//	sort.Slice(resp.Childs, func(i, j int) bool {
		//		return resp.Childs[i].OrderIndex > resp.Childs[j].OrderIndex
		//	})
		//}
		result["labels"] = respAry

		code = 0
	}
}

package manage

import (
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/service"
	"fmt"
	"strings"
	"testing"
)

func TestLabelIds(t *testing.T) { // 测试函数名必须以Test开头，必须接收一个*testing.T类型参数
	ary := strings.Split("4|7|8", "|")
	labelIds := "|"
	for _, tmp := range ary {
		if tmp == "|" {
			continue
		}
		tmpLabelId := utils.String2Uint(tmp)
		if aryLabelId, err := service.GetParentLabels(tmpLabelId); err != nil {
			logger.Error(err, "oReq.LabelIds:")
		} else {
			for _, labelId := range aryLabelId {
				key := fmt.Sprintf("|%d|", labelId)
				if !strings.Contains(labelIds, key) {
					labelIds += fmt.Sprintf("%d|", labelId)
				}
			}
		}
	}
	fmt.Println("labelIds" + labelIds)
	if labelIds == "|" {
		labelIds = ""
	}
}

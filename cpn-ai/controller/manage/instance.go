package manage

import (
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/internal/ccm/dockerfile"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"cpn-ai/service/tasklog"
	"errors"
	"fmt"
	"net/http"
	"runtime"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type _instanceApi struct {
}

var InstanceApi _instanceApi

type handShutdownReq struct {
	StartupMark string `json:"startup_mark"`
}

type instanceReq struct {
	Mobile           string `json:"mobile"`
	InstanceId       uint   `json:"instance_id"`
	InstanceUuid     string `json:"instance_uuid"`
	InstanceType     int    `json:"instance_type"`
	UserId           uint   `json:"user_id"`
	PodCategory      int    `json:"pod_category"`
	StartupMark      string `json:"startup_mark"`
	StartupNodeId    uint   `json:"startup_node_id"`
	StartupVirtualId uint   `json:"startup_virtual_id"`
	StartupVirtual   string `json:"startup_virtual"`
	StartupImageId   uint   `json:"startup_image_id"`
	DockerId         string `json:"docker_id"`
	ImageId          uint   `json:"image_id"`
	SaveImageId      uint   `json:"save_image_id"`
	PodId            uint   `json:"pod_id"`
	GpuModelId       uint   `json:"gpu_model_id"`
	Status           int    `json:"status"`
	Destroy          bool   `json:"destroy"`
	Task             string `json:"task"`
	Force            bool   `json:"force"`
	Page             int    `json:"page"`
	PageSize         int    `json:"page_size"`
	Pw               string `json:"pw"`
}
type instanceCreateReq struct {
	UserId       uint            `json:"user_id"`
	InstanceType int             `json:"instance_type"`
	PodId        uint            `json:"pod_id"`
	PodUuid      string          `json:"pod_uuid"`
	ImageId      uint            `json:"image_id"`
	ImageUuid    string          `json:"image_uuid"`
	ImageTag     string          `json:"image_tag"`
	GpuModelUuid string          `json:"gpu_model_uuid"`
	Gpus         int             `json:"gpus"`
	ChargingType int             `json:"charging_type"`
	ChargingNum  int             `json:"charging_num"`
	Amount       decimal.Decimal `json:"amount"`

	NodeId    uint `json:"node_id"`
	VirtualId uint `json:"virtual_id"`
	NoCard    int  `json:"no_card"`
}
type instanceStartupReq struct {
	InstanceUuid string `json:"instance_uuid"`
	NodeId       uint   `json:"node_id"`
	VirtualId    uint   `json:"virtual_id"`
	NoCard       int    `json:"no_card"`
	Force        bool   `json:"force"`
}
type instanceResp struct {
	ID                  uint                     `json:"id"`
	CreatedAt           jsontime.JsonTime        `json:"created_at"`
	UpdatedAt           jsontime.JsonTime        `json:"updated_at"`
	Uuid                string                   `json:"uuid"`
	InstanceType        int                      `json:"instance_type"`
	InstanceTypeTxt     string                   `json:"instance_type_txt"`
	UserId              uint                     `json:"user_id"`
	PodId               uint                     `json:"pod_id"`
	PodName             string                   `json:"pod_name"`
	PodTitle            string                   `json:"pod_title"`
	Category            int                      `json:"category"`
	GpuModelId          uint                     `json:"gpu_model_id"`
	GpuModelName        string                   `json:"gpu_model_name"`
	Gpus                int                      `json:"gpus"`
	NoCard              int                      `json:"no_card"`
	ChargingType        int                      `json:"charging_type"`
	ChargingNum         int                      `json:"charging_num"`
	FirstupTime         jsontime.JsonTime        `json:"firstup_time"`
	StartTime           jsontime.JsonTime        `json:"start_time"`
	EndTime             jsontime.JsonTime        `json:"end_time"`        //包日 包月这个时间提前设置
	SubIncrAmount       decimal.Decimal          `json:"sub_incr_amount"` //返还时需要用该金额去减已用金额
	LastSettleTime      jsontime.JsonTime        `json:"last_settle_time"`
	LastSettleAmount    decimal.Decimal          `json:"last_settle_amount"` //包日包月预付金额
	LastPodSettleTime   jsontime.JsonTime        `json:"last_pod_settle_time"`
	StartupMark         string                   `json:"startup_mark"`
	StartupMarkTime     jsontime.JsonTime        `json:"startup_mark_time"`
	StartupMaps         string                   `json:"-"`
	StartupMapsObj      []map[string]interface{} `json:"startup_maps"`
	StartupNodeId       uint                     `json:"startup_node_id"`
	StartupVirtualId    uint                     `json:"startup_virtual_id"`
	StartupVirtualHost  string                   `json:"startup_virtual_host"`
	StartupGpus         string                   `json:"startup_gpus"`
	StartupElapse       uint                     `json:"startup_elapse"`
	StartupTime         jsontime.JsonTime        `json:"startup_time"`
	StartupImageId      uint                     `json:"startup_image_id"`
	ShutdownTime        jsontime.JsonTime        `json:"shutdown_time"`
	ShutdownRegularTime jsontime.JsonTime        `json:"shutdown_regular_time"`
	ShutdownRegularSave bool                     `json:"shutdown_regular_save"`
	ShutdownReason      string                   `json:"shutdown_reason"`
	ShutdownDestroy     bool                     `json:"shutdown_destroy"`
	ShutdownTask        string                   `json:"shutdown_task"`
	ReminderAt          jsontime.JsonTime        `json:"reminder_at"`
	LastUseTime         jsontime.JsonTime        `json:"last_use_time"`
	LastCheckTime       jsontime.JsonTime        `json:"last_check_time"`
	Status              int                      `json:"status"`
	StatusTxt           string                   `json:"status_txt"`
	ImageStatus         int                      `json:"image_status"`
	ImageStatusTxt      string                   `json:"image_status_txt"`
	DockerId            string                   `json:"docker_id"`
	WebUrl              string                   `json:"web_url"`
	ApiBase             string                   `json:"api_base"`
	OrderNo             string                   `json:"order_no"` //包日包月这里会产生订单编号
	LastStatus          int                      `json:"last_status"`
	LastRecordId        uint                     `json:"last_record_id"`
	ImageId             uint                     `json:"image_id"`
	ImageType           int                      `json:"image_type"`
	ImageTypeTxt        string                   `json:"image_type_txt"`
	ImageName           string                   `json:"image_name"`
	ImageTag            string                   `json:"image_tag"`
	SaveImageId         uint                     `json:"save_image_id"`
	PodUuid             string                   `json:"pod_uuid"`
	PodPrice            string                   `json:"pod_price"`
	OutWebUrl           string                   `json:"out_web_url"`
	ChargingTypeName    string                   `json:"charging_type_name"`
	GpuModel            map[string]interface{}   `json:"gpu_model"`
}

type instanceSaveImageByManagerReq struct {
	InstanceUuid string `json:"instance_uuid"`
	NodeId       uint   `json:"node_id"`
	VirtualId    uint   `json:"virtual_id"`
	ImageTag     string `json:"image_tag"`
	StorageMode  int    `json:"storage_mode"`
	Shutdown     bool   `json:"shutdown"`
	KOLRemark    string `json:"kol_remark"`
}

type saveImageReq struct {
	InstanceUuid string `json:"instance_uuid"`
	StartupMark  string `json:"startup_mark"`
	ImageUuid    string `json:"image_uuid"`
	ImageId      uint   `json:"image_id"`
}

type saveImageCommitReq struct {
	InstanceUuid string `json:"instance_uuid"`
	Force        bool   `json:"force"`
}

type instanceMonitorReq struct {
	InstanceUuid string   `json:"instance_uuid"`
	StartTime    int64    `json:"start_time"`
	EndTime      int64    `json:"end_time"`
	Step         int64    `json:"step"` // 时间间隔
	Metrics      []string `json:"metrics"`
}

func (obj *_instanceApi) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq instanceReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	queryParm := make(map[string]interface{})
	var instance model.Instance
	if oReq.InstanceId > 0 {
		queryParm["id"] = oReq.InstanceId
	} else if oReq.InstanceUuid != "" {
		if len(oReq.InstanceUuid) == 32 {
			if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
				msg = "数据不存在"
				logger.Error(msg, err)
				return
			}
		}
	}
	if oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	if instance.ID > 0 { //用户ID
		queryParm["id"] = instance.ID
	} else {
		if oReq.InstanceUuid != "" {
			queryParm["uuid"] = oReq.InstanceUuid
		}
	}

	if oReq.UserId > 0 { //用户ID
		queryParm["user_id"] = oReq.UserId
	}
	if oReq.InstanceType >= 0 { //实例类型 0普通  3Kol
		queryParm["instance_type"] = oReq.InstanceType
	}
	if oReq.PodCategory > 0 { //Pod类型
		queryParm["category"] = oReq.PodCategory
	}
	if oReq.ImageId > 0 { //镜像ID
		queryParm["image_id"] = oReq.ImageId
	}
	if oReq.StartupImageId > 0 { //保存镜像ID
		queryParm["startup_image_id"] = oReq.StartupImageId
	}
	if oReq.SaveImageId > 0 { //保存镜像ID
		queryParm["save_image_id"] = oReq.SaveImageId
	}
	if oReq.PodId > 0 { //Pod ID
		queryParm["pod_id"] = oReq.PodId
	}
	if oReq.GpuModelId > 0 { //Gpu型号ID
		queryParm["gpu_model_id"] = oReq.GpuModelId
	}
	if oReq.StartupMark != "" {
		queryParm["startup_mark"] = oReq.StartupMark
	}
	if oReq.StartupNodeId > 0 { //启动节点ID
		queryParm["startup_node_id"] = oReq.StartupNodeId
	}

	if oReq.Mobile != "" {
		if oReq.UserId > 0 {
			msg = "手机号和用户ID只能填一个"
			return
		}
		var user model.User
		if err := user.GetByMobile(oReq.Mobile); err != nil {
			msg = err.Error()
			if err == gorm.ErrRecordNotFound {
				msg = "该手机号码用户不存在"
			}
			return
		} else {
			queryParm["user_id"] = user.ID
		}
	}

	if oReq.StartupVirtual != "" {
		if num, err := strconv.Atoi(oReq.StartupVirtual); err != nil {
			var virtual model.Virtual
			if err := virtual.GetByHost(oReq.StartupVirtual); err != nil {
				logger.Error(err, "  ", oReq.StartupVirtual)
				msg = "查询虚拟机失败"
				if err == gorm.ErrRecordNotFound {
					msg = "未查询到虚拟机" + oReq.StartupVirtual
				}
				return
			} else {
				queryParm["startup_virtual_id"] = virtual.ID
			}

		} else {
			queryParm["startup_virtual_id"] = num
		}
	}

	if oReq.StartupVirtualId > 0 { //启动虚拟机ID
		queryParm["startup_virtual_id"] = oReq.StartupVirtualId
	}
	if oReq.DockerId != "" {
		queryParm["docker_id"] = oReq.DockerId
	}
	if oReq.Status >= 0 {
		queryParm["status"] = oReq.Status
	}
	queryParm["all"] = true

	var ary = make([]instanceResp, 0)
	if total, err := instance.ListPro(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(ary); i++ {

			ary[i].InstanceTypeTxt = enums.InstanceTypeEnum.Name(ary[i].InstanceType)

			if ary[i].Category == enums.PodCategoryEnum.PodInstance {
				ary[i].OutWebUrl = fmt.Sprintf("https://%s.hz01.%s", ary[i].Uuid, service.GetSiteDomain(c.Request.Referer()))
			}

			ary[i].StartupMaps = strings.Replace(ary[i].StartupMaps, "suanyun.cn", "chenyu.cn", -1)

			if ary[i].StartupMaps != "" {
				ary[i].StartupMapsObj = utils.GetMapAryFromJson(ary[i].StartupMaps)
			}

			var virtual model.Virtual
			if ary[i].StartupVirtualId > 0 {
				if err := virtual.GetById(ary[i].StartupVirtualId); err != nil {
					logger.Error(err, "virtualId:", ary[i].StartupVirtualId)
				} else {
					ary[i].StartupVirtualHost = virtual.Host
				}
			}

			var pod model.Pod
			if err := pod.GetById(ary[i].PodId); err != nil {
				msg = "数据不存在"
				logger.Error(msg, err)
			} else {
				ary[i].PodName = pod.PodName
				ary[i].PodTitle = pod.Title
				ary[i].PodUuid = pod.Uuid
				ary[i].PodPrice = pod.Price
			}

			var gpuModel model.GpuModel
			if err := gpuModel.GetById(ary[i].GpuModelId); err != nil {
				msg = "数据不存在"
				logger.Error(msg, err)
			} else {
				m := make(map[string]interface{})
				m["gpu_name"] = gpuModel.GpuName
				m["price"] = gpuModel.Price
				m["uuid"] = gpuModel.Uuid
				m["title"] = gpuModel.Title
				if gpuModel.Title == "" {
					m["title"] = gpuModel.GpuName
				}
				ary[i].GpuModel = m
			}
			ary[i].StatusTxt = enums.InstanceStatusEnum.Name(ary[i].Status)
			ary[i].ChargingTypeName = enums.ChargingTypeEnum.Name(ary[i].ChargingType)

			if ary[i].ImageId > 0 {
				ary[i].ImageTypeTxt = enums.ImageTypeEnum.Name(ary[i].ImageType)
			}
		}
		result["instance"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj *_instanceApi) StatsRunning(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("Shutdown panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var instance model.Instance
	if usageInstanceCount, noCardInstanceCount, kolInstanceCount, err := instance.StatsRunning(); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
	} else {
		result["usage_count"] = usageInstanceCount
		result["nocard_count"] = noCardInstanceCount
		result["kol_count"] = kolInstanceCount
		code = 0
	}
}

func (obj *_instanceApi) Create(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	//claims := c.Value("center_claims").(*middleware.CenterClaims)
	//if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
	//	msg = "权限不足"
	//	return
	//}

	var oReq instanceCreateReq
	logger.Info("创建实例：oReq:", utils.GetJsonFromStruct(oReq))
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	claimsUserId := oReq.UserId
	if claimsUserId == 0 {
		msg = "请输入用户ID"
		return
	}

	ccm := oReq.PodUuid == "ccm"
	if ccm {
		if !slices.Contains([]int{1, 2, 4, 8}, oReq.Gpus) {
			msg = "显卡数量不对"
			return
		}

		oReq.PodUuid = "046cff0c6c3444a8884800d0fdfc5d95" //
	}

	if oReq.ImageId <= 0 {
		msg = "请输入镜像ID"
		return
	}

	var podImage model.PodImage
	if err := podImage.GetById(oReq.ImageId); err != nil {
		msg = "获取镜像信息失败 err:" + err.Error()
		return
	}

	var pod model.Pod
	if err := pod.GetById(podImage.PodId); err != nil {
		msg = "pod信息获取失败"
		logger.Error(msg, err)
		return
	}

	//不检测是否是学员镜像
	//if pod.ClassType == 1 {
	//	if ok, msg1, err := service.CheckClassStuByPod(oReq.UserId, pod); err != nil {
	//		msg = msg1
	//		logger.Error(msg, err)
	//		return
	//	} else {
	//		if !ok {
	//			msg = msg1
	//			return
	//		}
	//	}
	//}

	if pod.Category != enums.PodCategoryEnum.PodInstance && pod.Category != enums.PodCategoryEnum.GpuInstance {
		msg = "pod类别不正确"
		logger.Error(msg, pod.ID)
		return
	}

	if !ccm {
		oReq.Gpus = pod.NeedGpus
		//if oReq.Gpus < pod.NeedGpus {
		//	//msg = fmt.Sprintf("该Pod至少需要个%d显卡", pod.NeedGpus)
		//	//logger.Error(msg, oReq)
		//	//return
		//	oReq.Gpus = pod.NeedGpus
		//}
		//if oReq.Gpus > pod.NeedGpus {
		//	msg = fmt.Sprintf("显卡数量最多选择3个")
		//	logger.Error(msg, oReq)
		//	return
		//}
	}

	var gpuModel model.GpuModel
	if err := gpuModel.GetByUuid(oReq.GpuModelUuid); err != nil {
		msg = "显卡型号信息获取失败"
		logger.Error(msg, err)
		return
	}

	var user model.User
	if err := user.GetById(claimsUserId); err != nil {
		msg = "用户信息获取失败"
		logger.Error(msg, err)
		return
	}

	//if user.Insider == 0 {
	//	msg = "站点正在更新服务，暂时不能启动"
	//	return
	//}

	//var instanceLimit model.Instance
	//if total, err := instanceLimit.CountForLimit(claims.UserId); err != nil {
	//	msg = "查询实例数量失败"
	//	logger.Error(msg, err, "user_id:", claims.UserId)
	//	return
	//} else {
	//	if total >= 5 {
	//		msg = "实例数量已到限额，请先到【我的POD实例】中删除再创建"
	//		return
	//	}
	//}

	unitPrice := pod.UnitPrice(enums.ChargingTypeEnum.Usage)
	if unitPrice.GreaterThan(decimal.Zero) {
		if user.Amount.LessThan(decimal.Zero) {
			msg = "该Pod作者需要收取服务费，您的余额不足，请先充值"
			code = 5
			return
		}
	}

	instanceTitle := ""
	if ccm && len(podImage.Uuid) == 64 {
		row := dockerfile.GetByKey(oReq.ImageUuid)
		if row == nil {
			msg = "算力市场镜像不存在"
			return
		}

		err := podImage.GetByImageTypeAndTag(enums.ImageTypeEnum.CCM, row.Key, row.Tag)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			podImage = model.PodImage{
				Uuid:        utils.GetUUID(),
				UserId:      pod.UserId,
				ImageType:   enums.ImageTypeEnum.CCM,
				Title:       string(row.Components["CUDA"]), //TODO
				PodId:       pod.ID,
				PodUuid:     pod.Uuid,
				ImageName:   row.Key,
				ImageTag:    row.Tag,
				AuditStatus: enums.ImageAuditStatusEnum.AuditPass,
				Status:      1,
				StorageMode: enums.ImageStorageModeEnum.Registry,
			}

			err = podImage.Save()
			if err != nil {
				msg = "市场保存镜像信息失败"
				return
			}
		} else if err != nil {
			msg = "查询算力市场镜像信息失败"
			return
		}

		instanceTitle = pod.Title
	} else if podImage.ID > 0 {

		//if podImage.LastSaveTime.Before(common.DefaultTime.Add(time.Hour)) {
		//	msg = "该镜像还没保存成功"
		//	return
		//}
		//if (podImage.ImageType == enums.ImageTypeEnum.Private || podImage.ImageType == enums.ImageTypeEnum.PrivateInstance) && podImage.AuditStatus != enums.ImageAuditStatusEnum.PushSuccess {
		//	msg = "该镜像还没保存成功"
		//	return
		//}
		if podImage.ImageType == enums.ImageTypeEnum.Private && podImage.Title != "" {
			instanceTitle = podImage.Title
		}
		if podImage.ImageType == enums.ImageTypeEnum.Private && podImage.UserId != claimsUserId {
			//checkStr := podImage.Share
			//if !strings.Contains(checkStr, fmt.Sprintf("|%s|", user.Mobile)) && !strings.Contains(checkStr, fmt.Sprintf("|%s|", user.DisplayName)) {
			//	msg = "该镜像不是您的私有镜像，无法创建"
			//	logger.Error(msg, utils.GetJsonFromStruct(oReq))
			//	return
			//}
		}
	}

	if podImage.PodId > 0 {
		if podImage.PodId != pod.ID {
			msg = "Pod镜像不匹配"
			logger.Error(msg)
			return
		}
	}

	if user.Free != 1 { //1不做金额检测
		if oReq.ChargingType == enums.ChargingTypeEnum.Day || oReq.ChargingType == enums.ChargingTypeEnum.Week || oReq.ChargingType == enums.ChargingTypeEnum.Month {
			msg = "目前不支持该计费方式"
			logger.Error(msg, " oReq:", utils.GetJsonFromStruct(oReq))
			return
		}
		if oReq.ChargingType == enums.ChargingTypeEnum.Usage {
			validAmount := user.Amount
			var card model.Card
			if leaveAmount, err := card.CardValidAmount(claimsUserId, pod.ID); err != nil {
				logger.Error("CardValidAmount   ", "err:", err, " leaveAmount:", leaveAmount.String(), " userId:", claimsUserId, "   podId:", pod.ID)
			} else {
				logger.Info("CardValidAmount   ", "leaveAmount:", leaveAmount.String(), " userId:", claimsUserId, "   podId:", pod.ID)
				validAmount = validAmount.Add(leaveAmount)
			}

			if validAmount.LessThanOrEqual(decimal.Zero) {
				msg = "余额不足，请充值" //余额不足，Pod启动需要预存一个小时的余额，用于支付超时的算力和Pod服务费请充值
				msg = fmt.Sprintf("您当前的余额为%s，Pod启动需要至少预存一个小时的备用额度，用于支付可能超出的Pod服务费、镜像存储费、算力等。", user.Amount.Truncate(2).String())
				logger.Error(msg)
				code = 5 //余额不足，前端需要引导充值
				return
			}

			var countOfInstance model.Instance
			if count, err := countOfInstance.CountOfBootInOrRunning(claimsUserId); err != nil {
				logger.Error(err, " userId:", claimsUserId)
			} else {
				if count >= 1 {
					showValidAmount := validAmount
					for i := 0; i < count; i++ {
						validAmount = validAmount.Sub(decimal.NewFromInt(2))
						if validAmount.LessThan(decimal.Zero) {
							msg = fmt.Sprintf("已有%d个实例在运行中，您当前的有效余额为%s，不够再启动实例，请先充值或关闭运行中的实例。", count, showValidAmount.Truncate(2).String())
							logger.Error(msg)
							code = 5 //余额不足，前端需要引导充值
							return
						}
					}
				}
			}
		}
	}

	if oReq.ChargingType != enums.ChargingTypeEnum.Usage && oReq.ChargingType != enums.ChargingTypeEnum.Free {
		msg = "计费方式目前只市场按量或者免费"
		return
	}
	instance := model.Instance{
		Uuid:         utils.GetUUID(),
		UserId:       claimsUserId,
		PodId:        pod.ID,
		PodName:      pod.PodName,
		Category:     pod.Category,
		Title:        instanceTitle,
		ImageId:      podImage.ID,
		ImageType:    podImage.ImageType,
		ImageName:    podImage.ImageName,
		ImageTag:     podImage.ImageTag,
		ChargingType: oReq.ChargingType,
		ChargingNum:  0,
		Gpus:         oReq.Gpus,
		GpuModelId:   gpuModel.ID,
		GpuModelName: gpuModel.GpuName,
	}

	if ccm {
		instance.InstanceType = enums.InstanceTypeEnum.CCM
	}

	if oReq.ChargingType == enums.ChargingTypeEnum.Day || oReq.ChargingType == enums.ChargingTypeEnum.Week || oReq.ChargingType == enums.ChargingTypeEnum.Month {
		now := time.Now().Truncate(time.Second)
		endTime := service.CalculateEndTime(oReq.ChargingType, now, oReq.ChargingNum)
		instance.StartTime = now
		instance.EndTime = endTime

		price := gpuModel.CalculateAmount(oReq.ChargingType, oReq.ChargingNum, oReq.Gpus)
		if price.Cmp(decimal.Zero) == 0 {
			msg = "金额计算为零"
			logger.Error(msg)
			return
		}
		if user.Amount.LessThan(price) {
			msg = "余额不足，请充值"
			logger.Error(msg, user.ID, " ", price)
			return
		}

		if !price.Equal(oReq.Amount) {
			msg = "输入金额不一致"
			logger.Error(msg, oReq)
			return
		}
		instance.LastSettleAmount = price
		instance.LastSettleTime = now
	}

	if err := instance.Create(); err != nil {
		msg = "实例创建失败"
		logger.Error(msg, err, oReq)
		return
	} else {
		resp := instanceResp{
			StatusTxt: enums.InstanceStatusEnum.Name(instance.Status),
		}
		if err := utils.Scan(instance, &resp); err != nil {
			msg = "转换失败，请刷新"
			logger.Error(err)
			return
		}
		result["instance"] = resp
	}

	if err := pod.UpUsageCount(); err != nil {
		logger.Error(err, "podId:", pod.ID)
	}

	if ccm {
		if config.Env == enums.EnvEnum.PRODUCTION {
			//
		} else {
			oReq.NodeId = 2
		}

		if txt, err := service.InstanceNodeService.StartupPro(instance.Uuid, oReq.NodeId, oReq.VirtualId, oReq.NoCard); err != nil {
			msg = txt
			logger.Error(msg, err)
			return
		} else {
			msg = txt
		}
	} else {
		msg = "创建完成"
	}
	code = 0
}

func (obj *_instanceApi) Startup(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq instanceStartupReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err)
		msg = "参数获取失败"
		return
	}

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "获取实例信息失败"
		logger.Error(msg, err)
		return
	}

	var user model.User
	if err := user.GetById(instance.UserId); err != nil {
		msg = "用户信息获取失败"
		logger.Error(msg, err)
		return
	}

	if user.Free != 1 { //1不做金额检测
		if instance.ChargingType == enums.ChargingTypeEnum.Day || instance.ChargingType == enums.ChargingTypeEnum.Week || instance.ChargingType == enums.ChargingTypeEnum.Month {
			if instance.EndTime.Before(time.Now()) {
				msg = enums.ChargingTypeEnum.Name(instance.ChargingType) + "已到期，请续费后再启动"
				return
			}
		}
		if instance.ChargingType == enums.ChargingTypeEnum.Usage {
			if user.Amount.LessThan(decimal.Zero) {
				msg = "余额不足，请充值"
				logger.Error(msg)
				return
			}
		}
	}

	if user.ShortId == "" {
		if err := user.SetShortId(); err != nil {
			msg = "创建用户存储路径失败"
			logger.Error(msg, err)
			return
		}
	}

	if config.Env == enums.EnvEnum.PRODUCTION {
		if txt, err := service.InstanceNodeService.StartupPro(oReq.InstanceUuid, oReq.NodeId, oReq.VirtualId, oReq.NoCard); err != nil {
			msg = txt
			logger.Error(msg, err)
			return
		} else {
			msg = txt
		}

	} else {
		oReq.NodeId = 2
		if txt, err := service.InstanceNodeService.StartupPro(oReq.InstanceUuid, oReq.NodeId, oReq.VirtualId, oReq.NoCard); err != nil {
			msg = txt
			logger.Error(msg, err)
			return
		} else {
			msg = txt
		}
	}
	code = 0
}

func (obj *_instanceApi) StartupAbort(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 || msg == "开机已终止" {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq instanceStartupReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	logger.Info("接收到后台取消Pod开机操作 oReq:", utils.GetJsonFromStruct(oReq), " operatorUserId:", claims.UserId)
	defer func() {
		logger.Info("接收到后台取消Pod开机操作 oReq:", utils.GetJsonFromStruct(oReq), " Result:", utils.GetJsonFromStruct(result))
	}()

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "实例不存在"
		logger.Error(msg, err)
		return
	}

	if instance.Status != enums.InstanceStatusEnum.BootInProgress {
		msg = "实例不在开机中"
		return
	}

	if instance.StartupMark == "" || oReq.Force {
		if str, err := service.InstanceNodeService.StartupFail(instance.Uuid, instance.StartupMark, nil, "主动取消"); err != nil {
			msg = str
			logger.Error(err, str, instance.Uuid)
			return
		} else {
			msg = "开机已终止"
			code = 0
			return
		}
	}

	if ginH, err := service.NodeService.StartupAbort(instance.StartupNodeId, instance.StartupVirtualId, instance.StartupMark); err != nil {
		msg = err.Error()
		logger.Error(err, " ginH:", ginH)
	} else {
		c.JSON(http.StatusOK, ginH)
		code = 0
	}
}

func (obj *_instanceApi) Shutdown(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("Shutdown panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq instanceReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err)
		msg = "参数获取失败"
		return
	}

	//if oReq.Destroy == false {
	//	if oReq.Task == "" {
	//		msg = "关机后续任务不能为空"
	//		return
	//	}
	//}

	oReq.Destroy = true

	if txt, err := service.InstanceNodeService.Shutdown(oReq.InstanceUuid, enums.ShutdownReasonEnum.Manage, oReq.Destroy, oReq.Task); err != nil {
		msg = txt
		logger.Error(msg, err)
		return
	} else {
		msg = txt
	}
	code = 0
}

func (obj *_instanceApi) Restart(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("StartupLoop panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}

	}()
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		msg = "权限不足"
		return
	}

	var oReq instanceStartupReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err)
		msg = "参数获取失败"
		return
	}
	logger.Info("接收到后台重启操作 oReq:", utils.GetJsonFromStruct(oReq), " claimsUserId:", claims.UserId)
	defer func() {
		logger.Info("接收到后台重启操作 oReq:", utils.GetJsonFromStruct(oReq), " Result:", utils.GetJsonFromStruct(result))
	}()

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "获取实例信息失败"
		logger.Error(msg, err)
		return
	}

	if instance.Status != enums.InstanceStatusEnum.Running {
		msg = "实例不在运行中，不能进行重启操作"
		return
	}

	if ginH, err := service.InstanceNodeService.ReStart(oReq.InstanceUuid); err != nil {
		msg = err.Error()
		logger.Error(err, " ginH:", ginH)
	} else {
		c.JSON(http.StatusOK, ginH)
		code = 0
	}
}

func (obj *_instanceApi) SaveImage(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("StartupLoop panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		msg = "权限不足"
		return
	}

	var oReq instanceSaveImageByManagerReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err)
		msg = "参数获取失败"
		return
	}
	logger.Info("接收到后台镜像保存操作 oReq:", utils.GetJsonFromStruct(oReq), " claimsUserId:", claims.UserId)
	defer func() {
		logger.Info("接收到后台镜像保存操作 oReq:", utils.GetJsonFromStruct(oReq), " code:", code, " msg:", msg, " Result:", utils.GetJsonFromStruct(result))
	}()

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "获取实例信息失败"
		return
	}

	if instance.Status != enums.InstanceStatusEnum.Running {
		msg = "实例已经不在运行中，不能保存镜像"
		return
	}

	var podImage model.PodImage
	if err := podImage.GetByPodIdAndTag(instance.PodId, oReq.ImageTag); err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			msg = "查询镜像信息出错"
			logger.Error(msg, err)
			return
		}
	}

	if instance.InstanceType == enums.InstanceTypeEnum.Kol { //kol镜像
		if oReq.ImageTag == "" {
			msg = "KOL镜像请输入版本号"
			return
		}
		oReq.ImageTag = strings.TrimSpace(oReq.ImageTag)
		if strings.Contains(oReq.ImageTag, " ") {
			msg = "版本号不能带空格"
			return
		}
		if utils.IsVersion(oReq.ImageTag) == false {
			msg = "版本号必须以数字字母.和-组成"
			return
		}

		var pod model.Pod
		if err := pod.GetById(instance.PodId); err != nil {
			msg = "查询Pod信息出错"
			logger.Error(msg, err)
			return
		}

		if pod.InstanceUuid == "" {
			msg = "该Pod未启动实例"
			logger.Error(msg, pod.Uuid)
			return
		}

		lockKey := enums.RedisKeyEnum.LockKey + "instance_" + pod.InstanceUuid
		if common.RedisLock(lockKey, 1, 1000*60) {
			defer common.RedisUnLock(lockKey)

			// 未完成制作的镜像
			var unCompleteImage model.PodImage
			var ary = make([]model.PodImage, 0)
			if total, err := podImage.List(&ary, 0, instance.UserId, enums.ImageTypeEnum.Public, pod.ID, "", -1, 1, 100); err != nil {
				msg = "查询失败"
				logger.Error(msg, err)
				return
			} else {
				if total > 10 {
					msg = "最多只能有10个版本，请先删除不常用的版本"
				}
				for _, tmp := range ary {
					if tmp.AuditStatus != enums.ImageAuditStatusEnum.AuditPass {
						unCompleteImage = tmp
					}
				}
			}

			if unCompleteImage.ID > 0 && podImage.ID != unCompleteImage.ID {
				msg = fmt.Sprintf("版本[%s]正在编辑中，请输入该版本号覆盖该版本或者删除该版本后再提交", unCompleteImage.ImageTag)
				logger.Error(msg, oReq)
				return
			}

			if podImage.ID == 0 {
				parentId := uint(0)
				if instance.ImageType == enums.ImageTypeEnum.Base || instance.ImageType == enums.ImageTypeEnum.Public || instance.ImageType == enums.ImageTypeEnum.CCM {
					parentId = instance.ID
				}
				podImage = model.PodImage{
					Uuid:        utils.GetUUID(),
					ParentId:    parentId,
					UserId:      pod.UserId,
					ImageType:   enums.ImageTypeEnum.Public,
					PodId:       pod.ID,
					PodUuid:     pod.Uuid,
					ImageName:   pod.Uuid,
					ImageTag:    oReq.ImageTag,
					Remark:      oReq.KOLRemark,
					AuditStatus: enums.ImageAuditStatusEnum.Makeing,
					StorageMode: enums.ImageStorageModeEnum.Registry,
				}
				if err := podImage.Save(); err != nil {
					msg = "保存镜像信息失败"
					return
				}
			}

			// 是否满足镜像保存条件
			var satisfy bool
			if msg, satisfy = obj.savePodImageSatisfy(podImage); !satisfy {
				return
			}

			if oReq.KOLRemark != "" && oReq.KOLRemark != podImage.Remark {
				if err := podImage.SetRemark(oReq.KOLRemark); err != nil {
					msg = "保存镜像备注信息失败"
					return
				}
			}

			if err := instance.SetSaveImageId(podImage.ID); err != nil {
				msg = "修改保存镜像ID出错"
				logger.Error(msg, err)
				return
			}

			if err := podImage.SetAuditStatusToPushing(instance.StartupMark, instance.StartupVirtualId, instance.ID); err != nil {
				msg = "设置推送状态失败"
				logger.Error(msg, err)
				return
			}

			if ginH, err := service.NodeService.SaveImageDockerKol(instance.StartupNodeId, instance.StartupVirtualId, instance.StartupMark, podImage.ID); err != nil {
				msg = "发送保存指令失败"
				logger.Error(err)
				result["err"] = err.Error()
				return
			} else {
				if tmpH, err := service.ResultGinH(ginH); err != nil {
					msg = "解析出错"
					result["image_uuid"] = podImage.Uuid
				} else {
					code = tmpH.Code
					msg = tmpH.Msg
					result["image_uuid"] = podImage.Uuid
				}
				return
			}
		} else {
			msg = "正在保存中，请勿重复提交"
			return
		}
	} else { // 个人镜像保存
		if txt, err := service.InstanceNodeService.Shutdown(instance.Uuid, enums.ShutdownReasonEnum.Manage, false, ""); err != nil {
			msg = txt
			logger.Error(msg, err)
			return
		}
		code = 0
	}
}

func (obj *_instanceApi) SaveImageContinue(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		msg = "权限不足"
		return
	}

	var oReq saveImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	logger.Info("接收到后台继续保存镜像操作 oReq:", utils.GetJsonFromStruct(oReq), " claimsUserId:", claims.UserId)
	defer func() {
		logger.Info("接收到后台继续保存镜像操作 oReq:", utils.GetJsonFromStruct(oReq), " Result:", utils.GetJsonFromStruct(result))
	}()

	if oReq.ImageId == 0 {
		if oReq.ImageUuid != "" {
			var podImage model.PodImage
			if err := podImage.GetByUuid(oReq.ImageUuid); err != nil {
				msg = "获取镜像信息失败"
				return
			}
			oReq.ImageId = podImage.ID
			var instRecord model.InstRecord
			if podImage.CommitStartupMark != "" {
				if err := instRecord.GetByStartupMark(podImage.CommitStartupMark); err != nil {
					msg = "查询启动记录失败 err:" + err.Error()
					logger.Error(msg, err)
					return
				}
			} else {
				if err := instRecord.GetForContinueSaveImage(podImage.ID); err != nil {
					msg = "查询启动记录失败" + err.Error()
					logger.Error(msg, err)
					return
				}
			}

			var instance model.Instance
			if err := instance.GetById(instRecord.InstanceId); err != nil {
				msg = "获取实例信息失败"
				return
			} else {
				oReq.InstanceUuid = instance.Uuid
			}

		} else if oReq.InstanceUuid != "" {
			var instance model.Instance
			if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
				msg = "获取实例信息失败"
				return
			}
			oReq.ImageId = instance.SaveImageId
		}
	}
	if ginH, err := service.InstanceNodeService.SaveImageContinue(oReq.ImageId, oReq.InstanceUuid); err != nil {
		result["errInfo"] = err.Error()
		logger.Error(err)
		msg = err.Error()
	} else {
		code = 0
		if ginH == nil {
			c.JSON(http.StatusOK, "ginH is nil")
		} else {
			c.JSON(http.StatusOK, ginH)
		}
	}
}

func (obj *_instanceApi) SaveImageCreate(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})

	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		msg = "权限不足"
		return
	}

	var oReq saveImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	logger.Info("接收到后台创建保存镜像操作 oReq:", utils.GetJsonFromStruct(oReq), " claimsUserId:", claims.UserId)
	defer func() {
		logger.Info("接收到后台创建保存镜像操作 oReq:", utils.GetJsonFromStruct(oReq), " Result:", utils.GetJsonFromStruct(result))
	}()

	if oReq.StartupMark == "" {
		msg = "启动标记为空"
		return
	}

	var instRecord model.InstRecord
	if err := instRecord.GetByStartupMark(oReq.StartupMark); err != nil {
		msg = "查询启动记录失败 err:" + err.Error()
		logger.Error(msg)
		return
	}
	if instRecord.ShutdownTime.Before(common.NationalDay) {
		msg = "该实例还没关闭，不能创建个人镜像"
		return
	}

	pre := ""
	var instImage model.PodImage
	if err := instImage.GetById(instRecord.StartupImageId); err != nil {
		msg = "获取镜像信息失败"
		logger.Error(pre, msg, err, " instRecord.StartupImageId", instRecord.StartupImageId)
		return
	}
	var pod model.Pod
	if err := pod.GetById(instRecord.PodId); err != nil {
		logger.Error(pre, "查询Pod信息失败：err:", err, "  ", instRecord.PodId)
	}
	parentId := uint(0)
	if instImage.ImageType == enums.ImageTypeEnum.Base || instImage.ImageType == enums.ImageTypeEnum.Public || instImage.ImageType == enums.ImageTypeEnum.CCM {
		parentId = instImage.ID
	} else if instImage.ImageType == enums.ImageTypeEnum.Private || instImage.ImageType == enums.ImageTypeEnum.PrivateInstance {
		parentId = instImage.ParentId
	} else {
		msg = "未处理的镜像类型"
		err := errors.New(msg)
		logger.Error(pre, err, " instImageId:", instImage.ID)
	}

	imageUuid := utils.GetUUID()
	saveImageTag := service.GenPrivateImageTag(instRecord.UserId, instRecord.PodId, instRecord.ImageTag)
	if saveImageTag == "" {
		//msg = "生成新的镜像标签失败"
		//err := errors.New("生成新的镜像标签失败")
		//return msg, err
		saveImageTag = utils.GenSaveImageTag(instRecord.ImageTag)
		logger.Error("生成新的镜像标签失败", " instanceUuid:", instRecord.Uuid, " imageTag:", instRecord.ImageTag, " GenSaveImageTag: ", saveImageTag)
	}
	saveImageType := enums.ImageTypeEnum.Private
	saveImage := model.PodImage{
		Uuid:              imageUuid,
		ParentId:          parentId,
		UserId:            instRecord.UserId,
		ImageType:         saveImageType,
		InstanceId:        instRecord.InstanceId,
		StorageMode:       enums.ImageStorageModeEnum.Registry,
		PodId:             instRecord.PodId,
		PodUuid:           pod.Uuid,
		ImageName:         imageUuid,
		ImageTag:          saveImageTag,
		AuditStatus:       enums.ImageAuditStatusEnum.Pushing,
		CommitStartupMark: instRecord.StartupMark,
		CommitVirtualId:   instRecord.StartupVirtualId,
		CommitStartTime:   time.Now(),
	}
	if err := saveImage.Save(); err != nil {
		msg = "生成个人镜像失败 err:" + err.Error()
		logger.Error(msg)
		return
	} else {
		msg = "个人镜像创建成功"
		code = 0
		return
	}

}

func (obj *_instanceApi) SaveImageAbort(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		msg = "权限不足"
		return
	}

	var oReq saveImageCommitReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	logger.Info("接收到后台取消镜像保存操作 oReq:", utils.GetJsonFromStruct(oReq), " claimsUserId:", claims.UserId)
	defer func() {
		logger.Info("接收到后台取消镜像保存操作 oReq:", utils.GetJsonFromStruct(oReq), " Result:", utils.GetJsonFromStruct(result))
	}()

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "实例不存在"
		logger.Error(msg, err)
		return
	}

	newImageId := instance.SaveImageId
	if newImageId == 0 && instance.InstanceType == enums.InstanceTypeEnum.Kol {
		newImageId = instance.ImageId
	}

	if newImageId <= 0 {
		msg = "镜像ID不存在"
		return
	}

	var podImage model.PodImage
	if err := podImage.GetById(newImageId); err != nil {
		msg = "查询镜像信息出错"
		logger.Error(msg, err)
		return
	}

	if podImage.AuditStatus != enums.ImageAuditStatusEnum.Pushing && podImage.AuditStatus != enums.ImageAuditStatusEnum.Commiting {
		msg = "镜像不在推送中"
		return
	}

	if oReq.Force == false {
		if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.SaveImage, utils.Uint2String(podImage.ID)); err != nil {
			logger.Error(" err:", err)
			msg = "获取LogKey出错"
			return
		} else {
			if bOut, err := tasklog.OutTime(logKey, 60*5); err != nil {
				logger.Error(" err:", err)
				msg = "查询超时失败"
				return
			} else {
				if bOut {
				} else {
					logger.Info("正在推送中 logKey:", logKey)
					msg = "镜像推送日志在5分钟内"
					return
				}
			}
		}
	}

	if ginH, err := service.NodeService.SaveImageDockerAbort(instance.StartupNodeId, instance.StartupVirtualId, instance.StartupMark, newImageId, false); err != nil {
		result["errInfo"] = err.Error()
	} else {
		result["errInfo"] = utils.GetJsonFromStruct(ginH)
	}

	if err := podImage.GetById(newImageId); err != nil {
		msg = "查询镜像信息出错"
		logger.Error(msg, err)
		return
	}

	if podImage.AuditStatus != enums.ImageAuditStatusEnum.Pushing && podImage.AuditStatus != enums.ImageAuditStatusEnum.Commiting {
		msg = "镜像不在推送中"
		return
	}

	if err := podImage.SetAuditStatus(enums.ImageAuditStatusEnum.PushFail); err != nil {
		msg = "设置状态失败"
		return
	} else {
		msg = "本次镜像保存已取消"
		code = 0
		return
	}

}

func (obj *_instanceApi) SetNginx(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("Shutdown panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq instanceReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err)
		msg = "参数获取失败"
		return
	}
	if ginH, err := service.InstanceNodeService.SetNginx(oReq.InstanceUuid); err != nil {
		msg = err.Error()
		logger.Error(err, " ginH:", ginH)
	} else {
		c.JSON(http.StatusOK, ginH)
		code = 0
	}
}

func (obj *_instanceApi) BatchResetNginx(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("Shutdown panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})

	}()
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq instanceReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err)
		msg = "参数获取失败"
		return
	}
	if oReq.Pw != "3123" {
		msg = "口令不正确"
		logger.Error(msg, " pw:", oReq.Pw)
		return
	}
	commandKey := "HandleResetNginxProgress"
	if _, ok := service.TaskRunning.Load(commandKey); ok {
		msg = "批量重置实例映射，正在进行中，请无重复操作"
		logger.Error(msg)
		return
	}
	go func() {
		if err := service.Clear.HandleResetNginxProgress(); err != nil {
			logger.Error(err)
		}
	}()
	msg = "重置进行中"
	code = 0

}

func (obj *_instanceApi) RemoveNginx(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("Shutdown panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq instanceReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err)
		msg = "参数获取失败"
		return
	}

	if ginH, err := service.InstanceNodeService.RemoveNginx(oReq.InstanceUuid, oReq.Force); err != nil {
		msg = err.Error()
		logger.Error(err, " ginH:", ginH)
	} else {
		c.JSON(http.StatusOK, ginH)
		code = 0
	}
}

func (obj *_instanceApi) HandShutdown(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("Shutdown panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq handShutdownReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err)
		msg = "参数获取失败"
		return
	}

	var instance model.Instance
	var instRecord model.InstRecord
	if err := instRecord.GetByStartupMark(oReq.StartupMark); err != nil {
		logger.Error(err)
		msg = "查询实例记录失败" + err.Error()
		return
	}

	if instRecord.Status != enums.InstanceStatusEnum.ShutdownInProgress {
		msg = "InstRecord状态不正确"
		return
	}
	if err := instance.GetByStartupMark(oReq.StartupMark); err != nil {
		if err != gorm.ErrRecordNotFound {
			msg = "查询实例失败"
			return
		}
	} else {
		if instance.Status != enums.InstanceStatusEnum.ShutdownComplete && instance.Status != enums.InstanceStatusEnum.Hidden {
			msg = "实例状态不正确"
			return
		}
	}

	if err := service.InstanceNodeService.ShutdownInstRecord(instRecord.StartupMark, true); err != nil {
		msg = err.Error()
		return
	}
	msg = "操作完成"
	code = 0
}

func (obj *_instanceApi) LogsDocker(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq instanceReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err)
		msg = "参数获取失败"
		return
	}

	if oReq.InstanceUuid == "" {
		msg = "参数错误"
		return
	}

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "获取实例信息失败"
		logger.Error(msg, err, " instanceUuid:", oReq.InstanceUuid)
		return
	}

	if ginH, err := service.NodeService.LogsDocker(instance.StartupNodeId, instance.StartupVirtualId, instance.StartupMark); err != nil {
		msg = "访问节点获取信息失败"
		logger.Error(err)
		result["err"] = err.Error()
		return
	} else {
		code = 0
		c.JSON(http.StatusOK, ginH)
	}
}

func (obj *_instanceApi) MointorMetrics(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("Shutdown panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var metrics = make([]model.PrometheusLabel, 0)
	// 获取可用指标的列表
	for _, metric := range model.AllManagerLabels {
		metrics = append(metrics, metric)
	}

	result["data"] = metrics
	msg = "查询成功"
	code = 0

}

func (obj *_instanceApi) MointorData(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("StartupLoop panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		msg = "权限不足"
		return
	}

	var oReq instanceMonitorReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	for _, metric := range oReq.Metrics {
		if metric == "" {
			msg = "指标不能为空"
			return
		}
		// 判断指标是否存在
		if _, ok := model.AllManagerLabels[metric]; !ok {
			msg = "指标不存在"
			logger.Error(msg, metric)
			return
		}
	}

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "实例不存在"
		logger.Error(msg, err, oReq)
		return
	}

	if instance.StartTime.UnixMilli() < ************ {
		msg = "实例尚未启动，无法查询"
		return
	}

	if oReq.StartTime == 0 || oReq.StartTime < instance.StartTime.UnixMilli() {
		logger.Debug("查询开始时间小于启动时间，按照启动时间查询")
		oReq.StartTime = instance.StartTime.UnixMilli()
	}

	if oReq.EndTime == 0 || oReq.EndTime > instance.EndTime.UnixMilli() {
		// 如果结束时间是默认值 小于2000年01月01日00时00分00秒,或者处于正在运行中
		if instance.Status == enums.InstanceStatusEnum.Running || instance.EndTime.UnixMilli() < ************ {
			oReq.EndTime = time.Now().UnixMilli()
		} else {
			logger.Debug("查询结束时间大于结束时间，按照结束时间查询")
			oReq.EndTime = instance.EndTime.UnixMilli()
		}
	}

	var virtual model.Virtual
	if err := virtual.GetById(instance.StartupVirtualId); err != nil {
		msg = "实例不存在"
		logger.Error(msg, err, oReq)
		return
	}
	var gpus []int
	if err := utils.GetStructFromJson(&gpus, instance.StartupGpus); err != nil {
		logger.Error(err)
	}

	err, data := service.MonitorData(instance.Uuid, gpus, virtual.Host, oReq.Metrics, oReq.StartTime, oReq.EndTime, oReq.Step)
	if err != nil {
		msg = err.Error()
		logger.Error(msg)
		return
	}

	result["data"] = data
	msg = "查询成功"
	code = 0
}

func (obj *_instanceApi) savePodImageSatisfy(podImage model.PodImage) (string, bool) {
	if podImage.Status == 9 {
		return "该版本号已经使用过，请重新输入版本号", false
	}

	if podImage.AuditStatus == enums.ImageAuditStatusEnum.AuditPass {
		return "该版本已审核完成，请输入新的版本号", false
	}

	if podImage.AuditStatus == enums.ImageAuditStatusEnum.Pushing {
		return "推送中，请勿重复操作", false
	}
	return "", true
}

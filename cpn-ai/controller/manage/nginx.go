package manage

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"errors"
	"github.com/gin-gonic/gin"
	"net/http"
)

type nginxApi_ struct {
}

var NginxApi nginxApi_

type nginxReq struct {
	NodeId uint `json:"node_id"`

	Key          string `json:"key"`
	VirtualId    uint   `json:"virtual_id"`
	InstanceUuid string `json:"instance_uuid"`
	PodId        uint   `json:"pod_id"`
	Force        bool   `json:"force"`
}

type removeNginxReq struct {
	VirtualId    uint   `json:"virtual_id"`
	InstanceUuid string `json:"instance_uuid"`
	PodId        uint   `json:"pod_id"`
}

func (obj nginxApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq nginxReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if ginH, err := service.NodeService.ListNginx(oReq.NodeId); err != nil {
		msg = "获取映射列表失败"
		logger.Error(err, msg, "  ginH:", utils.GetJsonFromStruct(ginH))
		result["err"] = err.Error()
		return
	} else {
		c.JSON(http.StatusOK, ginH)
		code = 0
	}
}

func (obj nginxApi_) Get(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq nginxReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.Key == "" {
		msg = "参数为空"
		logger.Error(msg, "Key:", oReq.Key)
		return
	}

	if ginH, err := service.NodeService.GetNginx(oReq.NodeId, oReq.Key); err != nil {
		msg = "获取映射列表失败"
		logger.Error(err, msg, "  ginH:", utils.GetJsonFromStruct(ginH), " key:", oReq.Key)
		result["err"] = err.Error()
		return
	} else {
		c.JSON(http.StatusOK, ginH)
		code = 0
	}
}

func (obj *nginxApi_) RemoveInstance(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq nginxReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.InstanceUuid == "" {
		msg = "实例参数为空"
		logger.Error(msg)
		return
	}
	instanceUuid := oReq.InstanceUuid
	lockKey := enums.RedisKeyEnum.LockKey + "instance_" + instanceUuid
	if common.RedisLock(lockKey, 1, 1000*60) {
		defer common.RedisUnLock(lockKey)

		var instance model.Instance
		if err := instance.GetByUuid(instanceUuid); err != nil {
			msg = "获取实例信息失败"
			logger.Error(msg, err)
			return
		}

		if oReq.Force {

			if ginH, err := service.NodeService.RemoveInstanceNginx(instance.StartupVirtualId, instance.PodId, instance.Uuid); err != nil {
				logger.Error("instanceUuid:", instanceUuid, err, utils.GetJsonFromStruct(ginH))
				msg = "已移除实例Nginx映射失败"
				return
			} else {
				logger.Info("instanceUuid:", instanceUuid, "移除实例Nginx映射", utils.GetJsonFromStruct(ginH))
				code = 0
				msg = "已移除实例Nginx映射"
				return
			}

		} else {

			if instance.Status == enums.InstanceStatusEnum.ShutdownComplete || instance.Status == enums.InstanceStatusEnum.Hidden {
				if ginH, err := service.NodeService.RemoveInstanceNginx(instance.StartupVirtualId, instance.PodId, instance.Uuid); err != nil {
					logger.Error("instanceUuid:", instanceUuid, err, utils.GetJsonFromStruct(ginH))
					msg = "已移除实例Nginx映射失败"
					return
				} else {
					logger.Info("instanceUuid:", instanceUuid, "移除实例Nginx映射", utils.GetJsonFromStruct(ginH))
					code = 0
					msg = "已移除实例Nginx映射"
					return
				}
			} else {
				logger.Info("instanceUuid:", instanceUuid, "当前状态不移除实例Nginx映射 status:", instance.Status)
				msg = "移除的实例状态不支持"
				return
			}

		}

	} else {
		msg = "实例正在操作中，请勿重复操作"
		err := errors.New(msg)
		logger.Error(msg, err, instanceUuid)
		return
	}
}

package sdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
)

// Client 用于与 out api 通信
// BaseURL 形如 http://host:port/api/v1
// Token/ApiKey 由业务方传入
//
type Client struct {
	BaseURL string
	Token   string
	ApiKey  string
}

// NewClient 创建一个新的SDK客户端
func NewClient(baseURL, token, apiKey string) *Client {
	return &Client{
		BaseURL: baseURL,
		Token:   token,
		ApiKey:  apiKey,
	}
}

// BalanceRequest 余额请求体
// 实际参数如有不同请调整
//
type BalanceRequest struct {
	// 可根据实际API定义补充字段
}

// BalanceResponse 余额响应体
// 实际字段请根据API返回补充
//
type BalanceResponse struct {
	Balance float64 `json:"balance"`
	// 其他字段
}

// RechargeListRequest 充值记录请求体
// 与rechargeListReq一致
//
type RechargeListRequest struct {
	KW       string `json:"kw"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

// RechargeListItem 充值记录响应体
//
type RechargeListItem struct {
	CreatedAt  string  `json:"created_at"`
	PayTime    string  `json:"pay_time"`
	OutTradeNo string  `json:"out_trade_no"`
	Gateway    string  `json:"gateway"`
	PayChannel string  `json:"pay_channel"`
	Amount     float64 `json:"amount"`
}

type RechargeListResponse struct {
	Items []RechargeListItem `json:"items"`
	Total int                `json:"total"`
}

// BillListRequest 查询账单请求体
// 与balanceListReq一致
//
type BillListRequest struct {
	KW       string `json:"kw"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

// BillListItem 查询账单响应体
// 这里只做简单定义，具体字段可扩展
//
type BillListItem struct {
	OrderNo        string  `json:"order_no"`
	OccurredAmount float64 `json:"occurred_amount"`
	CardTxt        string  `json:"card_txt"`
	Show           string  `json:"show"`
}

type BillListResponse struct {
	Items []BillListItem `json:"items"`
	Total int            `json:"total"`
}

// PodListRequest 查询可用镜像请求体
// 与podReq一致
//
type PodListRequest struct {
	PodId    uint   `json:"pod_id"`
	PodUuid  string `json:"pod_uuid"`
	Kw       string `json:"kw"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

// PodListItem 查询可用镜像响应体
//
type PodListItem struct {
	Uuid      string `json:"uuid"`
	Title     string `json:"title"`
	ImageTags string `json:"image_tags"`
	ImageTag  string `json:"image_tag"`
	PodName   string `json:"pod_name"`
	Status    int    `json:"status"`
	StatusTxt string `json:"status_txt"`
	CreatedAt string `json:"created_at"`
}

type PodListResponse struct {
	Items []PodListItem `json:"items"`
	Total int           `json:"total"`
}

// InstanceCreateRequest 创建实例请求体
// 与instanceCreateReq一致
//
type InstanceCreateRequest struct {
	PodUuid      string `json:"pod_uuid"`
	ImageUuid    string `json:"image_uuid"`
	ImageTag     string `json:"image_tag"`
	GpuModelUuid string `json:"gpu_model_uuid"`
	AutoStart    int    `json:"auto_start"`
}

// InstanceResponse 创建实例响应体
//
type InstanceResponse struct {
	Instance map[string]interface{} `json:"instance"`
}

// InstanceStartRequest 启动/重启实例请求体
// 与instanceStartReq一致
//
type InstanceStartRequest struct {
	InstanceUuid string `json:"instance_uuid"`
}

// InstanceStopRequest 停止实例请求体
// 与instanceStopReq一致
//
type InstanceStopRequest struct {
	InstanceUuid   string `json:"instance_uuid"`
	ShutdownReason string `json:"shutdown_reason"`
}

// InstanceShutdownRegularTimeRequest 设置自动关机请求体
// 与instanceShutdownRegularTimeReq一致
//
type InstanceShutdownRegularTimeRequest struct {
	InstanceUuid string `json:"instance_uuid"`
	RegularTime  string `json:"regular_time"`
	Cancel       bool   `json:"cancel"`
	Save         bool   `json:"save"`
}

// InstanceStatusRequest 查询实例状态请求体
// 与instanceRenewalReq一致
//
type InstanceStatusRequest struct {
	InstanceUuid string `json:"instance_uuid"`
}

// InstanceStatusResponse 查询实例状态响应体
//
type InstanceStatusResponse struct {
	Instance map[string]interface{} `json:"instance"`
}

// InstanceListRequest 查询实例列表请求体
// 与instanceListReq一致
//
type InstanceListRequest struct {
	Status   int `json:"status"`
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
}

// InstanceListResponse 查询实例列表响应体
//
type InstanceListResponse struct {
	Items []map[string]interface{} `json:"items"`
	Total int                      `json:"total"`
}

// GpuModelsRequest 查询可用资源请求体
// 与gpuModelsReq一致
//
type GpuModelsRequest struct {
	PodUuid string `json:"pod_uuid"`
}

// GpuModelItem 查询可用资源响应体
//
type GpuModelItem struct {
	Uuid      string `json:"uuid"`
	Title     string `json:"title"`
	Desc      string `json:"desc"`
	Price     string `json:"price"`
	Remark    string `json:"remark"`
	Status    int    `json:"status"`
	StatusTxt string `json:"status_txt"`
	CreatedAt string `json:"created_at"`
	FreeTxt   string `json:"free_txt"`
}

type GpuModelsResponse struct {
	Items []GpuModelItem `json:"items"`
	Total int            `json:"total"`
}

// 通用POST方法
func (c *Client) post(endpoint string, req interface{}, resp interface{}) error {
	url := fmt.Sprintf("%s%s", c.BaseURL, endpoint)
	body, err := json.Marshal(req)
	if err != nil {
		return err
	}
	request, err := http.NewRequest("POST", url, bytes.NewBuffer(body))
	if err != nil {
		return err
	}
	request.Header.Set("Content-Type", "application/json")
	if c.Token != "" {
		request.Header.Set("Authorization", c.Token)
	}
	if c.ApiKey != "" {
		request.Header.Set("X-API-KEY", c.ApiKey)
	}
	respRaw, err := http.DefaultClient.Do(request)
	if err != nil {
		return err
	}
	defer respRaw.Body.Close()
	if respRaw.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status: %s", respRaw.Status)
	}
	respBody, err := ioutil.ReadAll(respRaw.Body)
	if err != nil {
		return err
	}
	return json.Unmarshal(respBody, resp)
}

// 余额
func (c *Client) GetBalance(req *BalanceRequest) (*BalanceResponse, error) {
	var resp struct {
		Code   int             `json:"code"`
		Msg    string          `json:"msg"`
		Result BalanceResponse `json:"result"`
	}
	err := c.post("/finances/balance", req, &resp)
	if err != nil {
		return nil, err
	}
	return &resp.Result, nil
}

// 充值记录
func (c *Client) GetRecharge(req *RechargeListRequest) (*RechargeListResponse, error) {
	var resp struct {
		Code   int                  `json:"code"`
		Msg    string               `json:"msg"`
		Result RechargeListResponse `json:"result"`
	}
	err := c.post("/finances/recharge", req, &resp)
	if err != nil {
		return nil, err
	}
	return &resp.Result, nil
}

// 账单
func (c *Client) GetBill(req *BillListRequest) (*BillListResponse, error) {
	var resp struct {
		Code   int              `json:"code"`
		Msg    string           `json:"msg"`
		Result BillListResponse `json:"result"`
	}
	err := c.post("/finances/bill", req, &resp)
	if err != nil {
		return nil, err
	}
	return &resp.Result, nil
}

// 查询可用镜像
func (c *Client) GetPods(req *PodListRequest) (*PodListResponse, error) {
	var resp struct {
		Code   int             `json:"code"`
		Msg    string          `json:"msg"`
		Result PodListResponse `json:"result"`
	}
	err := c.post("/app/list", req, &resp)
	if err != nil {
		return nil, err
	}
	return &resp.Result, nil
}

// 创建实例
func (c *Client) CreateInstance(req *InstanceCreateRequest) (*InstanceResponse, error) {
	var resp struct {
		Code   int              `json:"code"`
		Msg    string           `json:"msg"`
		Result InstanceResponse `json:"result"`
	}
	err := c.post("/app/instance/create", req, &resp)
	if err != nil {
		return nil, err
	}
	return &resp.Result, nil
}

// 停止实例
func (c *Client) StopInstance(req *InstanceStopRequest) error {
	var resp struct {
		Code   int    `json:"code"`
		Msg    string `json:"msg"`
		Result any    `json:"result"`
	}
	err := c.post("/app/instance/stop", req, &resp)
	if err != nil {
		return err
	}
	if resp.Code != 0 {
		return fmt.Errorf("stop instance failed: %s", resp.Msg)
	}
	return nil
}

// 启动实例
func (c *Client) StartInstance(req *InstanceStartRequest) error {
	var resp struct {
		Code   int    `json:"code"`
		Msg    string `json:"msg"`
		Result any    `json:"result"`
	}
	err := c.post("/app/instance/start", req, &resp)
	if err != nil {
		return err
	}
	if resp.Code != 0 {
		return fmt.Errorf("start instance failed: %s", resp.Msg)
	}
	return nil
}

// 重启实例
func (c *Client) RestartInstance(req *InstanceStartRequest) error {
	var resp struct {
		Code   int    `json:"code"`
		Msg    string `json:"msg"`
		Result any    `json:"result"`
	}
	err := c.post("/app/instance/restart", req, &resp)
	if err != nil {
		return err
	}
	if resp.Code != 0 {
		return fmt.Errorf("restart instance failed: %s", resp.Msg)
	}
	return nil
}

// 查询实例状态
func (c *Client) GetInstanceStatus(req *InstanceStatusRequest) (*InstanceStatusResponse, error) {
	var resp struct {
		Code   int                    `json:"code"`
		Msg    string                 `json:"msg"`
		Result InstanceStatusResponse `json:"result"`
	}
	err := c.post("/app/instance/status", req, &resp)
	if err != nil {
		return nil, err
	}
	return &resp.Result, nil
}

// 设置自动关机
func (c *Client) SetShutdownRegularTime(req *InstanceShutdownRegularTimeRequest) error {
	var resp struct {
		Code   int    `json:"code"`
		Msg    string `json:"msg"`
		Result any    `json:"result"`
	}
	err := c.post("/app/instance/scheduled/shutdown", req, &resp)
	if err != nil {
		return err
	}
	if resp.Code != 0 {
		return fmt.Errorf("set shutdown regular time failed: %s", resp.Msg)
	}
	return nil
}

// 查询实例列表
func (c *Client) GetInstances(req *InstanceListRequest) (*InstanceListResponse, error) {
	var resp struct {
		Code   int                  `json:"code"`
		Msg    string               `json:"msg"`
		Result InstanceListResponse `json:"result"`
	}
	err := c.post("/app/instance/list", req, &resp)
	if err != nil {
		return nil, err
	}
	return &resp.Result, nil
}

// 查询可用资源
func (c *Client) GetResources(req *GpuModelsRequest) (*GpuModelsResponse, error) {
	var resp struct {
		Code   int               `json:"code"`
		Msg    string            `json:"msg"`
		Result GpuModelsResponse `json:"result"`
	}
	err := c.post("/gpu/models", req, &resp)
	if err != nil {
		return nil, err
	}
	return &resp.Result, nil
}

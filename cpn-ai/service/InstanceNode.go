package service

import (
	"context"
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/model"
	"cpn-ai/service/tasklog"
	"cpn-ai/structs"
	"errors"
	"fmt"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type instanceNode_ struct {
}

var InstanceNodeService instanceNode_

func (obj *instanceNode_) StartupPro(instanceUuid string, nodeId uint, virtualId uint, noCard int) (string, error) {
	code := 1
	msg := ""
	//result := make(map[string]interface{})
	trace := fmt.Sprintf("开始启动实例%s nodeId:%d virtualId:%d noCard:%d", instanceUuid, nodeId, virtualId, noCard)
	logger.Info(trace)

	// 记录初始的nodeId参数
	logger.Info(fmt.Sprintf("StartupPro调用参数: instanceUuid=%s, 初始nodeId=%d, virtualId=%d, noCard=%d", instanceUuid, nodeId, virtualId, noCard))

	ctx := context.Background()
	if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupInstance, instanceUuid); err != nil {
		logger.Error(err)
	} else {
		ctx = context.WithValue(ctx, "logkey", logKey)
		ctx = context.WithValue(ctx, "task", tasklog.TaskEnum.StartupInstance)
		tasklog.Delete(logKey)
	}
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", trace, nil)

	//instKey := "inst:" + instanceUuid
	//StartupLog.Delete(instKey)
	if instanceUuid == "" {
		msg = "参数错误"
		logger.Error(trace, msg)
		return msg, errors.New(msg)
	}
	lockKey := enums.RedisKeyEnum.LockKey + "instance_" + instanceUuid
	if common.RedisLock(lockKey, 1, 1000*10) {
		defer common.RedisUnLock(lockKey)
		var instance model.Instance
		if err := instance.GetByUuid(instanceUuid); err != nil {
			msg = "获取实例信息失败"
			logger.Error(trace, msg, err)
			return msg, err
		}
		logger.Info(trace, "Instance:", utils.GetJsonFromStruct(instance))

		if instance.Status == enums.InstanceStatusEnum.Running {
			msg = "实例运行中,请勿重复操作"
			logger.Error(trace, msg)
			return msg, nil
		}

		if instance.ShutdownDestroy {
			msg = "该实例已做销毁处理，不能启动"
			logger.Error(trace, msg)
			return msg, nil
		}

		if instance.Status == enums.InstanceStatusEnum.BootInProgress {
			msg = fmt.Sprintf("当前实例实例状态为[%s]，不能做开机操作", enums.InstanceStatusEnum.Name(instance.Status))
			logger.Error(trace, msg)
			return msg, errors.New(msg)
		}

		b := instance.Status == enums.InstanceStatusEnum.Created || instance.Status == enums.InstanceStatusEnum.ShutdownComplete
		if !b {
			msg = fmt.Sprintf("当前实例实例状态为[%s]，请勿重复操作", enums.InstanceStatusEnum.Name(instance.Status))
			logger.Error(trace, msg)
			return msg, errors.New(msg)
		}

		var pod model.Pod
		if err := pod.GetById(instance.PodId); err != nil {
			msg = "获取Pod信息失败"
			logger.Error(trace, msg, pod.ID)
			return msg, err
		}
		if pod.Status == 0 {
			if pod.UserId != instance.UserId {
				msg = "该Pod暂时不可用"
				logger.Error(trace, msg, instance.ID)
				return msg, errors.New(msg)
			}
		}

		var user model.User
		if err := user.GetById(instance.UserId); err != nil {
			msg = "用户信息获取失败"
			logger.Error(trace, msg, err)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", msg, nil)
			return msg, err
		}

		userPath := ""
		if user.ShortId == "" {
			if err := user.SetShortId(); err != nil {
				msg = "创建用户存储路径失败"
				logger.Error(trace, msg, err)
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", msg, nil)
				return msg, err
			}
		}

		userPath = user.PrivateStorage
		if userPath == "" {
			msg = "用户文件存储路径为空"
			logger.Error(trace, msg)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", msg, nil)
			return msg, errors.New(msg)
		}

		if err := DirMakeSureByUserPod(user.PrivateStorage, pod.Uuid); err != nil {
			logger.Error("确认用户文件存储失败 err:", err)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "确认用户文件存储失败 err:"+err.Error(), nil)
		} else {
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "确认用户文件存储正常", nil)
		}
		//if pod.Command == "" {
		//	msg = "该Pod在配置中，暂时不可用"
		//	logger.Error(trace, msg, instance.ID)
		//	return msg, errors.New(msg)
		//}

		defer func() {
			if code == 0 {
				logger.Info("startup 回调 code=0 ", instanceUuid)
			} else {
				logger.Info("startup 回调 code=", code, "   msg:", msg, "   ", instanceUuid)
				if str, err := obj.StartupFail(instanceUuid, "", nil, msg); err != nil {
					logger.Error(trace, instanceUuid, err)
				} else {
					logger.Info(trace, instanceUuid, str)
				}
			}
		}()

		if err := instance.SetBootInProgress(noCard); err != nil {
			msg = "设置实例状态失败"
			logger.Error(trace, msg, err, instance.ID)
			return msg, err
		}

		if instance.SaveImageId > 0 {
			var tmpImage model.PodImage
			if err := tmpImage.GetById(instance.SaveImageId); err != nil {
				msg = "查询保存镜像信息失败"
				logger.Error(msg, err)
				return msg, errors.New(msg)
			}
			if tmpImage.AuditStatus == enums.ImageAuditStatusEnum.PushSuccess || tmpImage.AuditStatus == enums.ImageAuditStatusEnum.AuditPass {

			} else {
				msg = "有实例镜像正在保存中，请稍后操作"
				logger.Error(msg)
				return msg, errors.New(msg)
			}
		}

		var startupImage model.PodImage
		if instance.StartupImageId > 0 {
			if err := startupImage.GetById(instance.StartupImageId); err != nil {
				msg = "获取实例启动镜像失败"
				logger.Error(msg, err, " instanceUuid:", instance.Uuid)
				return msg, err
			}
		}
		if instance.StartupImageId == 0 {
			if err := instance.SetStartupImageId(instance.ImageId); err != nil {
				msg = "设置启动镜像失败"
				logger.Error(msg, err)
				return msg, err
			}
		} else if instance.InstanceType == enums.InstanceTypeEnum.Kol {
			logger.Info("Kol实例不需要设置启动镜像", instance.Uuid)
		} else if instance.SaveImageId > 0 && startupImage.ImageType != enums.ImageTypeEnum.Private {

			var privateInstanceImage model.PodImage
			if err := privateInstanceImage.GetPrivateInstanceImage(instance.ID); err != nil {
				if err == gorm.ErrRecordNotFound {
					//StartupImageModel = "PrivatePodImage"
				} else {
					msg = "获取个人实例镜像失败"
					logger.Error(msg, err, " instanceUuid:", instance.Uuid)
				}
			}

			var privatePodImage model.PodImage
			if instance.SaveImageId > 0 {
				if err := privatePodImage.GetById(instance.SaveImageId); err != nil {
					if err == gorm.ErrRecordNotFound {
						msg = "个人镜像信息已删除,请销毁该实例"
					} else {
						msg = "获取个人镜像信息失败"
					}
					logger.Error(msg, err)
					return msg, errors.New(msg)
				}
			} else {
				if err := privatePodImage.GetPrivatePodImage(instance.UserId, instance.PodId, instance.ImageTag); err != nil {
					if err == gorm.ErrRecordNotFound {

					} else {
						msg = "获取个人POD镜像失败"
						logger.Error(msg, err, " instanceUuid:", instance.Uuid)
						return msg, err
					}
				}
			}

			if privatePodImage.ID > 0 && privateInstanceImage.ID > 0 {
				if privatePodImage.LastSaveTime.After(privateInstanceImage.LastSaveTime) {
					privateInstanceImage.ID = 0
				} else {
					privatePodImage.ID = 0
				}
			}

			if privateInstanceImage.ID > 0 && privatePodImage.ID == 0 {
				if privateInstanceImage.AuditStatus == enums.ImageAuditStatusEnum.PushSuccess || privateInstanceImage.AuditStatus == enums.ImageAuditStatusEnum.AuditPass {
					if err := instance.SetStartupImageId(privateInstanceImage.ID); err != nil {
						msg = "设置个人实例镜像失败"
						logger.Error(msg, err)
						return msg, err
					}
				} else {
					msg = "个人实例镜像正在保存.请稍后再试"
					return msg, errors.New(msg)
				}
			} else if privatePodImage.ID > 0 && privateInstanceImage.ID == 0 {
				if privatePodImage.AuditStatus == enums.ImageAuditStatusEnum.PushSuccess || privatePodImage.AuditStatus == enums.ImageAuditStatusEnum.AuditPass {
					if err := instance.SetStartupImageId(privatePodImage.ID); err != nil {
						msg = "设置个人POD镜像失败"
						logger.Error(msg, err)
						return msg, err
					}
				} else {
					msg = "个人POD镜像正在保存.请稍后再试"
					return msg, errors.New(msg)
				}
			}
		}

		if instance.StartupImageId > 0 {
			var podImage model.PodImage
			if err := podImage.GetById(instance.StartupImageId); err != nil {
				logger.Error(trace, "获取启动镜像信息失败", err)
			} else {
				if podImage.Status == 9 {
					msg = "该镜像版本已经删除，请选择版本重新创建，本实例请做销毁处理"
					logger.Error(trace, msg)
					return msg, errors.New(msg)
				}
				if podImage.ImageType == enums.ImageTypeEnum.Private && podImage.UserId != instance.UserId {
					checkStr := podImage.Share
					if !strings.Contains(checkStr, fmt.Sprintf("|%s|", user.Mobile)) && !strings.Contains(checkStr, fmt.Sprintf("|%s|", user.DisplayName)) {
						msg = "该镜像不是您的私有镜像，无法启动"
						logger.Error(trace, msg)
						return msg, errors.New(msg)
					}
				}
				if err := podImage.SetLastUseTime(); err != nil {
					logger.Error(trace, "设置镜像最后使用时间失败", err)
				}
				if podImage.ImageType == enums.ImageTypeEnum.PrivateInstance || true {
					if podImage.LayerCount >= 120 {
						msg = "该镜像层数已达上限，请到我的镜像中使用[合并层]功能压缩层数后再使用"
						return msg, errors.New(msg)
					}
				}
				if podImage.ImageType == enums.ImageTypeEnum.Private && podImage.UserId == instance.UserId {
					if podImage.LayerCount >= 120 {
						msg = "限于容器机制，该镜像层数已达上限，请先在左边我的镜像中使用[合并层]功能压缩层数"
						return msg, errors.New(msg)
					}
				}
			}
		}

		if virtualId > 0 {
			logger.Info(fmt.Sprintf("指定了virtualId=%d，需要从virtual表获取nodeId", virtualId))
			var virtual model.Virtual
			if err := virtual.GetById(virtualId); err != nil {
				msg = "获取虚拟机信息失败"
				logger.Error(trace, msg, err)
				return msg, err
			}
			oldNodeId := nodeId
			nodeId = virtual.NodeId
			logger.Info(fmt.Sprintf("从virtual表获取nodeId: 原nodeId=%d, 新nodeId=%d", oldNodeId, nodeId))
		}
		/**
		config.KolNode g改成通过redis获取enums.RedisKeyEnum.KOLStartNode，如果没有就设置默认值8
		*/
		if instance.InstanceType == enums.InstanceTypeEnum.Kol {
			logger.Info(fmt.Sprintf("检测到KOL实例类型，需要设置专用nodeId"))
			kolNodeKey := enums.RedisKeyEnum.KOLStartNode
			nodeIdStr, err := common.RedisGet(kolNodeKey)
			if err != nil || nodeIdStr == "" {
				nodeIdStr = "10"
				common.RedisSet(kolNodeKey, nodeIdStr, 0)
				logger.Info(fmt.Sprintf("Redis中没有KOL节点配置，设置默认值: %s", nodeIdStr))
			} else {
				logger.Info(fmt.Sprintf("从Redis获取KOL节点配置: %s", nodeIdStr))
			}
			kolStartNode, _ := strconv.ParseUint(nodeIdStr, 10, 10)
			oldNodeId := nodeId
			nodeId = uint(kolStartNode)
			logger.Info(fmt.Sprintf("KOL实例nodeId设置: 原nodeId=%d, 新nodeId=%d", oldNodeId, nodeId))
		}

		var node model.Node
		var ary = make([]model.Node, 0)

		// 记录灰度测试节点选择逻辑
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("Pod灰度测试配置: AllowGrayTest=%d", pod.AllowGrayTest), nil)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("查询节点参数: nodeId=%d, virtualId=%d, status=1, allowGrayTest=%d", nodeId, virtualId, pod.AllowGrayTest), nil)

		// 记录KOL实例的特殊处理逻辑
		if instance.InstanceType == enums.InstanceTypeEnum.Kol {
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("KOL实例类型，nodeId可能被重新设置"), nil)
		}

		if _, err := node.ListWithPriorityAndRandom(&ary, nodeId, 1, pod.AllowGrayTest, 1, 10000); err != nil {
			msg = "查询节点失败"
			logger.Error(trace, msg, err)
			return msg, err
		}

		// 记录查询到的节点信息
		grayTestNodes := 0
		normalNodes := 0
		nodeDetails := make([]string, 0)
		for _, n := range ary {
			nodeDetails = append(nodeDetails, fmt.Sprintf("节点%d(is_gray_test=%d,status=%d)", n.ID, n.IsGrayTest, n.Status))
			if n.IsGrayTest == 1 {
				grayTestNodes++
			} else {
				normalNodes++
			}
		}
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("查找到%d个节点，其中灰度节点%d个，生产节点%d个", len(ary), grayTestNodes, normalNodes), nil)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("节点详情: %s", strings.Join(nodeDetails, ", ")), nil)

		firstVirtualId := uint(0)
		firstNodeIndex := -1

		if virtualId == 0 && instance.StartupNodeId > 0 && instance.StartupTime.After(common.NationalDay) {
			var firstVirtual model.Virtual
			if err := firstVirtual.GetById(instance.StartupVirtualId); err != nil {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("查找上次启动成功的节点虚拟机失败 nodeId:%d virtualId:%d err:%s", instance.StartupNodeId, instance.StartupVirtualId, err.Error()), nil)
			} else if firstVirtual.Status == 1 {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("查找上次启动成功的节点:%d", instance.StartupNodeId), nil)
				for i := 0; i < len(ary); i++ {
					if ary[i].ID == firstVirtual.NodeId {
						firstNodeIndex = i
						firstVirtualId = instance.StartupVirtualId
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("找到上次启动成功的节点:%d  firstVirtualId：%d", instance.StartupNodeId, firstVirtualId), nil)
						break
					}
				}
			} else {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("查找上次启动成功的节点虚拟机状态不符合 nodeId:%d virtualId:%d status:%d", instance.StartupNodeId, instance.StartupVirtualId, firstVirtual.Status), nil)
			}

		}

		var nums []int
		if firstNodeIndex >= 0 {
			nums = append(nums, firstNodeIndex)
		}
		for idx, _ := range ary {
			if firstNodeIndex >= 0 && firstNodeIndex == idx {

			} else {
				nums = append(nums, idx)
			}
		}

		for j := 0; j < len(nums); j++ {
			tmpNode := ary[nums[j]]
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, fmt.Sprintf("开始检测第%d个节点", j), fmt.Sprintf("nodeId:%d", tmpNode.ID), nil)
			if tmpNode.Status != 1 {
				if nodeId == 0 { //指定了节点不做无效判断
					msg = "节点状态无效"
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, fmt.Sprintf("第%d个节点状态无效，不符合", j), fmt.Sprintf("nodeId:%d", tmpNode.ID), nil)
					continue
				}
			}

			if instance.InstanceType == enums.InstanceTypeEnum.Kol {
				// 对于KOL类型实例，确保只选择状态正常的虚拟机
				if virtualId > 0 {
					var virtual model.Virtual
					if err := virtual.GetById(virtualId); err != nil {
						msg = "获取虚拟机信息失败"
						logger.Error(trace, msg, err)
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, fmt.Sprintf("KOL实例获取指定虚拟机失败", j), fmt.Sprintf("virtualId:%d", virtualId), nil)
						continue
					}
					if virtual.Status != 1 {
						msg = "KOL实例指定的虚拟机状态无效"
						logger.Error(trace, msg)
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, fmt.Sprintf("KOL实例指定的虚拟机状态无效", j), fmt.Sprintf("virtualId:%d status:%d", virtualId, virtual.Status), nil)
						continue
					}
				}
			}

			if tmpNode.ApiBaseUrl == "" {
				msg = "节点未配置地址"
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, fmt.Sprintf("第%d个节点未配置节点地址，不符合", j), fmt.Sprintf("nodeId:%d", tmpNode.ID), nil)
				continue
			}
			if tmpNode.FreeGpus < instance.Gpus {
				msg = "节点空闲Gpu数量不足"
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, fmt.Sprintf("第%d个节点空闲Gpu数量不足，不符合", j), fmt.Sprintf("nodeId:%d", tmpNode.ID), nil)

				//continue
			}

			apiBaseUrl := tmpNode.ApiBaseUrl
			//if tmpNode.ID == 16 { //gz01.chenyu.cn
			//	apiBaseUrl = fmt.Sprintf("https://node%d.%s/", tmpNode.ID, tmpNode.Domain)
			//}

			postUrl := apiBaseUrl + "api/node/docker/startup"
			postData := make(map[string]interface{})
			postData["instance_uuid"] = instance.Uuid
			if firstNodeIndex >= 0 && tmpNode.ID == ary[firstNodeIndex].ID {
				postData["first_virtual_id"] = firstVirtualId
			}
			postData["virtual_id"] = virtualId
			postData["pod_id"] = pod.ID
			postData["image_id"] = instance.StartupImageId
			postData["gpu_model_id"] = instance.GpuModelId
			if noCard == 1 {
				postData["need_gpus"] = 0
			} else {
				postData["need_gpus"] = instance.Gpus
			}
			postData["user_path"] = userPath

			startupParm := make(map[string]interface{})
			startupParm["instance_type"] = instance.InstanceType
			startupParm["user_id"] = instance.UserId
			postData["startup_parm"] = startupParm

			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, fmt.Sprintf("第%d个节点启动请求参数", j), utils.GetJsonFromStruct(startupParm), nil)
			mm, _ := PostNode(tmpNode.ID, postUrl, postData)
			if ginH, err := Result(mm); err != nil {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, fmt.Sprintf("第%d个节点请求启动接口失败", j), fmt.Sprintf("nodeId:%d err:%s", tmpNode.ID, err.Error()), nil)

				msg = "发送启动请求出错"
				logger.Error(trace, msg, fmt.Sprintf("实例：%s 在节点：%d 启动出错，尝试下一个节点", instance.Uuid, tmpNode.ID), err)
				continue
				//return msg, errors.New(err.Error())
			} else {
				if ginH.Code == 0 {
					startupVirtualId := uint(0)
					startupMark := ""
					startupGpus := ""
					logger.Info("result:", utils.GetJsonFromStruct(ginH.Result))
					if val, ok := ginH.Result["virtual_id"]; ok {
						startupVirtualId = uint(val.(float64))
					}
					if val, ok := ginH.Result["startup_mark"]; ok {
						startupMark = val.(string)
					}
					if val, ok := ginH.Result["startup_gpus"]; ok {
						startupGpus = utils.GetJsonFromStruct(val)
					}
					if startupVirtualId == 0 || startupMark == "" {
						msg = "未获取到启动标识"
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, fmt.Sprintf("第%d个节点请求未返回启动标志", j), fmt.Sprintf("nodeId:%d", tmpNode.ID), nil)

						logger.Error(trace, fmt.Sprintf("实例：%s 在节点：%d 启动失败，尝试下一个节点 ginH:", instance.Uuid, tmpNode.ID), ginH)
						continue
					}

					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, fmt.Sprintf("第%d个节点请求启动成功", j), fmt.Sprintf("nodeId:%d  startupVirtualId:%d startupMark:%s", tmpNode.ID, startupVirtualId, startupMark), nil)

					logger.Info("设置startupMark", instance.Uuid, "   ", startupMark)
					if err := instance.SetStartupMark(startupMark, startupGpus, tmpNode.ID, startupVirtualId); err != nil {
						msg = "设置启动标志失败"
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, fmt.Sprintf("第%d个节点设置启动标志失败", j), fmt.Sprintf("nodeId:%d  startupVirtualId:%d startupMark:%s", tmpNode.ID, startupVirtualId, startupMark), nil)

						logger.Error(trace, "设置启动标志失败", err, mm)
						return msg, err
					} else {
						code = 0
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, fmt.Sprintf("第%d个节点设置启动标志成功,启动中", j), fmt.Sprintf("nodeId:%d  startupVirtualId:%d startupMark:%s", tmpNode.ID, startupVirtualId, startupMark), nil)

						logger.Info(trace, "启动中 startupMark:", startupMark)
						return "启动中", nil
					}
				} else {
					msg = "发送启动请求失败"
					if ginH.Msg != "" {
						msg = ginH.Msg
					}
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, fmt.Sprintf("第%d个节点请求启动接口失败", j), fmt.Sprintf("nodeId:%d ginHMsg:%s", tmpNode.ID, ginH.Msg), nil)

					logger.Error(trace, fmt.Sprintf("实例：%s 在节点：%d 启动失败，尝试下一个节点 ginH:", instance.Uuid, tmpNode.ID), ginH)
					continue
				}
			}
		}
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, fmt.Sprintf("%d个节点检尝试完成，未找到可用节点", len(nums)), "", nil)
		if msg == "" {
			msg = "启动失败"
		}
		logger.Error(trace, msg, "code:", code)
		return msg, errors.New("启动失败，请稍后重试")
	} else {
		msg = "实例正在启动中,请稍后操作"
		logger.Error(trace, msg, instanceUuid, "   ", code)
		return msg, errors.New(msg)
	}
}

func (obj *instanceNode_) StartupLoop(instanceId uint) (bool, map[string]interface{}, error) {
	//status := -1
	defer func() {
		if e := recover(); e != nil {
			logger.Error("StartupLoop奔溃:", e, " instanceId:", instanceId)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("StartupLoop panicked:", e, "\nStack Trace:\n", string(stack), " instanceId:", instanceId)
		}
	}()
	var instance model.Instance
	if err := instance.GetById(instanceId); err != nil {
		logger.Error(err)
		if err == gorm.ErrRecordNotFound {
			return false, nil, err
		}
		return true, nil, err
	}
	if instance.StartupMark == "" {
		return false, nil, errors.New("startupmark为空")
	}
	if instance.Status != enums.InstanceStatusEnum.BootInProgress {
		return false, nil, errors.New("不是启动中状态")
	}
	pre := "instanceUuid:" + instance.Uuid

	if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, instance.StartupMark); err != nil {
		logger.Error(pre, err)
	} else {
		if logItem, _, err := tasklog.Last(logKey); err != nil {
			logger.Error(pre, err)
		} else {
			if logItem.State == tasklog.NormalTaskStateEnum.Success {
				if docker, state, _, err := NodeService.GetDockerDetail(instance.StartupVirtualId, instance.StartupMark); err != nil {
					msg := "获取docker详情失败"
					logger.Error(msg, err)
					return true, nil, err
				} else {
					if state == enums.DockerStatusEnum.NotExist {
						msg := "确定docker不存在，执行启动失败逻辑"
						logger.Error(msg, instanceId, "  ", instance.StartupMark)
						str, err := obj.StartupFail(instance.Uuid, instance.StartupMark, nil, "容器不存在")
						if err != nil {
							logger.Error(err, str, "设置失败出错")
							return false, nil, err
						} else {
							return false, nil, nil
						}
					}

					str, err := obj.StartupSuccess(instance.StartupMark, docker)
					if err != nil {
						logger.Error(err, str, "设置成功出错")
						return false, nil, err
					} else {
						return false, nil, nil
					}
				}
			} else if logItem.State == tasklog.NormalTaskStateEnum.Fail {
				if str, err := obj.StartupFail(instance.Uuid, instance.StartupMark, nil, "Fail"); err != nil {
					logger.Error(err, str, "设置失败出错")
					return false, nil, err
				} else {
					return false, nil, nil
				}
			}
		}
	}
	return false, nil, errors.New("暂时不启用该函数")
}

func (obj *instanceNode_) StartupFail(instanceUuid string, startupMark string, docker *Docker, reason string) (string, error) {
	//code := 1
	msg := ""
	//result := make(map[string]interface{})

	if docker == nil {
		docker = &Docker{}
	}

	if instanceUuid == "" {
		msg = "参数错误"
		logger.Error(msg, instanceUuid)
		return msg, errors.New(msg)
	}

	lockKey := enums.RedisKeyEnum.LockKey + "instance_startup_fail_" + instanceUuid
	logger.Info("StartupFail instanceUuid:", instanceUuid)

	if common.RedisLock(lockKey, 1, 1000*60) {
		defer common.RedisUnLock(lockKey)

		var instance model.Instance
		if err := instance.GetByUuid(instanceUuid); err != nil {
			msg = "获取实例信息失败"
			logger.Error(msg, err)
			return msg, err
		}

		if instance.Status != enums.InstanceStatusEnum.BootInProgress {
			msg = fmt.Sprintf("当前实例实例状态为[%s]，只有[%s]实例才能执行启动失败逻辑", enums.InstanceStatusEnum.Name(instance.Status), enums.InstanceStatusEnum.Name(enums.InstanceStatusEnum.BootInProgress))
			logger.Error(msg, instance.ID)
			return msg, errors.New(msg)
		}

		if startupMark == "" {

			if err := instance.SetStatus(instance.LastStatus); err != nil {
				msg = "更新实例状态失败"
				logger.Error(msg, err, instance.ID)
				return msg, err
			} else {
				//if str, err := NginxInstance(instance.Uuid, "remove"); err != nil {
				//	logger.Error("转发缓存移除失败", err, str)
				//	result["instance_forward"] = "转发缓存移除失败"
				//}
				msg = "已设置为启动失败"
				return msg, nil
			}
		}

		if startupMark != "" && instance.StartupMark != startupMark {
			msg = "启动标识不一致"
			logger.Error(msg, "curStartupMark:", instance.StartupMark, "   startupMark:", startupMark)
			return msg, errors.New(msg)
		}

		var pod model.Pod
		if err := pod.GetById(instance.PodId); err != nil {
			msg = "Pod不存在"
			logger.Error(msg, err, instance.ID)
			return msg, err
		}

		var node model.Node
		if err := node.GetById(instance.StartupNodeId); err != nil {
			msg = "节点数据不存在"
			logger.Error(err)
			return msg, err
		}

		startupMaps := ""

		virtualHost := docker.VirtualHost
		if virtualHost == "" && docker.VirtualId > 0 {
			var virtualTmp model.Virtual
			if err := virtualTmp.GetById(docker.VirtualId); err != nil {
				logger.Error(err)
			} else {
				virtualHost = virtualTmp.Host
			}
		}
		mapName := make(map[string]string)
		if pod.PortMaps != "" {
			ary := make([]structs.PortMap, 0)
			if err := utils.GetStructAryFromJson(&ary, pod.PortMaps); err != nil {
				msg = "数据解析失败"
				logger.Error(msg, err, pod.PortMaps)
				//return msg, err
			}
			for i := 0; i < len(ary); i++ {
				//outKey := docker.StartupMark + ary[i].HostPort
				//outKey := docker.InstanceUuid + ary[i].HostPort
				outKey := instance.Uuid + ary[i].HostPort
				url := fmt.Sprintf("https://%s.%s", outKey, node.Domain)

				title := "Web" + ary[i].HostPort
				if ary[i].HostPort == "89" {
					title = "Jupyter"
					url += "/lab?token=Zeyun1234"
				} else if ary[i].HostPort == "87" {
					title = "晨羽云端OS"
				} else if ary[i].HostPort == "88" {
					title = "WebUI"
				} else if ary[i].HostPort == "33" {
					title = "Api"
				} else if ary[i].Title != "" {
					title = ary[i].Title
				}
				mapName[ary[i].HostPort] = title
			}
		}
		{

			aryStartup := make([]structs.StrarupPortMap, 0)
			for i := 0; i < len(docker.MapPorts); i++ {
				//outKey := docker.StartupMark + docker.MapPorts[i]
				//outKey := docker.InstanceUuid + docker.MapPorts[i]
				outKey := instance.Uuid + docker.MapPorts[i]
				url := fmt.Sprintf("https://%s.%s", outKey, node.Domain)

				title := "Web" + docker.MapPorts[i]
				if docker.MapPorts[i] == "89" {
					title = "Jupyter"
					url += "/lab?token=Zeyun1234"
				} else if docker.MapPorts[i] == "88" {
					title = "WebUI"
				} else if docker.MapPorts[i] == "87" {
					title = "晨羽云端OS"
				} else if docker.MapPorts[i] == "33" {
					title = "Api"
				} else {
					if _, ok := mapName[docker.MapPorts[i]]; ok {
						title = mapName[docker.MapPorts[i]]
					}
				}
				innerUrl := fmt.Sprintf("http://%s:%s0%s", virtualHost, docker.MapPref, docker.MapPorts[i])
				innerAt := int64(0)
				if utils.CheckUrl(innerUrl) {
					innerAt = time.Now().Unix()
				}

				aryStartup = append(aryStartup, structs.StrarupPortMap{Title: title, Url: url, InnerUrl: innerUrl, InnerAt: innerAt})
			}
			startupMaps = utils.GetJsonFromStruct(aryStartup)

		}

		logger.Info(instance.Uuid, " docker信息：", docker)

		var virtual model.Virtual
		if instance.StartupVirtualId > 0 {
			if err := virtual.GetById(instance.StartupVirtualId); err != nil {
				logger.Error(err)
			}
		}
		if err := instance.SetStatus2Fail(reason, node.ID, virtual.ID, utils.GetJsonFromStruct(docker.Gpus), docker.ID, docker.WebUrl, docker.ApiBase, startupMaps); err != nil {
			msg = "更新实例状态失败"
			logger.Error(msg, err, instance.ID)
			return msg, err
		} else {
			//if str, err := NginxInstance(instance.Uuid, "remove"); err != nil {
			//	logger.Error("转发缓存移除失败", err, str)
			//}
			msg = "已设置为启动失败"
			return msg, nil
		}

		//if err := instance.SetStatus(instance.LastStatus); err != nil {
		//	msg = "更新实例状态失败"
		//	logger.Error(msg, err, instance.ID)
		//	return msg, err
		//} else {
		//	if str, err := NginxInstance(instance.Uuid, "remove"); err != nil {
		//		logger.Error("转发缓存移除失败", err, str)
		//		result["instance_forward"] = "转发缓存移除失败"
		//	}
		//	msg = "已设置为启动失败"
		//	return msg, nil
		//}
	} else {
		msg = "实例正在开启中,请稍后操作"
		logger.Error(msg, instanceUuid)
		return msg, errors.New(msg)
	}
}
func (obj *instanceNode_) StartupSuccess(startupMark string, docker *Docker) (string, error) {
	//code := 1
	msg := ""
	//result := make(map[string]interface{})
	logger.Info("success docker:", utils.GetJsonFromStruct(*docker))
	if startupMark == "" {
		msg = "参数错误"
		logger.Error(msg, startupMark)
		return msg, errors.New(msg)
	}

	var instance model.Instance
	if err := instance.GetByStartupMark(startupMark); err != nil {
		msg = "获取实例信息失败"
		logger.Error(msg, err)
		return msg, err
	}

	if docker == nil {
		msg = "docker不存在"
		logger.Error(msg, instance.ID, "   ", startupMark)
		return msg, errors.New(msg)
	}

	var virtual model.Virtual
	if err := virtual.GetById(instance.StartupVirtualId); err != nil {
		logger.Error(err)
	}

	lockKey := enums.RedisKeyEnum.LockKey + "instance_startup_" + instance.Uuid
	if common.RedisLock(lockKey, 1, 1000*60) {
		defer common.RedisUnLock(lockKey)

		if err := instance.GetById(instance.ID); err != nil {
			msg = "获取实例信息失败"
			logger.Error(msg, err)
			return msg, err
		}

		if instance.Status != enums.InstanceStatusEnum.BootInProgress {
			msg = fmt.Sprintf("当前实例实例状态为[%s]，只有[%s]实例才能执行成功逻辑", enums.InstanceStatusEnum.Name(instance.Status), enums.InstanceStatusEnum.Name(enums.InstanceStatusEnum.BootInProgress))
			logger.Error(msg, instance.ID)
			return msg, errors.New(msg)
		}
		if instance.StartupMark != startupMark {
			msg = "启动标识不一致"
			logger.Error(msg, "curStartupMark:", instance.StartupMark, "   startupMark:", startupMark)
			return msg, errors.New(msg)
		}

		var pod model.Pod
		if err := pod.GetById(instance.PodId); err != nil {
			msg = "Pod不存在"
			logger.Error(msg, err, instance.ID)
			return msg, err
		}

		var node model.Node
		if err := node.GetById(instance.StartupNodeId); err != nil {
			msg = "节点数据不存在"
			logger.Error(err)
			return msg, err
		}

		startupMaps := ""
		virtualHost := docker.VirtualHost
		if virtualHost == "" && docker.VirtualId > 0 {
			var virtualTmp model.Virtual
			if err := virtualTmp.GetById(docker.VirtualId); err != nil {
				logger.Error(err)
			} else {
				virtualHost = virtualTmp.Host
			}
		}
		mapName := make(map[string]string)
		if pod.PortMaps != "" {
			ary := make([]structs.PortMap, 0)
			if err := utils.GetStructAryFromJson(&ary, pod.PortMaps); err != nil {
				msg = "数据解析失败"
				logger.Error(msg, err, pod.PortMaps)
				//return msg, err
			}
			for i := 0; i < len(ary); i++ {
				//outKey := docker.StartupMark + ary[i].HostPort
				//outKey := docker.InstanceUuid + ary[i].HostPort
				outKey := instance.Uuid + ary[i].HostPort
				url := fmt.Sprintf("https://%s.%s", outKey, node.Domain)

				title := "Web" + ary[i].HostPort
				if ary[i].HostPort == "89" {
					title = "Jupyter"
					url += "/lab?token=Zeyun1234"
				} else if ary[i].HostPort == "87" {
					title = "晨羽云端OS"
				} else if ary[i].HostPort == "88" {
					title = "WebUI"
				} else if ary[i].HostPort == "33" {
					title = "Api"
				} else if ary[i].Title != "" {
					title = ary[i].Title
				}
				mapName[ary[i].HostPort] = title
			}
		}
		{

			aryStartup := make([]structs.StrarupPortMap, 0)
			for i := 0; i < len(docker.MapPorts); i++ {
				//outKey := docker.StartupMark + docker.MapPorts[i]
				//outKey := docker.InstanceUuid + docker.MapPorts[i]
				outKey := instance.Uuid + docker.MapPorts[i]
				url := fmt.Sprintf("https://%s.%s", outKey, node.Domain)

				title := "Web" + docker.MapPorts[i]
				if docker.MapPorts[i] == "89" {
					title = "Jupyter"
					url += "/lab?token=Zeyun1234"
				} else if docker.MapPorts[i] == "88" {
					title = "WebUI"
				} else if docker.MapPorts[i] == "87" {
					title = "晨羽云端OS"
				} else if docker.MapPorts[i] == "33" {
					title = "Api"
				} else {
					if _, ok := mapName[docker.MapPorts[i]]; ok {
						title = mapName[docker.MapPorts[i]]
					}
				}
				innerUrl := fmt.Sprintf("http://%s:%s0%s", virtualHost, docker.MapPref, docker.MapPorts[i])
				innerAt := int64(0)
				if utils.CheckUrl(innerUrl) {
					innerAt = time.Now().Unix()
				}

				aryStartup = append(aryStartup, structs.StrarupPortMap{Title: title, Url: url, InnerUrl: innerUrl, InnerAt: innerAt})
			}
			startupMaps = utils.GetJsonFromStruct(aryStartup)

		}

		logger.Info(instance.Uuid, " docker信息：", docker)
		if err := instance.SetStatus2Running(virtual.NodeId, virtual.ID, utils.GetJsonFromStruct(docker.Gpus), docker.ID, docker.WebUrl, docker.ApiBase, startupMaps); err != nil {
			msg = "更新实例状态失败"
			logger.Error(msg, err, instance.ID)
			return msg, err
		} else {
			go func() {
				var podImage model.PodImage
				if err := podImage.GetById(instance.StartupImageId); err != nil {
					msg = "获取启动镜像信息失败"
					logger.Error(msg, err)
					return
				}
				if podImage.ImageType == enums.ImageTypeEnum.PrivateInstance {
					var instRecode model.InstRecord
					if aryLast, err := instRecode.ListByStartupImage(instance.UserId, instance.StartupImageId, 2); err != nil {
						logger.Error(err)
					} else {
						for i := 1; i < len(aryLast); i++ {
							if msg1, err := NodeService.ResetCacheShutdown(aryLast[i].StartupVirtualId, aryLast[i].StartupMark, common.CacheShutdownKeepSeconds); err != nil {
								logger.Error(msg1, err, aryLast[i].StartupMark)
							}
						}
					}
				}
			}()

			msg = "启动成功"
			return msg, nil
		}

	} else {
		msg = "实例正在启动中,请稍后操作"
		logger.Error(msg, instance.Uuid)
		return msg, errors.New(msg)
	}
}

// destroy 是否将状态值置为9
// task 关机后的任务
func (obj *instanceNode_) Shutdown(instanceUuid string, shutdownReason string, destroy bool, task string) (string, error) {
	code := 1
	msg := ""
	pre := fmt.Sprintf("InstanceNode.Shutdown instanceUuid:%s shutdownReason:%s  destroy:%t, task:%s", instanceUuid, shutdownReason, destroy, task)
	defer func() {
		if code == 1 && !strings.Contains(msg, "请勿重复操作") {
			go func() {
				var instance model.Instance
				if err := instance.GetByUuid(instanceUuid); err != nil {
					msg = "获取实例信息失败"
					logger.Error(pre, msg, err)
				} else {
					reason := "实例关闭失败"
					logger.Error("reason", reason, " msg:", msg, " instanceUuid:", instanceUuid, "  shutdownReason:", shutdownReason)
					if msg1, err1 := WarnService.InstanceRunning(instance.UserId, instance.Uuid, reason); err1 != nil {
						logger.Error(pre, "发送请求(实例运行提醒)失败 userId:", instance.UserId, "  uuid:", instance.Uuid, "  reason:", reason, "  msg:", msg1, " err:", err1)
					} else {
						logger.Info(pre, "发送请求(实例运行提醒)成功 userId:", instance.UserId, "  uuid:", instance.Uuid, "  reason:", reason)
					}
				}
			}()
		}
	}()

	logger.Info(pre)
	if instanceUuid == "" {
		msg = "参数错误"
		logger.Error(pre, msg, instanceUuid, code)
		return msg, errors.New(msg)
	}

	if destroy == false && task == "" {
		//msg = "task参数错误"
		//logger.Error(pre, msg)
		//return msg, errors.New(msg)
		task = tasklog.TaskEnum.SaveImageAndShutdown
	}

	if destroy == true && task == "" {
		task = tasklog.TaskEnum.ShutdownAndDestroy
	}

	if task == tasklog.TaskEnum.SaveImageAndDestroy {
		destroy = true
	} else if task == tasklog.TaskEnum.ShutdownAndDestroy {
		destroy = true
	} else if task == tasklog.TaskEnum.SaveInstanceImage {
		destroy = false
	} else if task == tasklog.TaskEnum.SaveImageAndShutdown {
		destroy = false
	} else if task == tasklog.TaskEnum.Shutdown {
		destroy = false
	} else {
		msg = "task参数错误"
		logger.Error(pre, msg)
		return msg, errors.New(msg)
	}

	var instance model.Instance
	startupMark := ""
	if err := instance.GetByUuid(instanceUuid); err != nil {
		msg = "获取实例信息失败"
		logger.Error(pre, msg, err, " ", instanceUuid)
		return msg, err
	}
	pre += fmt.Sprintf(" startupMark:%s ", instance.StartupMark)
	logger.Info(pre)
	if destroy == true && instance.Status != enums.InstanceStatusEnum.Running {
		logger.Info(pre, "实例不在运行中，走Destroy逻辑", instance.Uuid)
		code = 0
		return obj.Destroy(instanceUuid)
	}

	lockKey := enums.RedisKeyEnum.LockKey + "instance_" + instanceUuid
	if common.RedisLock(lockKey, 1, 0) {
		defer common.RedisUnLock(lockKey)
		// 再次获取instance信息
		if err := instance.GetByUuid(instanceUuid); err != nil {
			msg = "获取实例信息失败"
			logger.Error(pre, msg, err, " ", instanceUuid)
			return msg, err
		}

		if instance.Status == enums.InstanceStatusEnum.ShutdownComplete || instance.Status == enums.InstanceStatusEnum.Hidden {
			if ginH, err := NodeService.RemoveInstanceNginx(instance.StartupVirtualId, instance.PodId, instance.Uuid); err != nil {
				logger.Error(pre, err, utils.GetJsonFromStruct(ginH))
			} else {
				logger.Info(pre, "移除实例Nginx映射", utils.GetJsonFromStruct(ginH))
			}
		}

		//if destroy == false
		if task == tasklog.TaskEnum.SaveImageAndDestroy || task == tasklog.TaskEnum.SaveImageAndShutdown || task == tasklog.TaskEnum.SaveInstanceImage {

			if instance.InstanceType == enums.InstanceTypeEnum.Kol {
				msg = "KOL镜像只能做销毁操作"
				logger.Error(pre, msg)
				return msg, errors.New(msg)
			}
			if instance.Status != enums.InstanceStatusEnum.Running {
				msg = "当前实例不是运行状态，不能进行该操作"
				logger.Error(pre, msg)
				return msg, errors.New(msg)
			}
			if instance.ImageId == 0 {
				msg = "该实例POD不支持关机保存操作，请直接销毁"
				logger.Error(pre, msg)
				return msg, errors.New(msg)
			}
			if instance.SaveImageId > 0 {
				var savePodImage model.PodImage
				if err := savePodImage.GetById(instance.SaveImageId); err != nil {
					msg = "查询镜像信息失败"
					logger.Error(pre, msg, err, instance.SaveImageId)
					return msg, err
				}
				if savePodImage.Status < 9 {
					if savePodImage.AuditStatus == enums.ImageAuditStatusEnum.PushSuccess || savePodImage.AuditStatus == enums.ImageAuditStatusEnum.AuditPass {

					} else {
						msg = "实例正在保存镜像.请稍后重试"
						return msg, errors.New(msg)
					}
				}
			}

			//if instance.ShutdownDestroy {
			//	msg = "该实例已做销毁处理，请点击销毁按钮"
			//	logger.Error(pre, msg)
			//	return msg, errors.New(msg)
			//}

			ctrInfo, err := NodeService.ContainerInfo(instance.StartupMark)
			if err != nil {
				msg = "获取容器信息失败"
				logger.Error(msg, err, instance.StartupMark)
				return msg, errors.New(msg)
			}

			logger.Info(fmt.Sprintf("container info: %+v", ctrInfo))
			logger.Info(fmt.Sprintf("task: %s, shutdownReason: %s", task, shutdownReason))

			var saveImage model.PodImage
			saveImageType := 0
			if task == tasklog.TaskEnum.SaveInstanceImage {
				saveImageType = enums.ImageTypeEnum.PrivateInstance
				//if enums.ImageSize(ctrInfo.SizeRw) > enums.ImageRWSizeMax && shutdownReason != enums.ShutdownReasonEnum.Manage {
				//	msg = "容器系统盘新增内容已经大于5G，请减少后再关机或者直接销毁"
				//	logger.Error(pre, msg)
				//	return msg, errors.New(msg)
				//}

				if err := saveImage.GetPrivateInstanceImage(instance.ID); err != nil {
					if err != gorm.ErrRecordNotFound {
						msg = "查询实例镜像失败"
						logger.Error(pre, msg, err)
						return msg, err
					}
				}
			} else if task == tasklog.TaskEnum.SaveImageAndDestroy || task == tasklog.TaskEnum.SaveImageAndShutdown {
				saveImageType = enums.ImageTypeEnum.Private
				var startupImage model.PodImage
				if err := startupImage.GetById(instance.StartupImageId); err != nil {
					msg = "查询启动镜像失败"
					logger.Error(pre, msg, err)
					return msg, err
				} else {
					if startupImage.ImageType == enums.ImageTypeEnum.Private {
						if startupImage.UserId != instance.UserId {
							msg = "分享镜像不能保存"
							return msg, errors.New(msg)
						}
						saveImage = startupImage
					}
					//if enums.ImageSize(ctrInfo.SizeRw) > enums.ImageRWSizeMax && shutdownReason != enums.ShutdownReasonEnum.Manage {
					//	msg = fmt.Sprintf("该镜像新增大小已超过 %s，请将大文件迁移到文件存储中，当前实例大小: %.2fG/新增大小: %.2fG", "5G", float32(ctrInfo.SizeRootFs)/common.SizeB2G, float32(ctrInfo.SizeRw)/common.SizeB2G)
					//	return msg, errors.New(msg)
					//}
				}
			} else {
				msg = "task参数错误"
				logger.Error(pre, msg)
				return msg, errors.New(msg)
			}

			if saveImage.ID == 0 {
				var instImage model.PodImage
				if err := instImage.GetById(instance.ImageId); err != nil {
					msg = "获取镜像信息失败"
					logger.Error(pre, msg, err, " instance.ImageId", instance.ImageId)
					return msg, errors.New(msg)
				}
				var pod model.Pod
				if err := pod.GetById(instance.PodId); err != nil {
					logger.Error(pre, "查询Pod信息失败：err:", err, "  ", instance.PodId)
				}
				parentId := uint(0)
				if instImage.ImageType == enums.ImageTypeEnum.Base || instImage.ImageType == enums.ImageTypeEnum.Public || instImage.ImageType == enums.ImageTypeEnum.CCM {
					parentId = instImage.ID
				} else if instImage.ImageType == enums.ImageTypeEnum.Private || instImage.ImageType == enums.ImageTypeEnum.PrivateInstance {
					parentId = instImage.ParentId
				} else {
					msg = "未处理的镜像类型"
					err := errors.New(msg)
					logger.Error(pre, err, " instImageId:", instImage.ID)
					return msg, err
				}

				imageUuid := utils.GetUUID()
				saveImageTag := GenPrivateImageTag(instance.UserId, instance.PodId, instance.ImageTag)
				if saveImageTag == "" {
					//msg = "生成新的镜像标签失败"
					//err := errors.New("生成新的镜像标签失败")
					//return msg, err
					saveImageTag = utils.GenSaveImageTag(instance.ImageTag)
					logger.Error("生成新的镜像标签失败", " instanceUuid:", instance.Uuid, " imageTag:", instance.ImageTag, " GenSaveImageTag: ", saveImageTag)
				}
				saveImage = model.PodImage{
					Uuid:              imageUuid,
					ParentId:          parentId,
					UserId:            instance.UserId,
					ImageType:         saveImageType,
					InstanceId:        instance.ID,
					StorageMode:       enums.ImageStorageModeEnum.Registry,
					PodId:             instance.PodId,
					PodUuid:           pod.Uuid,
					ImageName:         imageUuid,
					ImageTag:          saveImageTag,
					AuditStatus:       enums.ImageAuditStatusEnum.Pushing,
					CommitStartupMark: instance.StartupMark,
					CommitVirtualId:   instance.StartupVirtualId,
					CommitStartTime:   time.Now(),
				}

				if err := instance.SetSaveImage(&saveImage); err != nil {
					msg = "生成实例镜像失败"
					logger.Error(pre, msg, err)
					return msg, errors.New(msg)
				}
				if saveImage.ID == 0 {
					msg = "生成实例镜像失败"
					logger.Error(pre, msg)
					return msg, errors.New(msg)
				}
			} else {
				if err := saveImage.SetAuditStatusToPushing(instance.StartupMark, instance.StartupVirtualId, instance.ID); err != nil {
					msg = "设置推送状态失败.请重试"
					logger.Error(msg, err)
					return msg, err
				}
				if instance.SaveImageId != saveImage.ID {
					if err := instance.SetSaveImageId(saveImage.ID); err != nil {
						msg = "设置保存镜像失败.请重试"
						logger.Error(msg, err)
						return msg, err
					}
				}
			}

			if err := instance.SetShutdownTask(destroy, task); err != nil {
				msg = "操作失败，请重试"
				logger.Error(pre, "设置关机标志失败 instanceUuid：", instance.Uuid, " destroy:", destroy, " task:", task, " err:", err)
				return msg, err
			} else {
				logger.Info(pre, "设置关机标志 instanceUuid：", instance.Uuid, " destroy:", destroy, " task:", task)
			}

			if instance.SaveImageId > 0 {
				if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.SaveImage, utils.Uint2String(instance.SaveImageId)); err != nil {
					logger.Error(err)
				} else {
					tasklog.Delete(logKey)
				}
			}
		} else {

			if err := instance.SetShutdownDestroy(task); err != nil {
				msg = "操作失败，请重试"
				logger.Error(pre, "销毁标志 instanceUuid：", instance.Uuid, " 设置为销毁 err:", err)
				return msg, err
			} else {
				logger.Info(pre, "销毁标志 instanceUuid：", instance.Uuid, " 设置为销毁")
			}

		}

		startupMark = instance.StartupMark

		if instance.Status == enums.InstanceStatusEnum.Running { //结算成功后，状态应该变成completed
			if err := instance.SetShutdownReason(shutdownReason); err != nil {
				logger.Error(pre, err)
			}
			if err := instance.SettleSimple(enums.SettleReasonEnum.Shutdown); err != nil {
				msg = "关机结算失败"
				logger.Error(msg, err, instance.ID)
				return msg, err
			} else {
				logger.Info(fmt.Sprintf("实例ID:%d 已成功结算，并关闭了主线", instance.ID))
				msg = "关机成功"
				if destroy {
					msg = "实例释放成功"
				}
			}
		}
		if instance.Status == enums.InstanceStatusEnum.Running {
			msg = "实例关闭失败"
			err := errors.New(msg)
			logger.Error(msg, err, instance.ID)
			return msg, err
		}

		errBody := ""
		if ginH, err := NodeService.RemoveInstanceNginx(instance.StartupVirtualId, instance.PodId, instance.Uuid); err != nil {
			errBody = "Shutdown RemoveInstanceNginx instanceUuid:" + instanceUuid + err.Error() + utils.GetJsonFromStruct(ginH)
			logger.Error(errBody)
		} else {
			if rGinH, err := ResultGinH(ginH); err != nil {
				errBody = "Shutdown RemoveInstanceNginx rGinH err instanceUuid:" + instanceUuid + err.Error() + utils.GetJsonFromStruct(ginH)
				logger.Error(errBody)
			} else {
				if rGinH.Code == 0 {
					logger.Info("Shutdown RemoveInstanceNginx instanceUuid:", instanceUuid, "移除实例Nginx映射", utils.GetJsonFromStruct(ginH))
				} else {
					//logger.Info("Shutdown RemoveInstanceNginx rGinH.Code!=0 instanceUuid:", instanceUuid, "移除实例Nginx映射", utils.GetJsonFromStruct(ginH))
					errBody = "Shutdown RemoveInstanceNginx  rGinH.Code!=0 instanceUuid:" + instanceUuid + utils.GetJsonFromStruct(ginH)
					logger.Error(errBody)
				}
			}
		}

		//if str, err := NginxInstance(instance.Uuid, "remove"); err != nil {
		//	logger.Error("转发移除失败", err, str, " ", instance.ID)
		//}

		go func() {
			reason := "实例关闭成功"
			if msg1, err1 := WarnService.InstanceRunning(instance.UserId, instance.Uuid, reason); err1 != nil {
				logger.Error("发送请求(实例运行提醒)失败 userId:", instance.UserId, "  uuid:", instance.Uuid, "  reason:", reason, "  msg:", msg1, " err:", err1)
			} else {
				logger.Info("发送请求(实例运行提醒)成功 userId:", instance.UserId, "  uuid:", instance.Uuid, "  reason:", reason)
			}

			if errBody != "" {
				logger.Error("关机时移除Nginx失败 instanceUuid:"+instanceUuid, " errBody:", errBody)
				logKey := "RemoveInstanceNginx_" + instanceUuid
				EmailService.AddNeedSend(logKey, "关机时移除Nginx失败 instanceUuid:"+instanceUuid+"，检查时间:"+jsontime.Now().String()+" errBody:"+errBody)
			}
		}()
		go func() {
			if err := obj.ShutdownInstRecord(startupMark, false); err != nil {
				logger.Error("实例移除失败", err, startupMark)
			} else {
				if destroy {
					//time.Sleep(time.Millisecond * 500)
					if msg1, err := obj.Destroy(instanceUuid); err != nil {
						logger.Error(pre, "实例镜像停止成功，执行Destroy逻辑失败 ", msg1, "  err:", err)
					} else {
						logger.Info(pre, "实例镜像停止成功，执行Destroy逻辑成功")
					}
				}
			}
		}()
	} else {
		msg = "实例正在关机中，请勿重复操作"
		err := errors.New(msg)
		logger.Error(msg, instanceUuid)
		return msg, err
	}
	code = 0
	return msg, nil
}

func (obj *instanceNode_) Destroy(instanceUuid string) (string, error) {
	code := 1
	msg := ""
	pre := fmt.Sprintf("InstanceNode.Destroy instanceUuid:%s  ", instanceUuid)
	defer func() {
		if code == 1 && !strings.Contains(msg, "请勿重复操作") {
			go func() {
				var instance model.Instance
				if err := instance.GetByUuid(instanceUuid); err != nil {
					msg = "获取实例信息失败"
					logger.Error(pre, msg, err)
				} else {
					reason := "实例释放失败"
					if msg1, err1 := WarnService.InstanceRunning(instance.UserId, instance.Uuid, reason); err1 != nil {
						logger.Error(pre, "发送请求(实例运行提醒)失败 userId:", instance.UserId, "  uuid:", instance.Uuid, "  reason:", reason, "  msg:", msg1, " err:", err1)
					} else {
						logger.Info(pre, "发送请求(实例运行提醒)成功 userId:", instance.UserId, "  uuid:", instance.Uuid, "  reason:", reason)
					}
				}
			}()
		}
	}()

	logger.Info(pre)
	if instanceUuid == "" {
		msg = "参数错误"
		logger.Error(pre, msg, instanceUuid, code)
		return msg, errors.New(msg)
	}
	lockKey := enums.RedisKeyEnum.LockKey + "instance_" + instanceUuid
	if common.RedisLock(lockKey, 1, 0) {
		defer common.RedisUnLock(lockKey)
		var instance model.Instance
		if err := instance.GetByUuid(instanceUuid); err != nil {
			msg = "获取实例信息失败"
			logger.Error(pre, msg, err, " ", instanceUuid)
			return msg, err
		}
		pre += fmt.Sprintf(" startupMark:%s ", instance.StartupMark)
		logger.Info(pre)

		overDay := IsOverDayInstance(instance)
		if instance.Status == enums.InstanceStatusEnum.Hidden {
			msg = "该实例已销毁，请刷新一下浏览器清除记录"
			return msg, nil
		}

		if instance.Status != enums.InstanceStatusEnum.ShutdownComplete && instance.Status != enums.InstanceStatusEnum.Created {
			msg = "当前实例状态不支持该操作"
			return msg, errors.New(msg)
		}

		if msg1, err := InstanceNodeService.DestroyClear(instanceUuid); err != nil {
			logger.Error(msg1, err, instanceUuid)
		}

		//oldStatus := instance.Status
		if err := instance.Destroy(); err != nil {
			msg = "释放实例失败"
			logger.Error(msg, err, instance.ID)
			return msg, err
		}
		if overDay == false {
			go func() {
				reason := "实例释放成功"
				if msg1, err1 := WarnService.InstanceRunning(instance.UserId, instance.Uuid, reason); err1 != nil {
					logger.Error("发送请求(实例运行提醒)失败 userId:", instance.UserId, "  uuid:", instance.Uuid, "  reason:", reason, "  msg:", msg1, " err:", err1)
				} else {
					logger.Info("发送请求(实例运行提醒)成功 userId:", instance.UserId, "  uuid:", instance.Uuid, "  reason:", reason)
				}
			}()
		}
		code = 0
		msg = "实例释放成功"
		return msg, nil
	} else {
		msg = "实例正在释放中，请勿重复操作"
		err := errors.New(msg)
		logger.Error(msg, instanceUuid)
		return msg, err
	}

}

func (obj *instanceNode_) DestroyClear(instanceUuid string) (string, error) {
	msg := ""
	var instance model.Instance
	if err := instance.GetByUuid(instanceUuid); err != nil {
		msg = "获取实例信息失败"
		logger.Error(msg, instanceUuid)
		return msg, err
	}
	if instance.Status == enums.InstanceStatusEnum.Created {
		msg = "创建状态，无需清理"
		return msg, nil
	}
	if instance.Status != enums.InstanceStatusEnum.ShutdownComplete && instance.Status != enums.InstanceStatusEnum.Hidden {
		msg = "实例未关机"
		logger.Error(msg, instanceUuid)
		return msg, errors.New(msg)
	}
	//if instance.ShutdownDestroy == false {
	//	msg = "不是销毁操作，无需清理"
	//	return msg, errors.New(msg)
	//}

	if instance.StartupImageId == 0 {
		return "启动镜像Id为0", nil
	}

	var podImage model.PodImage
	if err := podImage.GetById(instance.StartupImageId); err != nil {
		msg = "获取启动镜像信息失败"
		logger.Error(msg, err, instance.StartupImageId)
		return msg, err
	}
	if podImage.ImageType != enums.ImageTypeEnum.PrivateInstance {
		msg = "不是个人实例镜像，不清理"
		//logger.Error(msg, podImage.ID)
		return msg, nil
	}
	if msg1, err := NodeService.ResetCacheShutdown(instance.StartupVirtualId, instance.StartupMark, common.CacheShutdownKeepSeconds); err != nil {
		logger.Error(msg1, err, instance.StartupMark)
	}
	if podImage.ImageType == enums.ImageTypeEnum.PrivateInstance {
		if err := DeleteHubByImageName(enums.ImageTypeEnum.PrivateInstance, podImage.ImageName); err != nil {
			msg = "删除镜像文件失败"
			logger.Error(msg, err)
			return msg, err
		} else {
			if err := podImage.SetDelete(); err != nil {
				msg = "删除失败"
				logger.Error(msg, err)
				return msg, err
			}
			msg = "删除成功"
			return msg, nil
		}
	}
	return "镜像ID空,不处理", nil
}

func (obj *instanceNode_) OverDayClear(instanceUuid string) (string, error) {
	msg := ""
	var instance model.Instance
	if err := instance.GetByUuid(instanceUuid); err != nil {
		msg = "获取实例信息失败"
		logger.Error(msg, instanceUuid)
		return msg, err
	}

	if IsOverDayInstance(instance) == false {
		msg = fmt.Sprintf("未过期实例")
		return msg, nil
	}
	//return "模拟清理", nil
	return obj.Destroy(instanceUuid)
}

func (obj *instanceNode_) SetShutdownRegularTime(instanceUuid string, regularTime time.Time, cancel, save bool) (string, error) {
	code := 1
	msg := ""

	logger.Info("设置定时关机 instanceUuid:", instanceUuid, "   regularTime:", regularTime)
	if instanceUuid == "" {
		msg = "参数错误"
		logger.Error(msg, instanceUuid, code)
		return msg, errors.New(msg)
	}
	lockKey := enums.RedisKeyEnum.LockKey + "instance_" + instanceUuid
	if common.RedisLock(lockKey, 1, 1000*60) {
		defer common.RedisUnLock(lockKey)

		var instance model.Instance
		if err := instance.GetByUuid(instanceUuid); err != nil {
			msg = "获取实例信息失败"
			logger.Error(msg, err)
			return msg, err
		}

		if instance.Status != enums.InstanceStatusEnum.Running {
			msg = "实例不在运行状态，不能设置定时关机"
			return msg, errors.New(msg)
		}
		if cancel {
			if err := instance.SetShutdownRegularTime(common.DefaultTime, save); err != nil {
				msg = "取消定时关机失败"
				logger.Error(msg, "  ", err)
				return msg, err
			}
			code = 0
			return "已取消定时关机", nil
		}

		//checkTime := time.Now().Add(time.Minute * 5)
		if regularTime.Before(time.Now()) {
			msg = "已过定时关机时间，请重新选择"
			return msg, errors.New(msg)
		}

		if err := instance.SetShutdownRegularTime(regularTime, save); err != nil {
			msg = "定时关机设置失败"
			logger.Error(msg, "  ", err)
			return msg, err
		}
		code = 0
		return "定时关机设置完成", nil
	} else {
		msg = "实例正在设置中，请勿重复操作"
		err := errors.New(msg)
		logger.Error(msg, instanceUuid)
		return msg, err
	}

}

func (obj *instanceNode_) GpuModels(podId uint) ([]structs.GpuModelMonitor, int64, error) {
	var pod model.Pod
	if podId > 0 {
		if err := pod.GetById(podId); err != nil {
			logger.Error(err)
		}
	}

	//var aryStats = make([]structs.GpuStats, 0)
	mStats := make(map[uint]structs.GpuStats)

	virtualIds := make([]uint, 0)
	if pod.VirtualIds != "" {
		ary := strings.Split(pod.VirtualIds, "|")
		for _, val := range ary {
			if virtualId := utils.String2Uint(val); virtualId > 0 {
				virtualIds = append(virtualIds, virtualId)
			}
		}
	}

	isValidVirtual := func(podIds string, podId uint) bool {
		if podIds == "" {
			return true
		}
		tmp := fmt.Sprintf("|%d|", podId)
		return strings.Contains(podIds, tmp)
	}

	var virtual model.Virtual
	aryVirtual := make([]model.Virtual, 0)
	if _, err := virtual.ListStats(&aryVirtual, virtualIds, 1, 1000); err != nil {
		logger.Error(err)
	} else {
		for _, tmpVirtual := range aryVirtual {
			if !isValidVirtual(tmpVirtual.PodIds, pod.ID) {
				continue
			}
			if tmpVirtual.NodeId == 2 {
				continue
			}
			//if config.Env == enums.EnvEnum.ONLINE {
			//	if tmpVirtual.NodeId != 2 {
			//		continue
			//	}
			//}
			//if config.Env == enums.EnvEnum.PRODUCTION {
			//	if tmpVirtual.NodeId == 2 {
			//		continue
			//	}
			//}

			if tmpStat, ok := mStats[tmpVirtual.GpuModelId]; ok {
				tmpStat.FreeGpus += tmpVirtual.FreeGpus
				tmpStat.TotalGpus += tmpVirtual.TotalGpus
				mStats[tmpVirtual.GpuModelId] = tmpStat
			} else {
				mStats[tmpVirtual.GpuModelId] = structs.GpuStats{GpuModelId: tmpVirtual.GpuModelId, FreeGpus: tmpVirtual.FreeGpus, TotalGpus: tmpVirtual.TotalGpus}
			}
		}
	}

	var gpuModel model.GpuModel

	var ary = make([]structs.GpuModelMonitor, 0)
	if total, err := gpuModel.List(&ary, uint(0), "", 1, 1, 100); err != nil {
		msg := "查询失败"
		logger.Error(msg, err)
		return nil, total, err
	} else {
		for i := 0; i < len(ary); i++ {
			ary[i].FreeTxt = "已售罄"
			if tmpStat, ok := mStats[ary[i].ID]; ok {
				ary[i].FreeGpus = tmpStat.FreeGpus
				ary[i].TotalGpus = tmpStat.TotalGpus
				//充足 少量 紧张 无
				if ary[i].FreeGpus <= 0 {
					ary[i].FreeTxt = "已售罄"
				} else if ary[i].FreeGpus < 5 {
					ary[i].FreeTxt = "紧张"
				} else if ary[i].FreeGpus < 10 {
					ary[i].FreeTxt = "少量"
				} else {
					ary[i].FreeTxt = "充足"
				}
			}
		}
		return ary, total, nil
	}
}

func (obj *instanceNode_) RemoveNginx(instanceUuid string, force bool) (gin.H, error) {
	code := 1
	msg := ""
	logger.Info("开始移除实例Nginx instanceUuid:", instanceUuid)
	if instanceUuid == "" {
		msg = "参数错误"
		logger.Error(msg, instanceUuid, code)
		return nil, errors.New(msg)
	}

	lockKey := ""
	defer func() {
		if lockKey != "" {
			common.RedisUnLock(lockKey)
		}
	}()
	lockKey = enums.RedisKeyEnum.LockKey + "instance_" + instanceUuid
	if common.RedisLock(lockKey, 1, 1000*10) {

	} else {
		lockKey = ""
		msg = "请勿频繁操作"
		return nil, errors.New(msg)
	}
	var instance model.Instance
	if err := instance.GetByUuid(instanceUuid); err != nil {
		msg = "获取实例信息失败"
		logger.Error(msg, err)
		return nil, err
	}

	if instance.Status == enums.InstanceStatusEnum.ShutdownComplete || instance.Status == enums.InstanceStatusEnum.Hidden || force {
		if ginH, err := NodeService.RemoveInstanceNginx(instance.StartupVirtualId, instance.PodId, instance.Uuid); err != nil {
			logger.Error("instanceUuid:", instanceUuid, err, utils.GetJsonFromStruct(ginH))
			return ginH, err
		} else {
			logger.Info("instanceUuid:", instanceUuid, "移除实例Nginx映射", utils.GetJsonFromStruct(ginH))
			return ginH, err
		}
	} else {
		logger.Info("instanceUuid:", instanceUuid, "当前状态不移除实例Nginx映射 status:", instance.Status)
		return nil, errors.New("实例当前状态不能移除Nginx映射")
	}
}

func (obj *instanceNode_) ShutdownInstRecord(startupMark string, force bool) error {
	pre := fmt.Sprintf("instanceNode_.ShutdownInstRecord %s %b ", startupMark, force)
	var instRecord model.InstRecord
	if err := instRecord.GetByStartupMark(startupMark); err != nil {
		logger.Error(pre, err)
		return err
	}
	if instRecord.Status == enums.InstanceStatusEnum.ShutdownComplete {
		logger.Info(pre, "已经关闭，无需处理", instRecord.ID)
		return nil
	}

	var instance model.Instance
	if err := instance.GetById(instRecord.InstanceId); err != nil {
		logger.Error(pre, err)
		return err
	}
	if instance.StartupMark == instRecord.StartupMark {
		if instance.Status == enums.InstanceStatusEnum.ShutdownComplete || instance.Status == enums.InstanceStatusEnum.Hidden {

		} else {
			msg := "实例未关闭，不能删除Docker"
			logger.Error(pre, msg, instance.ID, "  ", instRecord.ID, "   ", instRecord.StartupMark)
			return errors.New(msg)
		}
	}
	//if instance.Status == enums.InstanceStatusEnum.Running && instance.StartupMark == instRecord.StartupMark {
	//	msg := "实例未关闭，不能删除Docker"
	//	logger.Error(msg, instance.ID, "  ", instRecord.ID, "   ", instRecord.StartupMark)
	//	return errors.New(msg)
	//}

	postUrl := "api/node/docker/shutdown"
	postData := make(map[string]interface{})
	postData["virtual_id"] = instRecord.StartupVirtualId
	postData["startup_mark"] = instRecord.StartupMark
	postData["instance_uuid"] = instance.Uuid
	if instance.ShutdownDestroy == false {
		postData["save_image_id"] = instance.SaveImageId
	}
	postData["destroy"] = instance.ShutdownDestroy
	postData["force"] = force
	postData["task"] = instance.ShutdownTask
	if instance.ShutdownTask != "" {
		postData["save_image_id"] = instance.SaveImageId
	}
	arrKey := make([]string, 0)
	arrKey = append(arrKey, "lastusetime_"+instance.Uuid)
	if instance.StartupMaps != "" {
		arr := utils.GetMapAryFromJson(instance.StartupMaps)
		if arr != nil {
			for _, mm := range arr {
				if _, ok := mm["url"]; ok {
					//存在
					url := mm["url"].(string)
					url = strings.Replace(url, "https://", "", -1)
					tmpAry := strings.Split(url, ".")
					if len(tmpAry) > 0 && len(tmpAry[0]) > 16 {
						arrKey = append(arrKey, tmpAry[0])
					}
				}
			}
		}
	}
	postData["remove_keys"] = arrKey

	logger.Info("发送远程关机命令", postData)
	mm, _ := PostNode(instRecord.StartupNodeId, postUrl, postData)
	if ginH, err := Result(mm); err != nil {
		if instRecord.StartupNodeId <= 11 { //特殊处理
			if instRecord.Status == enums.InstanceStatusEnum.ShutdownComplete {
				logger.Info(pre, "已经关闭，无需处理", instRecord.ID)
				return nil
			}
			if err := instRecord.SetStatus2ShutdownComplete(); err != nil {
				logger.Error(pre, err, instRecord.ID)
				return err
			} else {
				logger.Info(pre, "docker关闭完成 instRecord.ID：", instRecord.ID)
				return nil
			}
		}
		msg := "移除远程Docker失败"
		logger.Error(pre, msg, err, instRecord)
		return err
	} else {
		logger.Info(pre, " instRecord.ID：", instRecord.ID, " ginH:", utils.GetJsonFromStruct(ginH))
		if ginH.Code == 0 {
			if instRecord.Status == enums.InstanceStatusEnum.ShutdownComplete {
				logger.Info(pre, "已经关闭，无需处理", instRecord.ID)
				return nil
			}
			if err := instRecord.SetStatus2ShutdownComplete(); err != nil {
				logger.Error(pre, err, instRecord.ID)
				return err
			} else {
				logger.Info(pre, "docker关闭完成 instRecord.ID：", instRecord.ID)
				return nil
			}
		} else {
			msg := "移除远程Docker失败"
			logger.Error(pre, msg, err, instRecord)
			return err
		}
	}
}

func (obj *instanceNode_) ReStart(instanceUuid string) (gin.H, error) {
	code := 1
	msg := ""

	if instanceUuid == "" {
		msg = "参数错误"
		logger.Error(msg, instanceUuid, code)
		return nil, errors.New(msg)
	}
	lockKey := enums.RedisKeyEnum.LockKey + "instance_" + instanceUuid
	if common.RedisLock(lockKey, 1, 1000*60) {
		defer common.RedisUnLock(lockKey)

		var instance model.Instance
		if err := instance.GetByUuid(instanceUuid); err != nil {
			msg = "获取实例信息失败"
			logger.Error(msg, err)
			return nil, err
		}
		if instance.Status != enums.InstanceStatusEnum.Running {
			msg = "实例不在运行中，不能进行重启操作"
			return nil, errors.New(msg)
		}
		return NodeService.ReStartDocker(instance.StartupNodeId, instance.StartupVirtualId, instance.StartupMark)
	} else {
		msg = "实例正在重启中，请勿重复操作"
		err := errors.New(msg)
		logger.Error(msg, instanceUuid)
		return nil, err
	}
}

func (obj *instanceNode_) SetNginx(instanceUuid string) (gin.H, error) {
	code := 1
	msg := ""

	if instanceUuid == "" {
		msg = "参数错误"
		logger.Error(msg, instanceUuid, code)
		return nil, errors.New(msg)
	}
	lockKey := enums.RedisKeyEnum.LockKey + "instance_" + instanceUuid
	if common.RedisLock(lockKey, 1, 1000*60) {
		defer common.RedisUnLock(lockKey)

		var instance model.Instance
		if err := instance.GetByUuid(instanceUuid); err != nil {
			msg = "获取实例信息失败"
			logger.Error(msg, err)
			return nil, err
		}
		if instance.Status != enums.InstanceStatusEnum.Running {
			msg = "实例不在运行中，不能进行该操作"
			return nil, errors.New(msg)
		}
		return NodeService.SetNginx(instance.StartupNodeId, instance.StartupVirtualId, instance.StartupMark)
	} else {
		msg = "实例正在设置中，请勿重复操作"
		err := errors.New(msg)
		logger.Error(msg, instanceUuid)
		return nil, err
	}
}

func (obj *instanceNode_) SaveImage(instanceUuid string, newImageTitle string, newImageTag string, storageMode int, shutdown bool) (gin.H, error) {
	code := 1
	msg := ""
	if storageMode == 0 {
		storageMode = 1
	}
	if instanceUuid == "" {
		msg = "参数错误"
		logger.Error(msg, instanceUuid, code)
		return nil, errors.New(msg)
	}
	lockKey := enums.RedisKeyEnum.LockKey + "instance_" + instanceUuid
	if common.RedisLock(lockKey, 1, 0) {
		defer common.RedisUnLock(lockKey)

		var instance model.Instance
		if err := instance.GetByUuid(instanceUuid); err != nil {
			msg = "获取实例信息失败"
			logger.Error(msg, err)
			return nil, err
		}

		if instance.Status == enums.InstanceStatusEnum.Running || instance.Status == enums.InstanceStatusEnum.ShutdownInProgress {
			msg = "实例还在运行中，请先关闭实例"
			err := errors.New(msg)
			return nil, err
		}
		shutdown = false //以后保存成功不用做关机操作了
		//if instance.Status != enums.InstanceStatusEnum.Running {
		//	msg = "实例未运行"
		//	logger.Error(msg, "instanceUuid:", instance.Uuid)
		//	return nil, errors.New(msg)
		//}

		var pod model.Pod
		if err := pod.GetById(instance.PodId); err != nil {
			msg = "获取Pod信息失败"
			logger.Error(msg, err)
			return nil, err
		}

		if newImageTitle == "" {
			newImageTitle = pod.Title
		}

		if instance.InstanceType == enums.InstanceTypeEnum.Kol {
			msg = "实例类型不正确"
			return nil, errors.New(msg)
		}

		if instance.ImageId <= 0 {
			msg = "该实例镜像不支持保存"
			logger.Error(msg, " instanceUuid:", instance.Uuid)
			return nil, errors.New(msg)
		}

		if instance.SaveImageId > 0 {
			var tmpImage model.PodImage
			if err := tmpImage.GetById(instance.SaveImageId); err != nil {
				if err == gorm.ErrRecordNotFound {

				} else {
					msg = "查询保存镜像信息失败"
					logger.Error(msg, err)
					return nil, errors.New(msg)
				}
			} else {
				if tmpImage.AuditStatus == enums.ImageAuditStatusEnum.PushSuccess || tmpImage.AuditStatus == enums.ImageAuditStatusEnum.AuditPass || tmpImage.Status == 9 {

				} else {
					msg = "上个镜像正在保存中，请稍后操作"
					logger.Error(msg)
					return nil, errors.New(msg)
				}
			}
		}

		var instImage model.PodImage
		if err := instImage.GetById(instance.ImageId); err != nil {
			msg = "获取镜像信息失败"
			logger.Error(msg, err, " instance.ImageId", instance.ImageId)
			return nil, errors.New(msg)
		}

		imageTag := instance.ImageTag
		if newImageTag != "" {
			imageTag = newImageTag
		}

		if imageTag == "" {
			if pod.Command != "" {
				imageTag = GetImageTag(pod.Command)
			} else if pod.ImageTag != "" {
				imageTag = pod.ImageTag
			}
		}

		if imageTag == "" {
			msg = "未获取到镜像标签"
			err := errors.New(msg)
			logger.Error(msg, err)
			return nil, err
		}

		if instImage.ImageType == enums.ImageTypeEnum.Private || instImage.ImageType == enums.ImageTypeEnum.PrivateInstance {
			if instImage.UserId != instance.UserId {
				msg = "无权限保存该镜像"
				logger.Error(msg, "instanceUuid:", instance.Uuid)
				return nil, errors.New(msg)
			}
			if instImage.LayerCount >= 121 {
				err := errors.New("该镜像保存次数已达上线")
				return nil, err
			}
			//if instance.SaveImageId == 0 {
			//	logger.Info("设置实例的 instance.SaveImageId:", instImage.ID)
			//	if err := instance.SetSaveImageId(instImage.ID); err != nil {
			//		msg = "保存镜像ID出错"
			//		logger.Error(msg)
			//		return nil, errors.New(msg)
			//	}
			//}
			//podImage = instImage
		}

		//var checkTitle model.PodImage
		//if err := checkTitle.GetUserPrivateImageByTitle(instance.UserId, newImageTitle); err != nil {
		//	if err != gorm.ErrRecordNotFound {
		//		msg = "查询镜像标题失败"
		//		logger.Error(err)
		//		return nil, err
		//	}
		//} else {
		//	msg = "镜像标题已存在"
		//	return nil, errors.New(msg)
		//}

		var podImage model.PodImage
		{
			parentId := uint(0)
			if instImage.ImageType == enums.ImageTypeEnum.Base || instImage.ImageType == enums.ImageTypeEnum.Public || instImage.ImageType == enums.ImageTypeEnum.CCM {
				parentId = instImage.ID
			}
			imageUuid := utils.GetUUID()
			//saveImageTag := GenSaveImageTag(imageTag)
			saveImageTag := GenPrivateImageTag(instance.UserId, instance.PodId, imageTag)
			if saveImageTag == "" {
				err := errors.New("生成新的镜像标签失败")
				logger.Error(err, " instanceUuid:", instance.Uuid, " imageTag:", imageTag)
				return nil, err
			}
			podImage = model.PodImage{
				Uuid:              imageUuid,
				ParentId:          parentId,
				UserId:            instance.UserId,
				ImageType:         enums.ImageTypeEnum.Private,
				InstanceId:        instance.ID,
				Title:             newImageTitle,
				StorageMode:       storageMode,
				PodId:             instance.PodId,
				PodUuid:           pod.Uuid,
				ImageName:         imageUuid,
				ImageTag:          saveImageTag,
				AuditStatus:       enums.ImageAuditStatusEnum.Pushing,
				CommitStartupMark: instance.StartupMark,
				CommitVirtualId:   instance.StartupVirtualId,
				CommitStartTime:   time.Now(),
			}
			if err := instance.SetSaveImage(&podImage); err != nil {
				msg = "生成镜像记录失败"
				logger.Error(msg, err)
				return nil, errors.New(msg)
			}
		}
		/*
			if instance.SaveImageId == 0 {
				parentId := uint(0)
				if instImage.ImageType == enums.ImageTypeEnum.Base || instImage.ImageType == enums.ImageTypeEnum.Public || instImage.ImageType == enums.ImageTypeEnum.CCM {
					parentId = instImage.ID
				}
				podImage = model.PodImage{
					Uuid:        utils.GetUUID(),
					ParentId:    parentId,
					UserId:      instance.UserId,
					ImageType:   enums.ImageTypeEnum.Private,
					InstanceId:  instance.ID,
					Title:       newImageTitle,
					StorageMode: storageMode,
					PodId:       instance.PodId,
					PodUuid:     pod.Uuid,
					ImageName:   instance.Uuid,
					ImageTag:    imageTag,
				}
				if err := instance.SetSaveImage(&podImage); err != nil {
					msg = "生成镜像记录失败"
					logger.Error(msg, err)
					return nil, errors.New(msg)
				}
			} else {
				if err := podImage.GetById(instance.SaveImageId); err != nil {
					msg = "获取存储镜像信息失败"
					logger.Error(msg, err)
					return nil, err
				}
			}*/

		//if instance.ImageId > 0 { //私有镜像启动的，直接覆盖私有镜像
		//	if err := podImage.GetById(instance.ImageId); err != nil {
		//		logger.Error(err)
		//		msg = "获取镜像失败"
		//		return nil, err
		//	}
		//	if podImage.ImageType == enums.ImageTypeEnum.Private && podImage.UserId == instance.UserId {
		//		if podImage.LayerCount >= 121 {
		//			err := errors.New("该镜像保存次数已达上线")
		//			return nil, err
		//		}
		//	} else {
		//		podImage.ID = 0
		//	}
		//}
		//if podImage.ID == 0 {
		//	podImage = model.PodImage{
		//		Uuid:        utils.GetUUID(),
		//		UserId:      instance.UserId,
		//		ImageType:   enums.ImageTypeEnum.Private,
		//		StorageMode: storageMode,
		//		PodId:       instance.PodId,
		//		PodUuid:     pod.Uuid,
		//		ImageName:   instance.Uuid,
		//		ImageTag:    imageTag,
		//	}
		//	if err := podImage.Save(); err != nil {
		//		msg = "创建镜像记录出错"
		//		logger.Error(msg, err)
		//		return nil, err
		//	}
		//}

		if podImage.ID <= 0 || instance.SaveImageId <= 0 {
			msg = "生成镜像记录失败"
			logger.Error(msg)
			return nil, errors.New(msg)
		}

		//if instance.SaveImageId > 0 && instance.SaveImageId != podImage.ID {
		//	msg = "镜像ID不匹配"
		//	logger.Error(msg, instance.SaveImageId, "   ", podImage.ID)
		//	return nil, errors.New(msg)
		//}

		//if podImage.AuditStatus != enums.ImageAuditStatusEnum.Pushing {
		//	//if err := podImage.SetAuditStatus(enums.ImageAuditStatusEnum.Pushing); err != nil {
		//	//	msg = "设置镜像状态失败"
		//	//	logger.Error(msg)
		//	//	return nil, errors.New(msg)
		//	//}
		//	if err := podImage.SetAuditStatusToPushing(instance.StartupMark, instance.StartupVirtualId); err != nil {
		//		msg = "设置镜像状态失败"
		//		logger.Error(msg)
		//		return nil, errors.New(msg)
		//	}
		//}

		//if instance.SaveImageId == 0 {
		//	logger.Info("设置实例的 imageID:", podImage.ID)
		//	if err := instance.SetSaveImageId(podImage.ID); err != nil {
		//		msg = "保存镜像ID出错"
		//		logger.Error(msg, err)
		//		return nil, errors.New(msg)
		//	}
		//}

		if shutdown {
			if err := instance.SetShutdownReason(enums.ShutdownReasonEnum.SaveImageAndShutdown); err != nil {
				logger.Error(err)
			}
		}

		userPath := ""
		if storageMode == enums.ImageStorageModeEnum.PrivateDisk {
			var user model.User
			if err := user.GetById(instance.UserId); err != nil {
				logger.Error(err)
				return nil, err
			}
			userPath = user.PrivateStorage
		}
		task := ""
		if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.SaveImage, utils.Uint2String(podImage.ID)); err != nil {
			logger.Error(err)
		} else {
			tasklog.Delete(logKey)
		}
		return NodeService.SaveImageDocker(instance.StartupNodeId, instance.StartupVirtualId, instance.StartupMark, podImage.ID, storageMode, task, userPath, shutdown)

	} else {
		msg = "镜像正在保存中，请勿重复操作"
		err := errors.New(msg)
		logger.Error(msg, instanceUuid)
		return nil, err
	}
}

func (obj *instanceNode_) SaveImageContinue(imageId uint, instanceUuId string) (gin.H, error) { //只继续保持个人镜像或者个人实例镜像
	lockKey := enums.RedisKeyEnum.LockKey + "SaveImageContinue_" + fmt.Sprintf("%d", imageId)
	if common.RedisLock(lockKey, 1, 0) {
		defer common.RedisUnLock(lockKey)

		msg := ""
		var podImage model.PodImage
		if err := podImage.GetById(imageId); err != nil {
			msg = "镜像信息不存在"
			logger.Error(msg, err)
			return nil, err
		}

		if instanceUuId == "" {
			var instRecord model.InstRecord
			if podImage.CommitStartupMark != "" {
				if err := instRecord.GetByStartupMark(podImage.CommitStartupMark); err != nil {
					msg = "查询启动记录失败 err:" + err.Error()
					logger.Error(msg, err)
					return nil, err
				}
			} else {
				if err := instRecord.GetForContinueSaveImage(podImage.ID); err != nil {
					msg = "查询启动记录失败" + err.Error()
					logger.Error(msg, err)
					return nil, err
				}
			}

			var instance model.Instance
			if err := instance.GetById(instRecord.InstanceId); err != nil {
				msg = "获取实例信息失败"
				return nil, err
			} else {
				instanceUuId = instance.Uuid
			}
		}

		var instance model.Instance
		if err := instance.GetById(podImage.InstanceId); err != nil {
			msg = "查询实例信息失败"
			logger.Error(msg, err)
			return nil, err
		}

		if instance.InstanceType == enums.InstanceTypeEnum.Kol {
			if podImage.ImageType != enums.ImageTypeEnum.Public {
				msg = "是KOL实例，镜像类型必须是公共镜像"
				err := errors.New(msg)
				logger.Error(msg, err)
				return nil, err
			}

			if container, err := NodeService.ContainerFullInfo(instance.StartupMark); err != nil {
				msg = "获取容器信息失败 err:" + err.Error()
				logger.Error(msg, err, instance.StartupMark)
				return nil, err
			} else {
				if container.State == "exited" {
					logger.Info("继续保存KOL镜像 startupMark:", instance.StartupMark, "  saveImageId:", podImage.ID)
				} else {
					msg = "该KOL实例容器还未停止，请手动处理"
					return nil, err
				}
			}

		} else {
			if podImage.ImageType != enums.ImageTypeEnum.Private && podImage.ImageType != enums.ImageTypeEnum.PrivateInstance {
				msg = "不是个人镜像或个人实例镜像"
				err := errors.New(msg)
				logger.Error(msg, err)
				return nil, err
			}
		}

		if podImage.AuditStatus == enums.ImageAuditStatusEnum.PushSuccess || podImage.AuditStatus == enums.ImageAuditStatusEnum.AuditPass {
			msg = "镜像已保存成功，无需再保存"
			logger.Error(msg)
			return nil, errors.New(msg)
		}

		if podImage.InstanceId == 0 {
			msg = "镜像实例ID为0"
			logger.Error(msg)
			return nil, errors.New(msg)
		}

		if instance.StartupMark == "" {
			msg = "启动标记已被重置为空，请先到启动记录里手动查询"
			logger.Error(errors.New(msg))
			return nil, errors.New(msg)
		}

		if instance.Uuid != instanceUuId {
			msg = "镜像关联的实例跟保存实例不一致，请手动处理"
			if podImage.CommitStartupMark == "" {
				logger.Error(errors.New(msg))
				return nil, errors.New(msg)
			} else {
				var tmpInstance model.Instance
				if err := tmpInstance.GetByStartupMark(podImage.CommitStartupMark); err != nil {
					msg += "查询CommitStartupMark失败 err：" + err.Error()
				} else {
					if tmpInstance.Uuid == instanceUuId {
						instance = tmpInstance
					} else {
						msg += " CommitStartupMark也不一致，需要手动处理"
						logger.Error(errors.New(msg))
						return nil, errors.New(msg)
					}
				}
			}

		}

		if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.SaveImage, utils.Uint2String(podImage.ID)); err != nil {
			msg = "获取镜像日志Key失败"
			logger.Error(msg, err, podImage.ID)
			return nil, err
		} else {
			if b, err := tasklog.OutTime(logKey, 60*5); err != nil {
				msg = "获取镜像超时信息失败"
				logger.Error(msg, podImage.ID)
				return nil, err
			} else {
				if !b {
					msg = "该镜像正在保存中，请稍后操作"
					logger.Error(msg, podImage.ID)
					return nil, errors.New(msg)
				}
			}
		}
		task := ""
		if instance.ShutdownDestroy == false {
			if podImage.ImageType == enums.ImageTypeEnum.PrivateInstance {
				task = tasklog.TaskEnum.SaveInstanceImage
			}
		}
		if podImage.AuditStatus == enums.ImageAuditStatusEnum.PushFail {
			if err := podImage.SetAuditStatus(enums.ImageAuditStatusEnum.Pushing); err != nil {
				msg = "设置镜像为推送状态失败"
				logger.Error(msg)
				return nil, errors.New(msg)
			}
		}
		logger.Info("发送继续保持镜像请求，startupMark:", instance.StartupMark)
		return NodeService.SaveImageDocker(instance.StartupNodeId, instance.StartupVirtualId, instance.StartupMark, podImage.ID, enums.ImageStorageModeEnum.Registry, task, "", false)
	} else {
		err := errors.New("请勿重复操作")
		logger.Error(err, lockKey)
		return nil, err
	}
}

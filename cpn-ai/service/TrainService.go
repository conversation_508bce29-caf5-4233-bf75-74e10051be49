package service

import (
	"cpn-ai/common/logger"
	"cpn-ai/common/redisqueue"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/model"
	"cpn-ai/structs"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"os"
	"path"
	"strings"
	"time"
)

type train_ struct {
	ModelCategorys []structs.TrainModelCategory
	ModelItems     []structs.TrainModelItem
}

var TrainService train_

const TrainImageSuffix = ".jpg,.jpeg,.png"

func init() {
	TrainService.Init()
}

func (obj *train_) Init() {
	modelCategorys := make([]structs.TrainModelCategory, 0)
	modelCategorys = append(modelCategorys, structs.TrainModelCategory{Id: 1, Title: "F.1", Logo: "", Order: 1})
	modelCategorys = append(modelCategorys, structs.TrainModelCategory{Id: 2, Title: "F.1极速模式", Logo: "", Order: 2})
	modelCategorys = append(modelCategorys, structs.TrainModelCategory{Id: 3, Title: "3.5L", Logo: "", Order: 3})
}

func (obj *train_) GetTrainStorage() string {

	if config.Env == enums.EnvEnum.DEV {
		return "/Users/<USER>/mnt/team-data/872de996f7ab954bc0c18dff2a6e08f4/train"
	}
	if strings.HasPrefix(utils.GetLocalIP(), "10.0.") {
		return "/usrdata/team-data/872de996f7ab954bc0c18dff2a6e08f4/train"
	}
	return "/mnt/team-data/872de996f7ab954bc0c18dff2a6e08f4/train"
}
func (obj *train_) GetTrainImgDomain() string {
	if strings.HasPrefix(utils.GetLocalIP(), "10.0.") {
		return "http://127.0.0.1:6002/api/train/img/"
	}
	return "https://trainimg.chenyu.cn/"
}

func (obj *train_) CheckJobParam(jobParam structs.TrainJobParam) (structs.TrainJobParam, error) {
	return jobParam, nil
}

func handlerPopTaggerTaskOutput(task redisqueue.Task) error {
	logger.Info("taskOutput:", utils.GetJsonFromStruct(task))
	qTaskInputJson := utils.GetJsonFromStruct(task.QTaskInput)
	var cropTagParam structs.CropTagParam
	if err := utils.GetStructFromJson(&cropTagParam, qTaskInputJson); err != nil {
		logger.Error("task.QTaskInput转structs.CropTagParam失败", err)
		return err
	}

	var tranTags model.TrainTags
	if err := tranTags.GetByUuid(cropTagParam.TagsUuid); err != nil {
		logger.Error(err)
		return err
	}

	if task.QTaskCompletedAt > 0 {
		qTaskErr := utils.TruncateStringByRuneCount(task.QTaskErr, 1000)
		if err := tranTags.SetCompleted(time.UnixMilli(task.QTaskStartAt), time.UnixMilli(task.QTaskCompletedAt), qTaskErr); err != nil {
			logger.Error(err)
			return err
		}
	}

	return nil
}

func (obj *train_) ManualHandlerPopJobTaskOutput(jobsUuid string) error {
	var trainJobs model.TrainJobs
	if err := trainJobs.GetByUuid(jobsUuid); err != nil {
		logger.Error(err)
		return err
	}
	var handerJobGetQueueName redisqueue.GetQueueNameFunc
	handerJobGetQueueName = obj.GetJobQueueName
	if task, err := redisqueue.GetTaskOutput("", handerJobGetQueueName, trainJobs.QTaskId); err != nil {
		return err
	} else {
		if err := handlerPopJobTaskOutput(task); err != nil {
			logger.Error(err)
			return err
		}
	}
	return nil
}

func handlerPopJobTaskOutput(task redisqueue.Task) error {
	logger.Info("taskOutput:", utils.GetJsonFromStruct(task))
	qTaskInputJson := utils.GetJsonFromStruct(task.QTaskInput)
	var trainJobParam structs.TrainJobParam
	if err := utils.GetStructFromJson(&trainJobParam, qTaskInputJson); err != nil {
		logger.Error("task.QTaskInput转structs.TrainJobParam", err)
		return err
	}

	var tranJobs model.TrainJobs
	if err := tranJobs.GetByUuid(trainJobParam.JobsUuid); err != nil {
		logger.Error(err)
		return err
	}

	if task.QTaskCompletedAt > 0 {
		qTaskErr := utils.TruncateStringByRuneCount(task.QTaskErr, 1000)
		if err := tranJobs.SetCompleted(time.UnixMilli(task.QTaskStartAt), time.UnixMilli(task.QTaskCompletedAt), qTaskErr); err != nil {
			logger.Error(err)
			return err
		}
	}

	return nil
}

func (obj *train_) GetTaggerQueueName(taggerName string) (string, string, string, string) {
	//            input              output            zset            hset
	return "tagger:" + taggerName, "tagger", "tagger:" + taggerName, "tagger"
}

func (obj *train_) GetJobQueueName(levelName string) (string, string, string, string) {
	//            input              output            zset            hset
	return "train:" + levelName, "train", "train:" + levelName, "train"
}

func (obj *train_) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("train奔溃:", e)
		}
	}()
	logger.Info("train.Run 开始循环获取打标输出队列")
	var handlerTaggerQueue redisqueue.TaskOutputLogicFunc
	handlerTaggerQueue = handlerPopTaggerTaskOutput
	var handerTaggerGetQueueName redisqueue.GetQueueNameFunc
	handerTaggerGetQueueName = obj.GetTaggerQueueName
	go func() {
		logger.Info("train.Run 开始循环获取打标输出队列")
		for {
			redisqueue.PopTaskOutput("", handerTaggerGetQueueName, handlerTaggerQueue)
		}
		logger.Info("train.Run 结束循环获取打标输出队列")
	}()

	logger.Info("train.Run 开始循环获取训练输出队列")
	var handlerJobQueue redisqueue.TaskOutputLogicFunc
	handlerJobQueue = handlerPopJobTaskOutput
	var handerJobGetQueueName redisqueue.GetQueueNameFunc
	handerJobGetQueueName = obj.GetJobQueueName
	go func() {
		logger.Info("train.Run 开始循环获取训练输出队列")
		for {
			redisqueue.PopTaskOutput("", handerJobGetQueueName, handlerJobQueue)
		}
		logger.Info("train.Run 结束循环获取训练输出队列")
	}()

}

func (obj *train_) PushTaggerTask(tagAlg string, task redisqueue.Task) (count int64, msg string, err error) {
	var handerGetQueueName redisqueue.GetQueueNameFunc
	handerGetQueueName = obj.GetTaggerQueueName
	if count, err = redisqueue.PushTask(tagAlg, handerGetQueueName, task); err != nil {
		msg = "加入打标队列失败"
		logger.Error(msg, err)
		return
	} else {
		msg = fmt.Sprintf("加入打标队列成功，当前排在第%d位", count)
		return
	}
}

func (obj *train_) PushJobTask(jobLevel string, task redisqueue.Task) (count int64, msg string, err error) {
	task.QTaskName = jobLevel
	var handerGetQueueName redisqueue.GetQueueNameFunc
	handerGetQueueName = obj.GetJobQueueName
	if count, err = redisqueue.PushTask(jobLevel, handerGetQueueName, task); err != nil {
		msg = "加入训练队列失败"
		logger.Error(msg, err)
		return
	} else {
		msg = fmt.Sprintf("加入训练队列成功，当前排在第%d位", count)
		return
	}
}

func (obj *train_) TaggerRank(tagAlg string, qTaskId string) (rankStatus string, msg string, rank int64, progress float64, err error) {
	//NoData无数据 Waiting在排队 Running在执行 Err
	var handerGetQueueName redisqueue.GetQueueNameFunc
	handerGetQueueName = obj.GetTaggerQueueName
	if rankStatus, msg, rank, progress, err = redisqueue.Rank(tagAlg, handerGetQueueName, qTaskId); err != nil {
		logger.Error("获取排队位置失败", err)
		return
	} else {
		//txt := fmt.Sprintf("当前排在第%d位", rank)
		//if progress > 0 {
		//	txt = fmt.Sprintf("正在执行中，当前进度%.2f%%", progress*100)
		//}
		//if progress >= 1 {
		//	txt = "已执行完成"
		//}
		//msg = txt
		return
	}
}

func (obj *train_) JobRank(levelName string, qTaskId string) (rankStatus string, msg string, rank int64, progress float64, err error) {
	var handerGetQueueName redisqueue.GetQueueNameFunc
	handerGetQueueName = obj.GetJobQueueName
	if rankStatus, msg, rank, progress, err = redisqueue.Rank(levelName, handerGetQueueName, qTaskId); err != nil {
		//msg = "获取排队位置失败"
		logger.Error("获取排队位置失败", err)
		return
	} else {

		//txt := fmt.Sprintf("当前排在第%d位", rank)
		//if progress > 0 {
		//	txt = fmt.Sprintf("正在执行中，当前进度%.2f%%", progress*100)
		//}
		//if progress >= 1 {
		//	txt = "已执行完成"
		//}
		//msg = txt
		return
	}
}

func (obj *train_) TaggerAbort(tagAlg string, qTaskId string) (msg string, err error) {
	var handerGetQueueName redisqueue.GetQueueNameFunc
	handerGetQueueName = obj.GetTaggerQueueName
	if exists, err1 := redisqueue.RemoveRank(tagAlg, handerGetQueueName, qTaskId); err != nil {
		msg = "移除排队队列失败"
		err = err1
		logger.Error(msg, err)
		return
	} else {
		if exists == 0 { //队列中已经不存在，检查是否正在执行，如果正在执行需要取消
			msg = "已取消正在执行的队列"
		} else {
			msg = "已从队列中移除"
		}
		return
	}
}

func (obj *train_) JobAbort(jobsUuid string) (msg string, err error) {
	var trainJobs model.TrainJobs
	if err = trainJobs.GetByUuid(jobsUuid); err != nil {
		msg = "获取训练任务失败"
		if err == gorm.ErrRecordNotFound {
			msg = "训练任务不存在"
		}
		logger.Error(msg, err)
		return
	}
	if trainJobs.Status == model.TrainJobsStatusTraining {
		msg = "该任务正在训练中，请使用终止功能"
		err = errors.New(msg)
		return
	}

	if trainJobs.Status == model.TrainJobsStatusTrainSuc || trainJobs.Status == model.TrainJobsStatusTrainFail {
		msg = "该任务已训练完成"
		err = errors.New(msg)
		return
	}

	levelName := trainJobs.Level
	qTaskId := trainJobs.QTaskId
	var handerGetQueueName redisqueue.GetQueueNameFunc
	handerGetQueueName = obj.GetJobQueueName
	if exists, err1 := redisqueue.RemoveRank(levelName, handerGetQueueName, qTaskId); err != nil {
		msg = "从队列中移除失败"
		err = err1
		logger.Error(msg, err)
		return
	} else {
		if exists == 0 { //队列中已经不存在，检查是否正在执行，如果正在执行需要取消
			msg = "该任务已不在队列中"
		} else {
			msg = "已从队列中移除"
		}
		if err = trainJobs.SetStatus(model.TrainJobsStatusReady); err != nil {
			msg = "设置状态失败"
			return
		}
		return
	}
}

func (obj *train_) JobTerminate(jobsUuid string) (msg string, err error) {
	var trainJobs model.TrainJobs
	if err = trainJobs.GetByUuid(jobsUuid); err != nil {
		msg = "获取训练任务失败"
		if err == gorm.ErrRecordNotFound {
			msg = "训练任务不存在"
		}
		logger.Error(msg, err)
		return
	}
	//if trainJobs.Status == model.TrainJobsStatusTraining {
	//	msg = "该任务正在训练中，请使用终止功能"
	//	err = errors.New(msg)
	//	return
	//}

	if trainJobs.Status == model.TrainJobsStatusTrainSuc || trainJobs.Status == model.TrainJobsStatusTrainFail {
		msg = "该任务已训练完成"
		err = errors.New(msg)
		return
	}

	levelName := trainJobs.Level
	qTaskId := trainJobs.QTaskId
	var handerGetQueueName redisqueue.GetQueueNameFunc
	handerGetQueueName = obj.GetJobQueueName

	if _, err = redisqueue.AbortTask(levelName, handerGetQueueName, qTaskId); err != nil {
		msg = "设置终止标志失败"
		logger.Error(msg, err)
		return
	} else {
		msg = "任务终止中，预计需要10秒"
		return
	}
}

func (obj *train_) handlerClearTasks(trainTags model.TrainTags) error {
	logger.Info("开始删除数据集:", trainTags.Uuid)
	if msg, err := obj.DeleteTags(trainTags.Uuid); err != nil {
		logger.Error(msg, err)
		return err
	}
	return nil
}

func (obj *train_) handlerClearImages(trainImages model.TrainImages) error {
	logger.Info("开始移除图片集:", trainImages.Uuid)
	if msg, err := obj.RemoveImages(trainImages.Uuid); err != nil {
		logger.Error(msg, err)
		return err
	}
	return nil
}

func (obj *train_) Clear() {
	var trainTags model.TrainTags
	var handlerTags model.TrainTagsLoopForClearLogicFunc
	handlerTags = obj.handlerClearTasks
	if count, err := trainTags.LoopForClear(1, 10, handlerTags); err != nil {
		logger.Error(count, err)
	} else {

	}

	var trainImages model.TrainImages
	var handerImages model.TrainImagesLoopForClearLogicFunc
	handerImages = obj.handlerClearImages
	if count, err := trainImages.LoopForClear(10, 100, handerImages); err != nil {
		logger.Error(count, err)
	} else {

	}
}

func (obj *train_) RemoveImages(imagesUuid string) (msg string, err error) {
	var trainImages model.TrainImages
	if err = trainImages.GetByUuid(imagesUuid); err != nil {
		msg = "获取数据集信息失败"
		logger.Error(msg, err)
		return
	}

	if len(imagesUuid) < 30 {
		msg = "参数错误"
		return
	}
	imagesBasePath := path.Join(obj.GetTrainStorage(), "images", imagesUuid)
	viewsBasePath := path.Join(obj.GetTrainStorage(), "views", imagesUuid)

	if err = os.RemoveAll(viewsBasePath); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			msg = "删除缩略图片集失败"
			logger.Error(msg, err, viewsBasePath)
			return
		}
	}

	if err = os.RemoveAll(imagesBasePath); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			msg = "删除图片集失败"
			logger.Error(msg, err, imagesBasePath)
			return
		}
	}

	if err = trainImages.Delete(); err != nil {
		msg = "删除图片集数据失败"
		logger.Error(msg, err)
		return
	}
	msg = "删除成功"
	return
}

func (obj *train_) DeleteTags(tagsUuid string) (msg string, err error) {

	var trainTags model.TrainTags
	if err = trainTags.GetByUuid(tagsUuid); err != nil {
		msg = "获取数据集信息失败"
		logger.Error(msg, err)
		return
	}

	var handerGetQueueName redisqueue.GetQueueNameFunc
	handerGetQueueName = obj.GetTaggerQueueName
	redisqueue.RemoveRank(trainTags.TagAlg, handerGetQueueName, trainTags.QTaskId)

	if len(tagsUuid) < 30 {
		msg = "参数错误"
		return
	}

	tagsBasePath := path.Join(obj.GetTrainStorage(), "tags", tagsUuid)

	if err = os.RemoveAll(tagsBasePath); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			msg = "删除数据集失败"
			logger.Error(msg, err, tagsBasePath)
			return
		}
	}

	if err = trainTags.Delete(); err != nil {
		msg = "删除数据集失败"
		logger.Error(msg, err)
		return
	}

	msg = "数据集删除成功"
	return
}

func (obj *train_) GetDefaultTrainJobParam() structs.TrainJobParam {
	parm := structs.TrainJobParam{
		//训练参数
		Repeat:         20,
		Epoch:          5,
		BatchSize:      1,
		MixedPrecision: "fp16",

		//样图设置
		Resolution: "1024*1024",
		Seed1:      1000000001,
		Sampler:    "Euler a",

		//保存设置
		SaveEveryNEpochs: 1,
		SavePrecision:    "fp16",

		//学习率&优化器
		LearningRate:         "1e-4",
		UnetLr:               0.0001,
		TextEncoderLr:        0.00001,
		LrScheduler:          "cosine_with_restarts",
		Optimizer:            "AdamW8bit",
		LrSchedulerNumCycles: 1,

		//网络
		NetworkRankDim: 32,
		NetworkAlpha:   32,

		//打标设置
		ShuffleCaption: true,
		KeepNTokens:    2,
		MaxTokenLength: 75,
		//噪声设置
		NoiseOffset: 0.1,
		Seed:        -1,
		ClipSkip:    1,
	}
	return parm
}

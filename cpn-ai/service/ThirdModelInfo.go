package service

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	model "cpn-ai/model"
	"cpn-ai/request"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"path"
	"regexp"
	"strconv"
	"strings"
	"time"
)

const (
	civitaiAPIBaseURL = "https://com/api/v1"

	huggingfaceAPIURL = "https://huggingface.co/api/models"
	downloadBaseURL   = "https://huggingface.co"

	userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
)

type HuggingFacFileInfo struct {
	Path    string `json:"path"`
	Size    int64  `json:"size"`
	Oid     string `json:"oid"`
	LfsSize int64  `json:"lfs_size,omitempty"`
	LfsOid  string `json:"lfs_oid,omitempty"`
	Sha     string `json:"sha,omitempty"`
}

type CivitaiModelResponse struct {
	ID      int    `json:"id"`
	Name    string `json:"name"`
	Version struct {
		ID int `json:"id"`
	} `json:"version"`
	ModelVersions []CivitaiModelVersion `json:"modelVersions"`
}

type CivitaiModelVersion struct {
	ID          int           `json:"id"`
	Name        string        `json:"name"`
	DownloadURL string        `json:"downloadUrl"`
	Files       []CivitaiFile `json:"files"`
}

type CivitaiFile struct {
	Name        string        `json:"name"`
	SizeKB      float64       `json:"sizeKB"`
	Hashes      CivitaiHashes `json:"hashes"`
	Primary     bool          `json:"primary"`
	DownloadURL string        `json:"downloadUrl"`
}

type CivitaiHashes struct {
	SHA256 string `json:"SHA256"`
	CRC32  string `json:"CRC32"`
	BLAKE3 string `json:"BLAKE3"`
}

type CivitaiModelVersionResponse struct {
	ID    int           `json:"id"`
	Name  string        `json:"name"`
	Files []CivitaiFile `json:"files"`
}

type ModelFileListResponse struct {
	FileName        string // 模型路径+名称 e: ace_step_transformer/diffusion_pytorch_model.safetensors
	Sha256          string // 文件的sha256
	FileDownloadUrl string // 文件下载地址
}

type _thirdModel struct {
}

var ThirdModelService _thirdModel

func (tm *_thirdModel) DownloadThirdModel(req request.ThirdModelDownloadReq, userId uint) error {
	var (
		err            error
		modelFileList  []ModelFileListResponse
		modelShaList   []string
		shareModelFile model.ShareModel
		user           model.User
	)

	// 获取用户信息
	if err = user.GetById(userId); err != nil {
		logger.Error(fmt.Sprintf("获取用户信息失败,err: %v", err))
		return errors.New("获取用户信息失败")
	}
	if user.ID == 0 {
		return errors.New("用户不存在")
	}

	if req.ModelSource == enums.ModelSourceHuggingFace {
		modelFileList, err = tm.getHuggingFaceModelFile(req.ModelUrl)
		if err != nil {
			err = errors.New("获取模型文件列表失败")
			logger.Error(fmt.Sprintf("获取模型文件列表失败,err: %v", err))
			return err
		}
	} else if req.ModelSource == enums.ModelSourceCivitai {
		modelFileList, err = tm.getCivitaiModelFile(req.ModelUrl)
		if err != nil {
			logger.Error(fmt.Sprintf("获取模型文件列表失败,err: %v", err))
			return errors.New("获取模型文件列表失败")
		}
	} else {
		return errors.New("不支持的模型来源")
	}

	for _, modelFile := range modelFileList {
		modelShaList = append(modelShaList, modelFile.Sha256)
	}

	// 对比数据库中的sha256，获取已存在的文件
	shareModelFileList, err := shareModelFile.ListBySha256In(nil, modelShaList)
	if err != nil {
		// 获取失败则忽略，意味着全部文件都要下载
		logger.Error(fmt.Sprintf("查询数据库失败,err: %v", err))
	}

	shareModelFileMap := make(map[string]string)
	for _, shareFile := range shareModelFileList {
		shareModelFileMap[shareFile.SHA256] = shareFile.OriginalPath
	}
	go func() {
		userPathBase := "/mnt/user-data/store0" // todo 确认路径
		userPathPrivate := user.PrivateStorage
		if userPathPrivate == "" || userPathPrivate == "/" {
			logger.Error(fmt.Sprintf("用户路径不正确, userPathPrivate: %s", userPathPrivate))
			return
		}

		// 模型文件存储路径
		downloadPathBase := path.Join(userPathBase, userPathPrivate)
		for _, modelFile := range modelFileList {
			// 文件实际下载路径
			fileDownPathAbs := path.Join(downloadPathBase, modelFile.FileName)

			if shareFilePath, ok := shareModelFileMap[modelFile.Sha256]; !ok {
				// 如果sha256不在数据库中，则下载
				err = tm.downloadFile(modelFile.FileDownloadUrl, fileDownPathAbs)
				if err != nil {
					logger.Error(fmt.Sprintf("下载文件失败,err: %v", err))
				}
			} else {
				if shareFilePath == "" {
					logger.Error(fmt.Sprintf("数据库中sha256对应的文件路径为空,sha256: %s", modelFile.Sha256))
					err = tm.downloadFile(modelFile.FileDownloadUrl, fileDownPathAbs)
					if err != nil {
						logger.Error(fmt.Sprintf("下载文件失败,err: %v", err))
					}
				}
				if err = common.CreateSymbolLink(shareFilePath, fileDownPathAbs); err != nil {
					logger.Error(fmt.Sprintf("创建软链接失败,err: %v", err))
					// 创建失败就去下载文件
					err = tm.downloadFile(modelFile.FileDownloadUrl, fileDownPathAbs)
					if err != nil {
						logger.Error(fmt.Sprintf("下载文件失败,err: %v", err))
					}
				}
			}
		}
	}()
	return nil
}

// 下载文件
func (tm *_thirdModel) downloadFile(downloadUrl, fileSavePath string) error {

	// 创建请求
	req, err := http.NewRequest("GET", downloadUrl, nil)
	if err != nil {
		return fmt.Errorf("创建下载请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", userAgent)

	// 创建HTTP客户端，设置较长的超时时间
	client := &http.Client{
		Timeout: 30 * time.Minute,
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("下载请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("下载API返回错误: %s - %s", resp.Status, string(body))
	}

	// 确保输出目录存在
	if err = common.EnsureFilePathExists(fileSavePath); err != nil {
		return fmt.Errorf("创建文件路径失败: %v", err)
	}

	file, err := os.Create(fileSavePath)
	if err != nil {
		return fmt.Errorf("创建输出文件失败: %v", err)
	}
	defer file.Close()

	// 写入下载的数据到文件
	bytesWritten, err := io.Copy(file, resp.Body)
	if err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	logger.Info("成功下载文件: %s (大小: %s)\n", fileSavePath, utils.FormatFileSize(bytesWritten))
	return nil
}

func (tm *_thirdModel) getHuggingFaceModelFile(url string) ([]ModelFileListResponse, error) {
	var (
		err  error
		resp []ModelFileListResponse
	)
	resp = make([]ModelFileListResponse, 0)
	// 解析URL
	repoName, branch, err := tm.parseHuggingFaceURL(url)
	if err != nil {
		logger.Error("解析模型地址失败,err: ", err)
		return resp, err
	}

	logger.Info(fmt.Sprintf("正在查询仓库: %s (分支: %s)...", repoName, branch))

	// 获取仓库根目录文件列表
	var allFiles []HuggingFacFileInfo
	err = tm.getAllFilesHuggingFace(repoName, branch, "", &allFiles)
	if err != nil {
		logger.Error("获取文件列表失败,err: ", err)
		return resp, err
	}

	for _, file := range allFiles {
		resp = append(resp, ModelFileListResponse{
			FileName:        file.Path,
			Sha256:          file.Sha,
			FileDownloadUrl: tm.getDownloadURL(repoName, branch, file.Path),
		})
	}

	return resp, nil
}

func (tm *_thirdModel) getCivitaiModelFile(url string) ([]ModelFileListResponse, error) {
	var (
		err  error
		resp []ModelFileListResponse
	)
	resp = make([]ModelFileListResponse, 0)
	// 确保URL不包含页面锚点和其他参数
	if idx := strings.Index(url, "#"); idx != -1 {
		url = url[:idx]
	}

	modelID, versionID, err := tm.extractIDsFromURL(url)
	if err != nil {
		logger.Error(fmt.Sprintf("解析模型地址失败,err: %v", err))
		return resp, err
	}

	logger.Info("提取的模型ID: ", modelID)
	if versionID > 0 {
		logger.Info(fmt.Sprintf("提取的版本ID: %d", versionID))
	} else {
		logger.Info("未提供版本ID，将获取最新版本")
	}

	// 如果没有提供版本ID，获取模型信息并使用最新版本
	modelFiles := make([]CivitaiFile, 0)
	if versionID == 0 {
		modelInfo, err := tm.getCivitaiModelInfo(modelID)
		if err != nil {
			logger.Error("获取模型信息失败,err: ", err)
			return resp, err
		}

		if len(modelInfo.ModelVersions) == 0 {
			logger.Error("模型没有版本，请检查模型ID")
			return resp, fmt.Errorf("模型没有版本，请检查模型ID")
		}

		// 使用最新版本（API返回的第一个版本）
		modelFiles = modelInfo.ModelVersions[0].Files
	} else {
		// 获取版本详细信息
		modelVersionInfo, err := tm.getCivitaiModelVersionInfo(versionID)
		if err != nil {
			logger.Error("获取模型版本信息失败,err: ", err)
			return resp, err
		}
		modelFiles = modelVersionInfo.Files
	}

	for _, file := range modelFiles {
		resp = append(resp, ModelFileListResponse{
			FileName:        file.Name,
			Sha256:          file.Hashes.SHA256,
			FileDownloadUrl: file.DownloadURL,
		})
	}
	return resp, nil
}

// 从URL中提取模型ID和版本ID
func (tm *_thirdModel) extractIDsFromURL(url string) (int, int, error) {
	// 提取模型ID
	modelIDRegex := regexp.MustCompile(`/models/(\d+)`)
	modelIDMatches := modelIDRegex.FindStringSubmatch(url)
	if len(modelIDMatches) < 2 {
		return 0, 0, fmt.Errorf("无法从URL中提取模型ID")
	}
	modelID, err := strconv.Atoi(modelIDMatches[1])
	if err != nil {
		return 0, 0, fmt.Errorf("模型ID转换错误: %v", err)
	}

	// 提取版本ID（如果存在）
	versionID := 0
	versionIDRegex := regexp.MustCompile(`modelVersionId=(\d+)`)
	versionIDMatches := versionIDRegex.FindStringSubmatch(url)
	if len(versionIDMatches) >= 2 {
		versionID, err = strconv.Atoi(versionIDMatches[1])
		if err != nil {
			return modelID, 0, fmt.Errorf("版本ID转换错误: %v", err)
		}
	}

	return modelID, versionID, nil
}

// 获取模型信息
func (tm *_thirdModel) getCivitaiModelInfo(modelID int) (*CivitaiModelResponse, error) {
	url := fmt.Sprintf("%s/models/%d", civitaiAPIBaseURL, modelID)
	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("获取模型信息失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API返回错误: %s - %s", resp.Status, string(body))
	}

	var modelInfo CivitaiModelResponse
	if err := json.NewDecoder(resp.Body).Decode(&modelInfo); err != nil {
		return nil, fmt.Errorf("解析模型信息失败: %v", err)
	}

	return &modelInfo, nil
}

// 获取模型版本信息
func (tm *_thirdModel) getCivitaiModelVersionInfo(versionID int) (*CivitaiModelVersionResponse, error) {
	url := fmt.Sprintf("%s/model-versions/%d", civitaiAPIBaseURL, versionID)
	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("获取版本信息失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API返回错误: %s - %s", resp.Status, string(body))
	}

	var version CivitaiModelVersionResponse
	if err := json.NewDecoder(resp.Body).Decode(&version); err != nil {
		return nil, fmt.Errorf("解析版本信息失败: %v", err)
	}

	return &version, nil
}

// 从URL中提取模型仓库名和分支
func (tm *_thirdModel) parseHuggingFaceURL(url string) (string, string, error) {
	// 移除末尾的斜杠（如果有）
	url = strings.TrimSuffix(url, "/")

	// 基础正则表达式匹配 HuggingFace 仓库路径
	repoPattern := regexp.MustCompile(`huggingface\.co/([^/]+/[^/]+)(?:/tree/([^/]+))?`)
	matches := repoPattern.FindStringSubmatch(url)

	if len(matches) < 2 {
		return "", "", fmt.Errorf("无效的 HuggingFace URL 格式")
	}

	repoName := matches[1]
	branch := "main" // 默认分支

	// 如果URL中指定了分支
	if len(matches) > 2 && matches[2] != "" {
		branch = matches[2]
	}

	return repoName, branch, nil
}

// 递归获取目录中的所有文件
func (tm *_thirdModel) getAllFilesHuggingFace(repoName, branch, path string, allFiles *[]HuggingFacFileInfo) error {
	// 构建API请求URL
	url := fmt.Sprintf("%s/%s/tree/%s/%s", huggingfaceAPIURL, repoName, branch, path)

	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", userAgent)
	req.Header.Set("Accept", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("API请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		// 如果是404，可能是遇到了LFS文件指针而非目录
		if resp.StatusCode == http.StatusNotFound {
			return nil
		}
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("API返回错误: %s - %s", resp.Status, string(body))
	}

	// 解析响应
	var files []HuggingFacFileInfo
	body, _ := io.ReadAll(resp.Body)
	if err = json.Unmarshal(body, &files); err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	// 处理每个文件/目录
	for _, file := range files {
		// 检查是否为目录
		isDir := strings.HasSuffix(file.Path, "/") || !strings.Contains(file.Path, ".")

		if isDir {
			// 递归处理子目录
			err := tm.getAllFilesHuggingFace(repoName, branch, file.Path, allFiles)
			if err != nil {
				fmt.Printf("警告: 处理目录 %s 时出错: %v\n", file.Path, err)
			}
		} else {
			// 添加文件到结果列表
			*allFiles = append(*allFiles, file)
		}
	}

	return nil
}

// 构建文件下载URL
func (tm *_thirdModel) getDownloadURL(repoName, branch, filePath string) string {
	// 将所有路径组件正确编码
	pathComponents := strings.Split(filePath, "/")
	encodedPath := ""
	for i, component := range pathComponents {
		if i > 0 {
			encodedPath += "/"
		}
		// URL 编码路径组件（不使用标准URL编码是因为HF API有特殊要求）
		encodedPath += strings.ReplaceAll(component, " ", "%20")
	}

	// 构建下载URL
	return fmt.Sprintf("%s/%s/resolve/%s/%s", downloadBaseURL, repoName, branch, encodedPath)
}

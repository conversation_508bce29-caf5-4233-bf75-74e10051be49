package service

import (
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/model"
	"cpn-ai/request"
	"cpn-ai/response"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path"
	"strings"
	"time"
)

type UserPrivateSizeResp struct {
	Code   int    `json:"code"`
	Msg    string `json:"msg"`
	Result struct {
		Data map[string]string
	}
}

type DirSizeRequest struct {
	BasePath string `json:"base_path"`
}

type SizeResponse struct {
	Code   int    `json:"code"`
	Msg    string `json:"msg"`
	Result struct {
		SumSize int64 `json:"sum_size"`
	}
}

func UserPrivateStatMap() (userPrivateSizeResp UserPrivateSizeResp) {
	err := PostFileStorage("/file/stat", nil, &userPrivateSizeResp)
	if err != nil || userPrivateSizeResp.Code != 0 {
		logger.Error("获取用户的私有空间大小调用失败，将使用默认大小进行,接口 file/stat")
		emailReq := EmailReq{
			From:    "",
			To:      "<EMAIL>,<EMAIL>",
			Subject: "实例关闭失败，需要人工介入 " + time.Now().Format(jsontime.TimeFormat),
			Content: "获取用户的私有空间大小调用失败，将使用默认大小进行,接口 file/stat",
		}
		EmailService.SendWarn(emailReq)
	}
	return userPrivateSizeResp
}

func GetDirSizeRemote(userId uint, dirPath string) (int64, error) {
	reqBody := DirSizeRequest{
		BasePath: dirPath,
	}
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		logger.Error("JSON序列化失败:", err)
		return 0, err
	}
	var result SizeResponse
	err = PostFileStorage("/file/dir_size", jsonData, &result)
	if err != nil {
		return 0, err
	}

	if result.Code != 0 {
		return 0, errors.New(result.Msg)
	}

	logger.Info("文件大小统计完成", dirPath)
	return result.Result.SumSize, nil
}

func FlashUploadCheck(req request.FlashUploadCheckReq) (response.FlashUploadCheckResp, error) {
	var (
		err      error
		fileInfo model.ShareModel
		resp     response.FlashUploadCheckResp
	)

	resp.Exist = enums.FlashUploadFileNotExist

	err = fileInfo.GetByHash(nil, req.FileHashHeader, req.FileHashMiddle, req.FileHashTail)
	if err != nil {
		logger.Error(fmt.Sprintf("根据hash查询文件失败,err: %v", err))
		return resp, err
	}
	if fileInfo.ID != 0 {
		resp.Exist = enums.FlashUploadFileExist
	}
	return resp, nil
}

func FlashUpload(req request.FlashUploadReq, userId uint) error {
	var (
		err      error
		fileInfo model.ShareModel
	)

	err = fileInfo.GetByHash(nil, req.FileHashHeader, req.FileHashMiddle, req.FileHashTail)
	if err != nil {
		logger.Error(fmt.Sprintf("根据hash查询文件失败,err: %v", err))
		return err
	}
	if fileInfo.ID != 0 {
		return errors.New("文件不存在")
	}

	var user model.User
	if err = user.GetById(userId); err != nil {
		logger.Error(fmt.Sprintf("根据id查询用户失败,err: %v", err))
		return err
	}
	if user.ShortId == "" {
		if err = user.SetShortId(); err != nil {
			logger.Error(fmt.Sprintf("设置用户存储路径失败,err: %v", err))
			return err
		}
	}
	if user.ShortId == "" {
		logger.Error(fmt.Sprintf("设置用户存储路径失败, req: %+v", req))
		return errors.New("设置用户存储路径失败")
	}
	if len(user.PrivateStorage) < 10 {
		logger.Error("用户存储路径不正确，请重试，userPrivateStoragePath: %s", user.PrivateStorage)
		return errors.New("用户存储路径不正确，请重试")
	}
	if strings.HasPrefix(user.PrivateStorage, "/") == true || strings.HasSuffix(user.PrivateStorage, "/") == false {
		logger.Error("用户存储路径不正确，请重试，userPrivateStoragePath: %s", user.PrivateStorage)
		return errors.New("用户存储路径不正确，请重试")
	}

	userBasePath := path.Join(config.PrivateStorage, user.PrivateStorage)
	if _, err = os.Stat(userBasePath); os.IsNotExist(err) {
		logger.Info("用户存储路径不存在 开始创建存储路径", userBasePath, userId)
		if err := os.MkdirAll(userBasePath, os.ModePerm); err != nil {
			logger.Error(fmt.Sprintf("创建用户文件夹出错,err: %v", err))
			return err
		}
	} else if err != nil {
		logger.Error(fmt.Sprintf("检测用户存储路径失败,err: %v", err))
		return err
	}
	if _, err = os.Stat(config.PrivateStorage); err == nil || os.IsExist(err) {
		if !strings.HasPrefix(config.PrivateStorage, "/") {
			logger.Error("存储路径格式错误")
			return errors.New("存储路径格式错误")
		}
	} else {
		logger.Error(fmt.Sprintf("检测存储路径失败,err: %v", err))
		return err
	}

	filePathAbs := path.Join(userBasePath, req.Path, req.Filename)
	// 软链接
	err = common.CreateSymbolLink(fileInfo.OriginalPath, filePathAbs)
	if err != nil {
		logger.Error(fmt.Sprintf("创建软链接失败,err: %v", err))
		return err
	}

	return nil
}

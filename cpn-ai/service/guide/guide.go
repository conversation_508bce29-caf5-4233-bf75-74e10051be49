package guide

import (
	"math"
	"strconv"
)

func GetEventId(id int) int { //0,1,2,3,4,5
	return int(math.Pow(2, float64(id)))
}

// 判断一个数字是否是 2 的幂次方
func IsEventId(n int) bool {
	// 数字必须是正数，且 n & (n - 1) 必须为 0
	//return n > 0 && (n&(n-1)) == 0
	if n <= 0 {
		return false
	}
	binary := DecimalToBinary(int64(n))
	for i := 1; i < len(binary); i++ {
		if binary[i] == '1' {
			return false
		}
	}
	return true
}

// 判断事件是否完成 eventID必须是2的0次 1次 2次
func IsEventCompleted(eventsStatus int, eventID int) bool {
	// 使用位运算检查第 eventID 位是否为 1 (表示已完成)
	//return (eventsStatus & (1 << (eventID - 1))) != 0
	return eventsStatus&eventID != 0
}

// 设置事件为完成
func SetEventCompleted(eventsStatus int, eventID int) int {
	// 使用位运算将第 eventID 位设置为 1 (表示已完成)
	//return eventsStatus | (1 << (eventID - 1))
	return eventsStatus | eventID
}

func IsGuideCompleted(guideId int64, guideStatus int64) bool {
	binary := DecimalToBinary(guideStatus) //10010
	if int64(len(binary)) < guideId {
		return false
	}
	if binary[len(binary)-int(guideId)] == '1' {
		return true
	}
	return false
}

func SetGuideCompleted(guideId int64, guideStatus int64) int64 {
	// 使用位运算将第 eventID 位设置为 1 (表示已完成)
	binary := "1"
	for i := int64(0); i < guideId-1; i++ {
		binary = binary + "0"
	}
	guideIdNum, _ := BinaryToDecimal(binary)
	return guideIdNum | guideStatus
}

func DecimalToBinary(n int64) string {
	if n == 0 {
		return "0"
	}

	binary := ""
	for n > 0 {
		remainder := n % 2
		binary = strconv.FormatInt(remainder, 10) + binary
		n /= 2
	}
	return binary
}

//func DecimalToBinary(n int) string {
//	if n == 0 {
//		return "0"
//	}
//	binary := ""
//	for n > 0 {
//		remainder := n % 2
//		binary = strconv.Itoa(remainder) + binary
//		n /= 2
//	}
//	return binary
//}

func BinaryToDecimal(binary string) (int64, error) {
	decimal, err := strconv.ParseInt(binary, 2, 64)
	if err != nil {
		return 0, err
	}
	return decimal, nil
}

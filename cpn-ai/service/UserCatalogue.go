package service

import (
	"cpn-ai/common/logger"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/model"
	"errors"
	"fmt"
	"regexp"
	"strings"
)

type user_catalogue_ struct {
}
type CatalogueItem struct {
	Title string `json:"title"`
	Path  string `json:"path"`
	Cat   string `json:"cat"`
	Group string `json:"group"`
	Order int    `json:"order"`
}

type CatalogueListItem struct {
	Key string `json:"key"`
	CatalogueItem
}

var CatalogueMap map[string]CatalogueItem

var UserCatalogue user_catalogue_

const (
	DirKeyComfyModels             = "ComfyModels"
	DirKeyComfyCheckpoints        = "ComfyCheckpoints"
	DirKeyComfyLora               = "ComfyLora"
	DirKeyComfyVAE                = "ComfyVAE"
	DirKeyComfyUnet               = "ComfyUnet"
	DirKeyComfyAppdataInput       = "ComfyAppdataInput"
	DirKeyComfyAppdataOutput      = "ComfyAppdataOutput"
	DirKeyComfyPoddataModels      = "ComfyPoddataModels"
	DirKeyComfyPoddataCheckpoints = "ComfyPoddataCheckpoints"
	DirKeyComfyPoddataLora        = "ComfyPoddataLora"
	DirKeyComfyPoddataVAE         = "ComfyPoddataVAE"
	DirKeyComfyPoddataUnet        = "ComfyPoddataUnet"
	DirKeySdUsrdataModels         = "SdUsrdataModels"
	DirKeySdUsrdataModelsSd       = "SdUsrdataModelsSd"
	DirKeySdUsrdataLora           = "SdUsrdataLora"
	DirKeySdUsrdataVAE            = "SdUsrdataVAE"
	DirKeySdAppdataInput          = "SdAppdataInput"
	DirKeySdAppdataOutput         = "SdAppdataOutput"
	DirKeySdPoddataModels         = "SdPoddataModels"
	DirKeySdPoddataModelsSd       = "SdPoddataModelsSd"
	DirKeySdPoddataModelsLora     = "SdPoddataModelsLora"
	DirKeySdPoddataModelsVAE      = "SdPoddataModelsVAE"
	DirKeyForgeUsrdataModels      = "ForgeUsrdataModels"
	DirKeyForgeUsrdataModelsSd    = "ForgeUsrdataModelsSd"
	DirKeyForgeUsrdataLora        = "ForgeUsrdataLora"
	DirKeyForgeUsrdataVAE         = "ForgeUsrdataVAE"
	DirKeyForgeAppdataInput       = "ForgeAppdataInput"
	DirKeyForgeAppdataOutput      = "ForgeAppdataOutput"
	DirKeyForgePoddataModels      = "ForgePoddataModels"
	DirKeyForgePoddataModelsSd    = "ForgePoddataModelsSd"
	DirKeyForgePoddataModelsLora  = "ForgePoddataModelsLora"
	DirKeyForgePoddataModelsVAE   = "ForgePoddataModelsVAE"
)

const (
	DirKeyUnShareInput  = "UnShareInput"
	DirKeyUnShareOutput = "UnShareOutput"
)

func init() {

	CatalogueMap = map[string]CatalogueItem{
		//"UsrData":       {Title: "用户根目录", Path: "/usrdata/chenyu", Cat: "usrdata"},
		//"AppdataInput":  {Title: "输入目录", Path: "/appdata/input"},
		//"AppdataOutput": {Title: "输出目录", Path: "/appdata/output"},

		//ComfyUI用户目录
		DirKeyComfyModels:      {Title: "用户模型根目录", Path: "/usrdata/chenyu/ComfyUI/models", Cat: "comfy", Group: "用户目录", Order: 1},
		DirKeyComfyCheckpoints: {Title: "用户主模型", Path: "/usrdata/chenyu/ComfyUI/models/checkpoints", Cat: "comfy", Group: "用户目录", Order: 2},
		DirKeyComfyLora:        {Title: "用户Lora目录", Path: "/usrdata/chenyu/ComfyUI/models/loras", Cat: "comfy", Group: "用户目录", Order: 3},
		DirKeyComfyVAE:         {Title: "用户VAE", Path: "/usrdata/chenyu/ComfyUI/models/vae", Cat: "comfy", Group: "用户目录", Order: 4},
		DirKeyComfyUnet:        {Title: "用户Unet", Path: "/usrdata/chenyu/ComfyUI/models/unet", Cat: "comfy", Group: "用户目录", Order: 5},
		//ComfyUI应用目录
		DirKeyComfyAppdataInput:  {Title: "输入目录", Path: "/appdata/input", Cat: "comfy", Group: "应用目录", Order: 6},
		DirKeyComfyAppdataOutput: {Title: "输出目录", Path: "/appdata/output", Cat: "comfy", Group: "应用目录", Order: 7},
		//ComfyUI创作者目录
		DirKeyComfyPoddataModels:      {Title: "创作者模型根目录", Path: "/poddata/ComfyUI/models", Cat: "comfy", Group: "创作者目录", Order: 8},
		DirKeyComfyPoddataCheckpoints: {Title: "创作者主模型目录", Path: "/poddata/ComfyUI/models/checkpoints", Cat: "comfy", Group: "创作者目录", Order: 9},
		DirKeyComfyPoddataLora:        {Title: "创作者Lora目录", Path: "/poddata/ComfyUI/models/loras", Cat: "comfy", Group: "创作者目录", Order: 10},
		DirKeyComfyPoddataVAE:         {Title: "创作者VAE目录", Path: "/poddata/ComfyUI/models/vae", Cat: "comfy", Group: "创作者目录", Order: 11},
		DirKeyComfyPoddataUnet:        {Title: "创作者Unet目录", Path: "/poddata/ComfyUI/models/unet", Cat: "comfy", Group: "创作者目录", Order: 12},

		//SD用户目录
		DirKeySdUsrdataModels:   {Title: "用户模型根目录", Path: "/usrdata/chenyu/stable-diffusion-webui/models", Cat: "sd", Group: "用户目录", Order: 1},
		DirKeySdUsrdataModelsSd: {Title: "用户主模型目录", Path: "/usrdata/chenyu/stable-diffusion-webui/models/Stable-diffusion", Cat: "sd", Group: "用户目录", Order: 2},
		DirKeySdUsrdataLora:     {Title: "用户Lora目录", Path: "/usrdata/chenyu/stable-diffusion-webui/models/Lora", Cat: "sd", Group: "用户目录", Order: 3},
		DirKeySdUsrdataVAE:      {Title: "用户VAE目录", Path: "/usrdata/chenyu/stable-diffusion-webui/models/VAE", Cat: "sd", Group: "用户目录", Order: 4},
		//SD应用目录
		DirKeySdAppdataInput:  {Title: "输入目录", Path: "/appdata/input", Cat: "sd", Group: "应用目录", Order: 5},
		DirKeySdAppdataOutput: {Title: "输出目录", Path: "/appdata/outputs", Cat: "sd", Group: "应用目录", Order: 6},
		//SD创作者目录
		DirKeySdPoddataModels:     {Title: "创作者模型根目录", Path: "/poddata/stable-diffusion-webui/models", Cat: "sd", Group: "创作者目录", Order: 5},
		DirKeySdPoddataModelsSd:   {Title: "创作者主模型目录", Path: "/poddata/stable-diffusion-webui/models/Stable-diffusion", Cat: "sd", Group: "创作者目录", Order: 6},
		DirKeySdPoddataModelsLora: {Title: "创作者Lora目录", Path: "/poddata/stable-diffusion-webui/models/Lora", Cat: "sd", Group: "创作者目录", Order: 7},
		DirKeySdPoddataModelsVAE:  {Title: "创作者VAE目录", Path: "/poddata/stable-diffusion-webui/models/VAE", Cat: "sd", Group: "创作者目录", Order: 8},

		//Forge用户目录
		DirKeyForgeUsrdataModels:   {Title: "用户模型根目录", Path: "/usrdata/chenyu/stable-diffusion-webui-forge/models", Cat: "forge", Group: "用户目录", Order: 1},
		DirKeyForgeUsrdataModelsSd: {Title: "用户主模型目录", Path: "/usrdata/chenyu/stable-diffusion-webui-forge/models/Stable-diffusion", Cat: "forge", Group: "用户目录", Order: 2},
		DirKeyForgeUsrdataLora:     {Title: "用户Lora目录", Path: "/usrdata/chenyu/stable-diffusion-webui-forge/models/Lora", Cat: "forge", Group: "用户目录", Order: 3},
		DirKeyForgeUsrdataVAE:      {Title: "用户VAE目录", Path: "/usrdata/chenyu/stable-diffusion-webui-forge/models/VAE", Cat: "forge", Group: "用户目录", Order: 4},
		//Forge应用目录
		DirKeyForgeAppdataInput:  {Title: "输入目录", Path: "/appdata/input", Cat: "forge", Group: "应用目录", Order: 5},
		DirKeyForgeAppdataOutput: {Title: "输出目录", Path: "/appdata/outputs", Cat: "forge", Group: "应用目录", Order: 6},
		//Forge创作者目录
		DirKeyForgePoddataModels:     {Title: "创作者模型根目录", Path: "/poddata/stable-diffusion-webui-forge/models", Cat: "forge", Group: "创作者目录", Order: 7},
		DirKeyForgePoddataModelsSd:   {Title: "创作者主模型", Path: "/poddata/stable-diffusion-webui-forge/models/Stable-diffusion", Cat: "forge", Group: "创作者目录", Order: 8},
		DirKeyForgePoddataModelsLora: {Title: "创作者Lora", Path: "/poddata/stable-diffusion-webui-forge/models/Lora", Cat: "forge", Group: "创作者目录", Order: 9},
		DirKeyForgePoddataModelsVAE:  {Title: "创作者VAE", Path: "/poddata/stable-diffusion-webui-forge/models/VAE", Cat: "forge", Group: "创作者目录", Order: 10},
	}
}

func (d *user_catalogue_) GetListItem(key string) *CatalogueListItem {
	if val, ok := CatalogueMap[key]; ok {
		aa := CatalogueListItem{
			Key: key,
			CatalogueItem: CatalogueItem{ // 将 CatalogueItem 字段嵌入
				Title: val.Title,
				Path:  val.Path,
				Cat:   val.Cat,
				Group: val.Group,
				Order: val.Order,
			},
		}
		return &aa
	}
	return nil
}

func (d *user_catalogue_) GetPathOfContainer(key string) (string, error) {
	if val, ok := CatalogueMap[key]; ok {
		return val.Path, nil
	}
	return "", errors.New("key不存在")
}

func (d *user_catalogue_) GetPathOfHost(key string, userId uint, podUuid string) (string, string, error) {
	if path, err := d.GetPathOfContainer(key); err != nil {
		return "", "", err
	} else {
		return d.ReplacePath(path, userId, podUuid)
	}
}

func (d *user_catalogue_) GetNeedPrivilege(key string) (string, error) {
	if path, err := d.GetPathOfContainer(key); err != nil {
		return "", err
	} else {
		if strings.HasPrefix(path, "/appdata/") {
			return enums.CataLoguePrivilegeEnum.USER, nil
		}
		if strings.HasPrefix(path, "/usrdata/chenyu/") {
			return enums.CataLoguePrivilegeEnum.USER, nil
		}
		if strings.HasPrefix(path, "/models/") {
			return enums.CataLoguePrivilegeEnum.KOL, nil
		}
		if strings.HasPrefix(path, "/poddata/") {
			return enums.CataLoguePrivilegeEnum.KOL, nil
		}
		if strings.HasPrefix(path, "/chenyudata/") {
			return enums.CataLoguePrivilegeEnum.SYS, nil
		}
		return "", errors.New("not match privilege")
	}
}

func (d *user_catalogue_) ReplacePath11(path string, userId uint, podUuid string) (string, string, error) {
	var user model.User
	userStorage := ""
	if err := user.GetById(userId); err != nil {
		return "", "", err
	} else {
		userStorage = strings.TrimLeft(user.PrivateStorage, "/")
		userStorage = strings.TrimRight(user.PrivateStorage, "/")
	}

	if len(podUuid) != 32 {
		return "", "", errors.New("PodUuid 不正确")
	}
	redirectPath := ""
	basePath := ""
	if strings.HasPrefix(path, "/usrdata/") {
		redirectPath = strings.Replace(path, "/usrdata/", "/", -1) //redirectPath = strings.Replace(path, "/usrdata/chenyu/", fmt.Sprintf("/root/suanyun-user/%s/", userPath), -1)
		basePath = fmt.Sprintf("/mnt/user-data/%s/", userStorage)
	} else if strings.HasPrefix(path, "/poddata/") {
		redirectPath = strings.Replace(path, "/poddata/", "/", -1) //redirectPath = strings.Replace(path, "/poddata/", fmt.Sprintf("/root/suanyun-share/%s/", podUuid), -1)
		basePath = fmt.Sprintf("/mnt/pod-data/%s/", podUuid)
	} else if strings.HasPrefix(path, "/appdata/") {
		redirectPath = strings.Replace(path, "/appdata/", fmt.Sprintf("/container/%s/", podUuid), -1) //redirectPath = strings.Replace(path, "/appdata/", fmt.Sprintf("/container/%s/", userPath, podUuid), -1)
		basePath = fmt.Sprintf("/mnt/user-data/%s/", userStorage)
	}
	if redirectPath == "" {
		return "", "", errors.New("路径不正确")
	}
	if len(basePath) <= 25 || !strings.Contains(basePath, "/root/suanyun-") {
		return "", "", errors.New("基础路径不正确")
	}
	if config.Env == enums.EnvEnum.DEV {
		basePath = strings.Replace(basePath, "/root/", "/tmp/", -1)
	}
	return redirectPath, basePath, nil
}

func (d *user_catalogue_) ReplacePath(path string, userId uint, podUuid string) (string, string, error) {
	var user model.User
	userStorage := ""
	if err := user.GetById(userId); err != nil {
		return "", "", err
	} else {
		userStorage = strings.TrimLeft(user.PrivateStorage, "/")
		userStorage = strings.TrimRight(user.PrivateStorage, "/")
	}

	if len(podUuid) != 32 {
		return "", "", errors.New("PodUuid 不正确")
	}
	redirectPath := ""
	basePath := ""
	if strings.HasPrefix(path, "/usrdata/") {
		redirectPath = strings.Replace(path, "/usrdata/", "/", -1) //redirectPath = strings.Replace(path, "/usrdata/chenyu/", fmt.Sprintf("/root/suanyun-user/%s/", userPath), -1)
		basePath = fmt.Sprintf("/mnt/user-data/store0/%s/", userStorage)
	} else if strings.HasPrefix(path, "/poddata/") {
		redirectPath = strings.Replace(path, "/poddata/", "/", -1) //redirectPath = strings.Replace(path, "/poddata/", fmt.Sprintf("/root/suanyun-share/%s/", podUuid), -1)
		basePath = fmt.Sprintf("/mnt/pod-data/%s/", podUuid)
	} else if strings.HasPrefix(path, "/appdata/") {
		redirectPath = strings.Replace(path, "/appdata/", fmt.Sprintf("/container/%s/", podUuid), -1) //redirectPath = strings.Replace(path, "/appdata/", fmt.Sprintf("/container/%s/", userPath, podUuid), -1)
		basePath = fmt.Sprintf("/mnt/user-data/store0/%s/", userStorage)
	}
	if redirectPath == "" {
		return "", "", errors.New("路径不正确")
	}
	if len(basePath) <= 25 {
		logger.Error("len(basePath) <= 25 ", basePath)
		return "", "", errors.New("基础路径不正确")
	}
	if !strings.Contains(basePath, "/mnt/user-data/store") && !strings.Contains(basePath, "/mnt/pod-data/") {
		logger.Error("基础路径不正确 ", basePath)
		return "", "", errors.New("基础路径不正确")
	}
	if config.Env == enums.EnvEnum.DEV {
		basePath = strings.Replace(basePath, "/mnt/", "/tmp/", -1)
	}
	return redirectPath, basePath, nil
}

func (d *user_catalogue_) CheckBasePath11(basePath string) bool {
	if len(basePath) <= 25 {
		return false
	}
	if config.Env == enums.EnvEnum.DEV {
		return true
	}
	if !strings.HasSuffix(basePath, "/") {
		basePath += "/"
	}
	if strings.HasPrefix(basePath, "/root/suanyun-user/") { // 0/33zfej14a47b/
		if len(basePath) < 30 {
			return false
		}
		re := `^/root/suanyun-user/\d+/[a-zA-Z0-9]{12}/$`
		// 编译正则表达式
		r, err := regexp.Compile(re)
		if err != nil {
			logger.Error(err)
			return false
		}
		return r.MatchString(basePath)
	}
	if strings.HasPrefix(basePath, "/root/suanyun-share/") { // /root/suanyun-share/7b0fe29ef29d430a8c7a97ea3378a407/
		if len(basePath) < 52 {
			return false
		}
		re := `^/root/suanyun-share/[a-zA-Z0-9]{32}/$`
		// 编译正则表达式
		r, err := regexp.Compile(re)
		if err != nil {
			logger.Error(err)
			return false
		}
		return r.MatchString(basePath)
	}
	return false
}

func (d *user_catalogue_) CheckBasePath(basePath string) bool {
	//把目录/root/suanyun-user/改成/mnt/user-data/store0/
	// /mnt/user-data/store0/c503abeeefb74af4ab4cb0e5948d4c56/
	if len(basePath) <= 25 {
		return false
	}
	//if config.Env == enums.EnvEnum.DEV {
	//	return true
	//}
	if !strings.HasSuffix(basePath, "/") {
		basePath += "/"
	}
	if strings.HasPrefix(basePath, "/mnt/user-data/store0/") { // 0/33zfej14a47b/
		if len(basePath) < 30 {
			return false
		}
		re := `^/mnt/user-data/store0/\d+/[a-zA-Z0-9]{12}/$`
		// 编译正则表达式
		r, err := regexp.Compile(re)
		if err != nil {
			logger.Error(err)
			return false
		}
		return r.MatchString(basePath)
	}
	if strings.HasPrefix(basePath, "/mnt/pod-data/") { // /mnt/pod-data/7b0fe29ef29d430a8c7a97ea3378a407/
		if len(basePath) < 40 {
			return false
		}
		re := `^/mnt/pod-data/[a-zA-Z0-9]{32}/$`
		// 编译正则表达式
		r, err := regexp.Compile(re)
		if err != nil {
			logger.Error(err)
			return false
		}
		return r.MatchString(basePath)
	}
	return false
}

//-v /root/suanyun-share/7b0fe29ef29d430a8c7a97ea3378a407:/models:ro
//-v /root/suanyun-share/7b0fe29ef29d430a8c7a97ea3378a407:/poddata:ro
//-v /mnt/nvme/chenyu-nvme:/mnt/chenyu-nvme:ro
//-v /mnt/nvme/chenyu-nvme:/chenyudata:ro
//-v /root/suanyun-user/0/ebidfi71488a:/usrdata/chenyu
//-v /root/suanyun-user/0/ebidfi71488a/container/7b0fe29ef29d430a8c7a97ea3378a407:/appdata

/*
UserCatalogueMap := make(map[string]string)

UserCatalogueMap["AppdataInput"] = "/appdata/input"
UserCatalogueMap["AppdataOutput"] = "/appdata/output"

//ComfyUI用户目录
UserCatalogueMap["ComfyUIModels"] = "/usrdata/chenyu/ComyUI/models"                  //模型根目录
UserCatalogueMap["ComfyUICheckpoints"] = "/usrdata/chenyu/ComyUI/models/checkpoints" //主模型
UserCatalogueMap["ComfyUILora"] = "/usrdata/chenyu/ComyUI/models/loras"              //主模型
UserCatalogueMap["ComfyUIVAE"] = "/usrdata/chenyu/ComyUI/models/vae"                 //vae
UserCatalogueMap["ComfyUIUnet"] = "/usrdata/chenyu/ComyUI/models/unet"               //unet

//ComfyUI应用目录
UserCatalogueMap["ComfyUIAppdataInput"] = "/appdata/input"
UserCatalogueMap["ComfyUIAppdataOutput"] = "/appdata/output"

//ComfyUI创作者目录
UserCatalogueMap["ComfyUIPoddataModels"] = "/poddata/ComyUI/models"                  //模型根目录
UserCatalogueMap["ComfyUIPoddataCheckpoints"] = "/poddata/ComyUI/models/checkpoints" //主模型
UserCatalogueMap["ComfyUIPoddataLora"] = "/poddata/ComyUI/models/loras"              //lora目录
UserCatalogueMap["ComfyUIPoddataVae"] = "/poddata/ComyUI/models/vae"                 //vae
UserCatalogueMap["ComfyUIPoddataUnet"] = "/poddata/ComyUI/models/unet"               //unet

//SD用户目录
UserCatalogueMap["SdUsrdataModels"] = "/usrdata/chenyu/stable-diffusion-webui/models"                    //模型根目录
UserCatalogueMap["SdUsrdataModelsSd"] = "/usrdata/chenyu/stable-diffusion-webui/models/Stable-diffusion" //主模型目录
UserCatalogueMap["SdUsrdataLora"] = "/usrdata/chenyu/stable-diffusion-webui/models/Lora"                 //Lora
UserCatalogueMap["SdUsrdataVAE"] = "/usrdata/chenyu/stable-diffusion-webui/models/VAE"                   //VAE

//SD应用目录
UserCatalogueMap["SdAppdataInput"] = "/appdata/input"
UserCatalogueMap["SdAppdataOutput"] = "/appdata/output"

//SD创作者目录
UserCatalogueMap["SdPoddataModels"] = "/poddata/stable-diffusion-webui/models"                    //模型根目录
UserCatalogueMap["SdPoddataModelsSd"] = "/poddata/stable-diffusion-webui/models/Stable-diffusion" //主模型
UserCatalogueMap["SdPoddataModelsLora"] = "/poddata/stable-diffusion-webui/models/Lora"           //Lora
UserCatalogueMap["SdPoddataModelsVAE"] = "/poddata/stable-diffusion-webui/models/VAE"             //VAE

//Forge用户目录
UserCatalogueMap["ForgeUsrdataModels"] = "/usrdata/chenyu/stable-diffusion-webui-forge/models"                    //模型根目录
UserCatalogueMap["ForgeUsrdataModelsSd"] = "/usrdata/chenyu/stable-diffusion-webui-forge/models/Stable-diffusion" //主模型目录
UserCatalogueMap["ForgeUsrdataLora"] = "/usrdata/chenyu/stable-diffusion-webui-forge/models/Lora"                 //Lora
UserCatalogueMap["ForgeUsrdataVae"] = "/usrdata/chenyu/stable-diffusion-webui-forge/models/VAE"                   //VAE

//Forge应用目录
UserCatalogueMap["ForgeAppdataInput"] = "/appdata/input"
UserCatalogueMap["ForgeAppdataOutput"] = "/appdata/output"

//Forge创作者目录
UserCatalogueMap["ForgePoddataModels"] = "/poddata/stable-diffusion-webui-forge/models"                    //模型根目录
UserCatalogueMap["ForgePoddataModelsSd"] = "/poddata/stable-diffusion-webui-forge/models/Stable-diffusion" //主模型
UserCatalogueMap["ForgePoddataModelsLora"] = "/poddata/stable-diffusion-webui-forge/models/Lora"           //Lora
UserCatalogueMap["ForgePoddataModelsVAE"] = "/poddata/stable-diffusion-webui-forge/models/VAE"             //VAE
*/

package service

import (
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/model"
	"cpn-ai/structs"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

type _statDay struct {
	GeningStatMap sync.Map
}

var StatDayService _statDay

func (obj *_statDay) RunEveryday() error {
	commandKey := "StatDayRunEveryday"
	atomic.AddInt32(&TaskRunningCount, 1)
	commandRunningValue := structs.RunningCommand{Command: "每日统计数据", StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	TaskRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&TaskRunningCount, -1)
		TaskRunning.Delete(commandKey)
	}()

	//startAt := time.Now().AddDate(0, 0, -30)
	startAt := time.Date(2024, 1, 3, 0, 0, 0, 0, time.Now().Location())

	var needStatDay model.StatDay
	if err := needStatDay.GetFirstNeedStat(); err != nil {
		if err != gorm.ErrRecordNotFound {
			logger.Error(err)
			return err
		}
	} else {
		statDayStr := utils.Int642String(needStatDay.StatDay) //20241128
		if len(statDayStr) != 8 {
			msg := "日期参数错误"
			return errors.New(msg)
		}
		day := utils.String2Int(statDayStr[6:])
		if day == 0 {
			msg := "日期参数错误"
			return errors.New(msg)
		}
		startAt = time.Date(utils.String2Int(statDayStr[:4]), time.Month(utils.String2Int(statDayStr[4:6])), day, 0, 0, 0, 0, time.Now().Location())
	}

	logger.Info("开始统计每日数据 startAt:", startAt)
	for i := 0; i <= 3650; i++ {
		statAt := startAt.AddDate(0, 0, i)
		if err := obj.GenStat(statAt); err != nil {
			logger.Error("每日统计失败, statAt:", statAt, " err:", err)
			return err
		} else {
			logger.Info("每日统计成功, statAt:", statAt)
		}
		time.Sleep(time.Second)
	}
	logger.Info("统计每日数据结束 startAt:", startAt)
	return nil
}

func (obj *_statDay) RunRepairday() error {
	commandKey := "StatDayRunRepairday"
	atomic.AddInt32(&TaskRunningCount, 1)
	commandRunningValue := structs.RunningCommand{Command: "修复每日统计数据", StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	TaskRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&TaskRunningCount, -1)
		TaskRunning.Delete(commandKey)
	}()

	//startAt := time.Now().AddDate(0, 0, -30)
	startAt := time.Date(2024, 1, 3, 0, 0, 0, 0, time.Now().Location())

	//var needStatDay model.StatDay
	//if err := needStatDay.GetFirstNeedStat(); err != nil {
	//	if err != gorm.ErrRecordNotFound {
	//		logger.Error(err)
	//		return err
	//	}
	//} else {
	//	statDayStr := utils.Int642String(needStatDay.StatDay) //20241128
	//	if len(statDayStr) != 8 {
	//		msg := "日期参数错误"
	//		return errors.New(msg)
	//	}
	//	day := utils.String2Int(statDayStr[6:])
	//	if day == 0 {
	//		msg := "日期参数错误"
	//		return errors.New(msg)
	//	}
	//	startAt = time.Date(utils.String2Int(statDayStr[:4]), time.Month(utils.String2Int(statDayStr[4:6])), day, 0, 0, 0, 0, time.Now().Location())
	//}

	logger.Info("开始修复每日统计数据 startAt:", startAt)
	for i := 0; i <= 3650; i++ {
		statAt := startAt.AddDate(0, 0, i)
		if err := obj.RepairStat(statAt); err != nil {
			logger.Error("修复每日统计数据失败, statAt:", statAt, " err:", err)
			return err
		} else {
			logger.Info("修复每日统计数据成功, statAt:", statAt)
		}
		time.Sleep(time.Second)
	}
	logger.Info("修复统计每日数据结束 startAt:", startAt)
	return nil
}

func (obj *_statDay) RepairStat(statAt time.Time) error {
	trace := fmt.Sprintf("statAt:%v ", statAt)
	msg := ""
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	tomorrow := today.AddDate(0, 0, 1)
	if statAt.After(tomorrow) || statAt.Format("20060102") == tomorrow.Format("20060102") {
		msg = "还未到统计日期"
		err := errors.New(msg)
		logger.Error(err, " statAt:", statAt)
		return err
	}
	if statAt.Before(time.Date(2024, 1, 1, 0, 0, 0, 0, now.Location())) {
		err := errors.New("该统计时间太早了，项目还没诞生哦")
		logger.Error(trace, err)
		return err
	}

	statAtTime := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statAtTomorrow := statAtTime.AddDate(0, 0, 1)
	statDayNum := utils.String2Int64(statAt.Format("20060102"))
	if statDayNum == 0 {
		msg = "统计日期参数错误"
		err := errors.New(msg)
		logger.Error(err, " statAt:", statAt)
		return err
	}

	if val, ok := obj.GeningStatMap.Load(statDayNum); ok {
		err := errors.New(fmt.Sprintf("日期%d正在统计中 %s", statDayNum, val))
		logger.Error(err)
		return err
	}

	obj.GeningStatMap.Store(statDayNum, time.Now())
	defer func() {
		obj.GeningStatMap.Delete(statDayNum)
	}()

	//statAtIsLastDay := false
	//if statAt.Before(today) {
	//	statAtIsLastDay = true
	//}

	var statDay model.StatDay

	if err := statDay.GetByStatDay(statDayNum); err != nil {
		msg = "查询记录失败"
		logger.Error(msg, err)
		return err
	} else {

	}

	if time.Now().Before(common.NationalDay) || true {
		type simpleInstRecord struct {
			ID           uint      `json:"id"`
			InstanceType int       `json:"instance_type"`
			UserId       uint      `json:"user_id"`
			PodId        uint      `json:"pod_id"`
			StartupTime  time.Time `json:"startup_time"`
			ShutdownTime time.Time `json:"shutdown_time"`
			Status       int       `json:"status"`
		}

		type podUsageData struct {
			UsersStr string `json:"-"`
			PodId    uint   `json:"-"`
			Users    int    `json:"users"`
			Usages   int    `json:"usages"`
			Duration uint   `json:"duration"`
		}

		mmPodId := make(map[uint]*podUsageData)
		mmUserId := make(map[uint]time.Time)
		dayPodUsageIds := 0            //单日使用过的Pod数量，不会超过Pod总数
		dayPodUsageUsers := 0          //单日使用Pod的人数，不重复计算
		dayPodUsageCount := 0          //单日所有Pod使用次数
		dayPodUsageDuration := uint(0) //单日所有Pod使用时长

		lastStartupTime := common.DefaultTime
		limit := 50
		for {
			ary := make([]simpleInstRecord, 0)
			if err := statDay.StatDayInstRecordList(lastStartupTime, limit, statAt, &ary); err != nil {
				logger.Error(err)
				return err
			}
			for i := 0; i < len(ary); i++ {
				item := ary[i]
				lastStartupTime = item.StartupTime
				if item.Status == enums.InstanceStatusEnum.StartupFail {
					//logger.Info(statDayNum, "启动失败实例，不统计", utils.GetJsonFromStruct(item))
					continue
				}
				if item.InstanceType == enums.InstanceTypeEnum.Kol {
					logger.Info(statDayNum, "Kol镜像，不统计", utils.GetJsonFromStruct(item))
					continue
				}

				if item.StartupTime.Before(common.NationalDay) { //没启动
					logger.Error(statDayNum, "没启动", utils.GetJsonFromStruct(item))
					continue
				}
				if item.StartupTime.After(statAtTomorrow) { //启动时间在统计时间之后
					logger.Error(statDayNum, "启动时间在统计时间之后", utils.GetJsonFromStruct(item))
					continue
				}

				if item.ShutdownTime.After(common.NationalDay) && item.ShutdownTime.Before(item.StartupTime) {
					logger.Error(statDayNum, "启动时间关机时间有问题", utils.GetJsonFromStruct(item))
					continue
				}

				if item.ShutdownTime.After(common.NationalDay) && item.ShutdownTime.Before(statAtTime) {
					logger.Error(statDayNum, "在统计时间之前就已经关机了", utils.GetJsonFromStruct(item))
					continue
				}

				dayPodUsageCount++

				if _, ok := mmUserId[item.UserId]; !ok {
					mmUserId[item.UserId] = time.Now()
				}

				durationStart := statAtTime
				if item.StartupTime.After(statAtTime) {
					durationStart = item.StartupTime
				}

				durationEnd := common.DefaultTime
				if item.ShutdownTime.Before(item.StartupTime) { //没关机
					durationEnd = statAtTomorrow
				} else {
					if item.ShutdownTime.Before(statAtTomorrow) { //统计当天关机的
						durationEnd = item.ShutdownTime
					} else { //统计当天之后关机的
						durationEnd = statAtTomorrow
					}
				}

				duration := durationEnd.Sub(durationStart)
				seconds := uint(duration.Seconds())
				if seconds < 0 {
					err := errors.New("使用时长错误")
					logger.Error(statDayNum, err, utils.GetJsonFromStruct(item))
					return err
				}

				dayPodUsageDuration += seconds

				if _, ok := mmPodId[item.PodId]; ok {
					if !strings.Contains(mmPodId[item.PodId].UsersStr, fmt.Sprintf("|%d|", item.UserId)) {
						mmPodId[item.PodId].UsersStr += fmt.Sprintf("%d|", item.UserId)
						mmPodId[item.PodId].Users += 1
					}
					mmPodId[item.PodId].Usages += 1
					mmPodId[item.PodId].Duration += seconds
				} else {
					podStatDataItem := podUsageData{
						UsersStr: fmt.Sprintf("|%d|", item.UserId),
						PodId:    item.PodId,
						Users:    1,
						Usages:   1,
						Duration: seconds,
					}
					mmPodId[item.PodId] = &podStatDataItem
				}
			}
			if len(ary) < limit {
				break
			}
		}
		dayPodUsageIds = len(mmPodId)
		dayPodUsageUsers = len(mmUserId)

		//statDay.DayPodUsageIds = dayPodUsageIds
		//statDay.DayPodUsageUsers = dayPodUsageUsers
		//statDay.DayPodUsageCount = dayPodUsageCount
		//statDay.DayPodUsageDuration = dayPodUsageDuration
		//statDay.DayPodUsageData = utils.GetJsonFromStruct(mmPodId)

		mm := make(map[string]interface{})
		mm["day_pod_usage_ids"] = dayPodUsageIds
		mm["day_pod_usage_users"] = dayPodUsageUsers
		mm["day_pod_usage_count"] = dayPodUsageCount
		mm["day_pod_usage_duration"] = dayPodUsageDuration
		mm["day_pod_usage_data"] = utils.GetJsonFromStruct(mmPodId)

		if err := statDay.Updates(mm); err != nil {
			msg = "保存修复数据失败"
			logger.Error(msg, err)
			return err
		}
	}
	return nil
}

func (obj *_statDay) GenStat(statAt time.Time) error {
	lockKey := ""
	defer func() {
		if lockKey != "" {
			common.RedisUnLock(lockKey)
		}
	}()
	lockKey = enums.RedisKeyEnum.LockKey + "StatDay_GenStat"
	if common.RedisLock(lockKey, 1, 1000*60*5) {

	} else {
		lockKey = ""
		return errors.New("正在统计中，请稍后操作")
	}

	trace := fmt.Sprintf("statAt:%v ", statAt)
	msg := ""
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	tomorrow := today.AddDate(0, 0, 1)
	if statAt.After(tomorrow) || statAt.Format("20060102") == tomorrow.Format("20060102") {
		msg = "还未到统计日期"
		err := errors.New(msg)
		logger.Error(err, " statAt:", statAt)
		return err
	}
	if statAt.Before(time.Date(2024, 1, 1, 0, 0, 0, 0, now.Location())) {
		err := errors.New("该统计时间太早了，项目还没诞生哦")
		logger.Error(trace, err)
		return err
	}

	statAtTime := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statAtTomorrow := statAtTime.AddDate(0, 0, 1)
	statDayNum := utils.String2Int64(statAt.Format("20060102"))
	if statDayNum == 0 {
		msg = "统计日期参数错误"
		err := errors.New(msg)
		logger.Error(err, " statAt:", statAt)
		return err
	}

	if val, ok := obj.GeningStatMap.Load(statDayNum); ok {
		err := errors.New(fmt.Sprintf("日期%d正在统计中 %s", statDayNum, val))
		logger.Error(err)
		return err
	}

	obj.GeningStatMap.Store(statDayNum, time.Now())
	defer func() {
		obj.GeningStatMap.Delete(statDayNum)
	}()

	statAtIsLastDay := false
	if statAt.Before(today) {
		statAtIsLastDay = true
	}

	var statDay model.StatDay

	if err := statDay.GetByStatDay(statDayNum); err != nil {
		if err != gorm.ErrRecordNotFound {
			msg = "查询记录失败"
			logger.Error(msg, err)
			return err
		}
	} else {
		if statDay.Status == 1 {
			msg = "记录已统计完成，不做统计 "
			logger.Info(trace, msg, statAt)
			return nil
		}
	}
	if statDay.ID == 0 {
		statDay.StatDay = statDayNum
	}
	statDay.StatAt = now

	if totalCount, err := statDay.StatTotalRegisterUsers(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.TotalRegisterUsers = totalCount
	}
	logger.Info("statDay.StatTotalRegisterUsers")

	if totalCount, err := statDay.StatTotalCertUsers(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.TotalCertUsers = totalCount
	}
	logger.Info("statDay.StatTotalCertUsers")

	if totalCount, err := statDay.StatTotalRechargeCount(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.TotalRechargeCount = totalCount
	}
	logger.Info("statDay.StatTotalRechargeCount")

	if totalCount, err := statDay.StatTotalRechargeUsers(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.TotalRechargeUsers = totalCount
	}
	logger.Info("statDay.StatTotalRechargeUsers")

	if totalAmount, err := statDay.StatTotalRechargeAmount(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.TotalRechargeAmount = totalAmount
	}
	logger.Info("statDay.StatTotalRechargeAmount")

	if totalAmount, err := statDay.StatTotalRechargeMaxAmount(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.TotalRechargeMaxAmount = totalAmount
	}
	logger.Info("statDay.StatTotalRechargeMaxAmount")

	if totalCount, err := statDay.StatTotalRechargeRemainUsers(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.TotalRechargeRemainUsers = totalCount
	}
	logger.Info("statDay.StatTotalRechargeRemainUsers")

	if totalAmount, err := statDay.StatTotalRechargeRemainAmount(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.TotalRechargeRemainAmount = totalAmount
	}
	logger.Info("statDay.StatTotalRechargeRemainAmount")

	if totalCount, err := statDay.StatTotalBuyCardUsers(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.TotalBuyCardUsers = totalCount
	}
	logger.Info("statDay.StatTotalBuyCardUsers")

	if totalAmount, err := statDay.StatTotalBuyCardAmount(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.TotalBuyCardAmount = totalAmount
	}
	logger.Info("statDay.StatTotalBuyCardAmount")

	if totalAmount, err := statDay.StatTotalCostAmount(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.TotalCostAmount = totalAmount
	}
	logger.Info("statDay.StatTotalCostAmount")

	if totalAmount, err := statDay.StatTotalCostRemainAmount(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.TotalCostRemainAmount = totalAmount
	}
	logger.Info("statDay.StatTotalCostRemainAmount")

	if totalAmount, err := statDay.StatTotalCostCardAmount(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.TotalCostCardAmount = totalAmount
	}
	logger.Info("statDay.StatTotalCostCardAmount")

	//开始当日统计
	if totalCount, err := statDay.StatDayRegisterUsers(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.DayRegisterUsers = totalCount
	}
	logger.Info("statDay.StatDayRegisterUsers")

	if totalCount, err := statDay.StatDayCertUsers(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.DayCertUsers = totalCount
	}
	logger.Info("statDay.StatDayCertUsers")

	{
		type simpleRecharge struct {
			UserId  uint            `json:"user_id"`
			Amount  decimal.Decimal `json:"amount"`
			PayTime time.Time       `json:"pay_time"`
		}

		ary := make([]simpleRecharge, 0)
		if err := statDay.StatDayRechargeList(statAt, &ary); err != nil {
			logger.Error(err)
			return err
		}
		mmUserId := make(map[uint]time.Time)
		mmRegisterTime := make(map[uint]time.Time)
		rechargeRegisterUsers := 0
		rechargeFirstUsers := 0
		rechargeRepeatUsers := 0
		for _, item := range ary {
			if userRegisterTime, firstRechargeTime, err := obj.StatRecharge(item.UserId, item.PayTime); err != nil {
				logger.Error(err, " userId:", item.UserId)
				return err
			} else {
				if _, ok := mmUserId[item.UserId]; ok {
					//存在
				} else {
					mmUserId[item.UserId] = firstRechargeTime
					mmRegisterTime[item.UserId] = userRegisterTime
					if firstRechargeTime.Format("2006-01-02") == statAt.Format("2006-01-02") {
						rechargeFirstUsers += 1
						if userRegisterTime.Format("2006-01-02") == statAt.Format("2006-01-02") {
							rechargeRegisterUsers += 1
						}
					} else {
						rechargeRepeatUsers += 1
					}
				}
			}
		}
		statDay.DayRechargeRegisterUsers = rechargeRegisterUsers
		statDay.DayRechargeFirstUsers = rechargeFirstUsers
		statDay.DayRechargeRepeatUsers = rechargeRepeatUsers
	}
	logger.Info("statDay.StatDayRechargeList")

	if totalCount, err := statDay.StatDayRechargeCount(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.DayRechargeCount = totalCount
	}
	logger.Info("statDay.StatDayRechargeCount")

	if totalCount, err := statDay.StatDayRechargeUsers(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.DayRechargeUsers = totalCount
	}
	logger.Info("statDay.StatDayRechargeUsers")

	if totalAmount, err := statDay.StatDayRechargeAmount(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.DayRechargeAmount = totalAmount
	}
	logger.Info("statDay.StatDayRechargeAmount")

	if totalAmount, err := statDay.StatDayRechargeMaxAmount(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.DayRechargeMaxAmount = totalAmount
	}
	logger.Info("statDay.StatDayRechargeMaxAmount")

	if totalCount, err := statDay.StatDayRechargeRemainUsers(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.DayRechargeRemainUsers = totalCount
	}
	logger.Info("statDay.StatDayRechargeRemainUsers")

	if totalAmount, err := statDay.StatDayRechargeRemainAmount(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.DayRechargeRemainAmount = totalAmount
	}
	logger.Info("statDay.StatDayRechargeRemainAmount")

	if totalCount, err := statDay.StatDayBuyCardUsers(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.DayBuyCardUsers = totalCount
	}
	logger.Info("statDay.StatDayBuyCardUsers")

	if totalAmount, err := statDay.StatDayBuyCardAmount(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.DayBuyCardAmount = totalAmount
	}
	logger.Info("statDay.StatDayBuyCardAmount")

	if totalAmount, err := statDay.StatDayCostAmount(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.DayCostAmount = totalAmount
	}
	logger.Info("statDay.StatDayCostAmount")

	if totalAmount, err := statDay.StatDayCostRemainAmount(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.DayCostRemainAmount = totalAmount
	}
	logger.Info("statDay.StatDayCostRemainAmount")

	if totalAmount, err := statDay.StatDayCostCardAmount(statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		statDay.DayCostCardAmount = totalAmount
	}
	logger.Info("statDay.StatDayCostCardAmount")

	type detailCostAmount struct {
		OrderType   int             `json:"order_type"`
		TotalAmount decimal.Decimal `json:"total_amount"`
	}
	detailCostAry := make([]detailCostAmount, 0)
	if err := statDay.StatDayCostDetailAmount(&detailCostAry, statAt); err != nil {
		logger.Error(err)
		return err
	} else {
		costOtherAmount := decimal.Zero
		for _, item := range detailCostAry {
			if item.OrderType == enums.OrderTypeEnum.CostINST {
				statDay.DayCostInstAmount = item.TotalAmount
			} else if item.OrderType == enums.OrderTypeEnum.CostPod {
				statDay.DayCostPodAmount = item.TotalAmount
			} else if item.OrderType == enums.OrderTypeEnum.ImageStore {
				statDay.DayCostImageStore = item.TotalAmount
			} else if item.OrderType == enums.OrderTypeEnum.CloudStore {
				statDay.DayCostCloudStore = item.TotalAmount
			} else {
				costOtherAmount = costOtherAmount.Add(item.TotalAmount)
			}
		}
		statDay.DayCostOtherAmount = costOtherAmount
	}
	logger.Info("statDay.StatDayCostDetailAmount")

	if time.Now().Before(common.NationalDay) || true {
		type simpleInstRecord struct {
			ID           uint      `json:"id"`
			InstanceType int       `json:"instance_type"`
			UserId       uint      `json:"user_id"`
			PodId        uint      `json:"pod_id"`
			StartupTime  time.Time `json:"startup_time"`
			ShutdownTime time.Time `json:"shutdown_time"`
			Status       int       `json:"status"`
		}

		type podUsageData struct {
			UsersStr string `json:"-"`
			PodId    uint   `json:"-"`
			Users    int    `json:"users"`
			Usages   int    `json:"usages"`
			Duration uint   `json:"duration"`
		}

		mmPodId := make(map[uint]*podUsageData)
		mmUserId := make(map[uint]time.Time)
		dayPodUsageIds := 0            //单日使用过的Pod数量，不会超过Pod总数
		dayPodUsageUsers := 0          //单日使用Pod的人数，不重复计算
		dayPodUsageCount := 0          //单日所有Pod使用次数
		dayPodUsageDuration := uint(0) //单日所有Pod使用时长

		lastStartupTime := common.DefaultTime
		limit := 50
		for {
			ary := make([]simpleInstRecord, 0)
			if err := statDay.StatDayInstRecordList(lastStartupTime, limit, statAt, &ary); err != nil {
				logger.Error(err)
				return err
			}
			for i := 0; i < len(ary); i++ {
				item := ary[i]
				lastStartupTime = item.StartupTime
				if item.Status == enums.InstanceStatusEnum.StartupFail {
					//logger.Info(statDayNum, "启动失败实例，不统计", utils.GetJsonFromStruct(item))
					continue
				}
				if item.InstanceType == enums.InstanceTypeEnum.Kol {
					logger.Info(statDayNum, "Kol镜像，不统计", utils.GetJsonFromStruct(item))
					continue
				}

				if item.StartupTime.Before(common.NationalDay) { //没启动
					logger.Error(statDayNum, "没启动", utils.GetJsonFromStruct(item))
					continue
				}
				if item.StartupTime.After(statAtTomorrow) { //启动时间在统计时间之后
					logger.Error(statDayNum, "启动时间在统计时间之后", utils.GetJsonFromStruct(item))
					continue
				}

				if item.ShutdownTime.After(common.NationalDay) && item.ShutdownTime.Before(item.StartupTime) {
					logger.Error(statDayNum, "启动时间关机时间有问题", utils.GetJsonFromStruct(item))
					continue
				}

				if item.ShutdownTime.After(common.NationalDay) && item.ShutdownTime.Before(statAtTime) {
					logger.Error(statDayNum, "在统计时间之前就已经关机了", utils.GetJsonFromStruct(item))
					continue
				}

				dayPodUsageCount++

				if _, ok := mmUserId[item.UserId]; !ok {
					mmUserId[item.UserId] = time.Now()
				}

				durationStart := statAtTime
				if item.StartupTime.After(statAtTime) {
					durationStart = item.StartupTime
				}

				durationEnd := common.DefaultTime
				if item.ShutdownTime.Before(item.StartupTime) { //没关机
					durationEnd = statAtTomorrow
				} else {
					if item.ShutdownTime.Before(statAtTomorrow) { //统计当天关机的
						durationEnd = item.ShutdownTime
					} else { //统计当天之后关机的
						durationEnd = statAtTomorrow
					}
				}

				duration := durationEnd.Sub(durationStart)
				seconds := uint(duration.Seconds())
				if seconds < 0 {
					err := errors.New("使用时长错误")
					logger.Error(statDayNum, err, utils.GetJsonFromStruct(item))
					return err
				}

				dayPodUsageDuration += seconds

				if _, ok := mmPodId[item.PodId]; ok {
					if !strings.Contains(mmPodId[item.PodId].UsersStr, fmt.Sprintf("|%d|", item.UserId)) {
						mmPodId[item.PodId].UsersStr += fmt.Sprintf("%d|", item.UserId)
						mmPodId[item.PodId].Users += 1
					}
					mmPodId[item.PodId].Usages += 1
					mmPodId[item.PodId].Duration += seconds
				} else {
					podStatDataItem := podUsageData{
						UsersStr: fmt.Sprintf("|%d|", item.UserId),
						PodId:    item.PodId,
						Users:    1,
						Usages:   1,
						Duration: seconds,
					}
					mmPodId[item.PodId] = &podStatDataItem
				}
			}
			if len(ary) < limit {
				break
			}
		}
		dayPodUsageIds = len(mmPodId)
		dayPodUsageUsers = len(mmUserId)
		statDay.DayPodUsageIds = dayPodUsageIds
		statDay.DayPodUsageUsers = dayPodUsageUsers
		statDay.DayPodUsageCount = dayPodUsageCount
		statDay.DayPodUsageDuration = dayPodUsageDuration
		//
		//// 转为切片
		//var sortedPods []*podUsageData
		//for _, data := range mmPodId {
		//	sortedPods = append(sortedPods, data)
		//}
		//
		//// 按 Duration 从大到小排序
		//sort.Slice(sortedPods, func(i, j int) bool {
		//	return sortedPods[i].Duration > sortedPods[j].Duration
		//})
		statDay.DayPodUsageData = utils.GetJsonFromStruct(mmPodId)
	}
	logger.Info("statDay.StatDayInstRecordList")

	if statAtIsLastDay {
		statDay.Status = 1

		yesStatDayNum := utils.String2Int64(statAt.AddDate(0, 0, -1).Format("20060102"))
		var yesStatDay model.StatDay
		if err := yesStatDay.GetByStatDay(yesStatDayNum); err != nil {
			if err != gorm.ErrRecordNotFound {
				msg = "查询记录失败"
				logger.Error(msg, err)
				return err
			}
		} else {
			if yesStatDay.Status == 1 {
				if yesStatDay.DayRegisterUsers > 0 {
					diff := statDay.DayRegisterUsers - yesStatDay.DayRegisterUsers
					dayOn := float64(diff) / float64(yesStatDay.DayRegisterUsers)
					statDay.DayonRegisterUsers = decimal.NewFromFloat(dayOn)
				}
				if yesStatDay.DayRechargeUsers > 0 {
					diff := statDay.DayRechargeUsers - yesStatDay.DayRechargeUsers
					dayOn := float64(diff) / float64(yesStatDay.DayRechargeUsers)
					statDay.DayonRechargeUsers = decimal.NewFromFloat(dayOn)
				}
			}
		}
	}
	if err := statDay.Save(); err != nil {
		logger.Error(trace, " 更新奖励记录失败 err:", err)
		return err
	}

	return nil
}

func (obj *_statDay) StatRecharge(userId uint, payTime time.Time) (time.Time, time.Time, error) {

	pre := fmt.Sprintf("统计用户充值情况 userId:%d ", userId)
	var user model.User
	if err := user.GetById(userId); err != nil {
		logger.Error(err)
		return user.CreatedAt, user.RechargeFirstAt, err
	}

	if user.RechargeLastAt.Equal(payTime) || user.RechargeLastAt.After(payTime) {
		return user.CreatedAt, user.RechargeFirstAt, nil
	}

	firstRechargeTime := user.RechargeFirstAt
	lastRechargeTime := user.RechargeLastAt
	lastPayTime := user.RechargeLastAt
	size := 50
	totalCount := user.RechargeCount
	totalAmount := user.RechargeAmount
	for {
		var recharge model.Recharge
		ary, err := recharge.ListForUserStat(userId, lastPayTime, size)
		if err != nil {
			logger.Error(pre, err, "  lastPayTime:", lastPayTime)
			return user.CreatedAt, firstRechargeTime, err
		}
		logger.Info(pre, "查询到", len(ary), "条需要统计的充值数据")
		for _, item := range ary {
			lastPayTime = item.PayTime

			if firstRechargeTime.Before(common.NationalDay) {
				firstRechargeTime = item.PayTime
			}
			if item.PayTime.After(lastRechargeTime) {
				lastRechargeTime = item.PayTime
			}

			totalCount += 1
			totalAmount = totalAmount.Add(item.Amount)
		}
		if len(ary) < size {
			logger.Info(pre, "查询到", len(ary), "条需要的统计的充值数据,已经小于size:", size, "条，结束轮询")
			break
		}
	}
	if err := user.SetRechargeStat(totalCount, totalAmount, firstRechargeTime, lastRechargeTime); err != nil {
		logger.Error(err)
		return user.CreatedAt, firstRechargeTime, err
	}

	return user.CreatedAt, firstRechargeTime, nil

}

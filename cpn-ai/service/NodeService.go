package service

import (
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/model"
	"cpn-ai/service/tasklog"
	"cpn-ai/structs"
	"errors"
	"fmt"
	"github.com/docker/docker/api/types"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/ssh"
)

type node_ struct {
}

var NodeService node_

func (obj *node_) TasklogList(nodeId uint, logKey string) (gin.H, error) {
	postUrl := "api/node/tasklog/list"
	postData := make(map[string]interface{})
	postData["log_key"] = logKey
	return PostNodeForGin(nodeId, postUrl, postData)
}

func (obj *node_) TasklogLast(nodeId uint, logKey string) (gin.H, error) {
	postUrl := "api/node/tasklog/last"
	postData := make(map[string]interface{})
	postData["log_key"] = logKey
	return PostNodeForGin(nodeId, postUrl, postData)
}

func (obj *node_) TasklogBootIn(nodeId uint, startupMark string) (bool, gin.H, error) {
	if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, startupMark); err != nil {
		logger.Error(err)
		return false, nil, err
	} else {
		postUrl := "api/node/tasklog/bootin"
		postData := make(map[string]interface{})
		postData["log_key"] = logKey
		if ginH, err := PostNodeForGin(nodeId, postUrl, postData); err != nil {
			return false, nil, err
		} else {
			if rGinH, err := ResultGinH(ginH); err != nil {
				return false, nil, err
			} else {
				if rGinH.Code == 0 {
					if _, ok := rGinH.Result["boot_in"]; ok {
						return rGinH.Result["boot_in"].(bool), ginH, nil
					} else {
						return rGinH.Result["boot_in"].(bool), ginH, errors.New("bootin不存在")
					}
				} else {
					return false, ginH, errors.New(rGinH.Msg)
				}
			}
		}
	}
}

func (obj *node_) StopDocker(nodeId uint, virtualId uint, startupMark string) (bool, string, error) {

	postUrl := "api/node/docker/stop"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["startup_mark"] = startupMark
	mm, _ := PostNode(nodeId, postUrl, postData)
	if ginH, err := Result(mm); err != nil {
		logger.Error(err)
		return false, "请求失败", err
	} else {
		if ginH.Code == 0 {
			return true, ginH.Msg, nil
		} else {
			logger.Error(ginH)
			return false, ginH.Msg, nil
		}
	}
}

func (obj *node_) StartDocker(nodeId uint, virtualId uint, startupMark string) (bool, string, error) {

	postUrl := "api/node/docker/start"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["startup_mark"] = startupMark
	mm, _ := PostNode(nodeId, postUrl, postData)
	if ginH, err := Result(mm); err != nil {
		logger.Error(err)
		return false, "请求失败", err
	} else {
		if ginH.Code == 0 {
			return true, ginH.Msg, nil
		} else {
			logger.Error(ginH)
			return false, ginH.Msg, nil
		}
	}
}

func (obj *node_) ReStartDocker11(nodeId uint, virtualId uint, startupMark string) (bool, string, error) {

	postUrl := "api/node/docker/restart"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["startup_mark"] = startupMark
	mm, _ := PostNode(nodeId, postUrl, postData)
	if ginH, err := Result(mm); err != nil {
		logger.Error(err)
		return false, "请求失败", err
	} else {
		if ginH.Code == 0 {
			return true, ginH.Msg, nil
		} else {
			logger.Error(ginH)
			return false, ginH.Msg, nil
		}
	}
}

func (obj *node_) ReStartDocker(nodeId uint, virtualId uint, startupMark string) (gin.H, error) {
	postUrl := "api/node/docker/restart"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["startup_mark"] = startupMark
	return PostNodeForGin(nodeId, postUrl, postData)
}

func (obj *node_) SetNginx(nodeId uint, virtualId uint, startupMark string) (gin.H, error) {
	postUrl := "api/node/docker/set_nginx"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["startup_mark_key"] = startupMark + "88"
	return GetNodeForGin(nodeId, postUrl, postData)
}

func (obj *node_) LogsDocker(nodeId uint, virtualId uint, startupMark string) (gin.H, error) {

	postUrl := "api/node/docker/logs"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["startup_mark"] = startupMark
	return PostNodeForGin(nodeId, postUrl, postData)
}

func (obj *node_) CommitDocker(nodeId uint, virtualId uint, startupMark string, newImageId uint, userPath string) (gin.H, error) {

	postUrl := "api/node/docker/commit_image"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["startup_mark"] = startupMark
	postData["new_image_id"] = newImageId
	postData["user_path"] = userPath
	return PostNodeForGin(nodeId, postUrl, postData)
}

func (obj *node_) SaveImageDockerKol(nodeId uint, virtualId uint, startupMark string, newImageId uint) (gin.H, error) {

	postUrl := "api/node/docker/save_image"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["startup_mark"] = startupMark
	postData["new_image_id"] = newImageId
	postData["storage_mode"] = enums.ImageStorageModeEnum.Registry
	return PostNodeForGin(nodeId, postUrl, postData)
}

func (obj *node_) SaveImageDocker(nodeId uint, virtualId uint, startupMark string, newImageId uint, storageMode int, task string, userPath string, shutdown bool) (gin.H, error) {

	postUrl := "api/node/docker/save_image"
	//if shutdown {
	//	postUrl = "api/node/docker/save_image_and_shutdown"
	//}
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["startup_mark"] = startupMark
	postData["new_image_id"] = newImageId
	postData["storage_mode"] = storageMode
	postData["task"] = task
	postData["user_path"] = userPath
	postData["shutdown"] = shutdown
	return PostNodeForGin(nodeId, postUrl, postData)
}

func (obj *node_) SaveImageDockerAbort(nodeId uint, virtualId uint, startupMark string, newImageId uint, shutdown bool) (gin.H, error) {

	postUrl := "api/node/docker/save_image_abort"
	//if shutdown {
	//	postUrl = "api/node/docker/save_image_and_shutdown"
	//}
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["startup_mark"] = startupMark
	postData["new_image_id"] = newImageId
	postData["shutdown"] = shutdown
	return PostNodeForGin(nodeId, postUrl, postData)
}

func (obj *node_) StartupAbort(nodeId uint, virtualId uint, startupMark string) (gin.H, error) {

	postUrl := "api/node/docker/startup_abort"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["startup_mark"] = startupMark
	return PostNodeForGin(nodeId, postUrl, postData)
}

func (obj *node_) PullImage(nodeId uint, virtualId uint, imageId uint) (gin.H, error) {

	postUrl := "api/node/docker/pull_image"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["image_id"] = imageId
	return PostNodeForGin(nodeId, postUrl, postData)
}

func (obj *node_) GetNodeDetail(nodeId uint) (gin.H, error) {

	postUrl := "api/node/detail"
	postData := make(map[string]interface{})
	return PostNodeForGin(nodeId, postUrl, postData)
}

func (obj *node_) NodeAction(nodeId uint, postData map[string]interface{}) (gin.H, error) {

	postUrl := "api/node/action"
	return PostNodeForGin(nodeId, postUrl, postData)
}

func (obj *node_) GetNodeRunningCommand(nodeId uint) (gin.H, error) {

	postUrl := "api/node/running_command"
	postData := make(map[string]interface{})
	return PostNodeForGin(nodeId, postUrl, postData)
}

func (obj *node_) ActionVirtual(virtualId uint, postData map[string]interface{}) (gin.H, error) {
	var virtual model.Virtual
	if err := virtual.GetById(virtualId); err != nil {
		logger.Error(err, virtualId)
		return nil, err
	}
	postUrl := "api/node/virtual/action"
	//postData := make(map[string]interface{})
	//postData["virtual_id"] = virtualId
	return PostNodeForGin(virtual.NodeId, postUrl, postData)
}

func (obj *node_) ResetCacheShutdown(virtualId uint, startupMark string, keepSeconds int64) (string, error) {
	postData := make(map[string]interface{})
	postData["action"] = "ResetCacheShutdown"
	postData["virtual_id"] = virtualId
	postData["startup_mark"] = startupMark
	postData["keep_seconds"] = keepSeconds

	if ginH, err := NodeService.ActionVirtual(virtualId, postData); err != nil {
		logger.Error(err)
		return "请求失败", err
	} else {
		if ginR, err := Result(ginH); err != nil {
			return "解析失败", err
		} else {
			if ginR.Code == 0 {
				return ginR.Msg, nil
			} else {
				return ginR.Msg, errors.New(ginR.Msg)
			}
		}
	}
}

func (obj *node_) ContainerFullInfo(startupMark string) (types.Container, error) {
	postData := make(map[string]interface{})
	var containerInfo types.Container
	var instRecord model.InstRecord
	if err := instRecord.GetByStartupMark(startupMark); err != nil {
		logger.Error(err, startupMark)
		return containerInfo, err
	}
	postData["action"] = "ContainerFullInfo"
	postData["virtual_id"] = instRecord.StartupVirtualId
	postData["startup_mark"] = instRecord.StartupMark
	postData["container_id"] = instRecord.DockerId
	if ginH, err := obj.ActionVirtual(instRecord.StartupVirtualId, postData); err != nil {
		logger.Error(err)
		return containerInfo, err
	} else {
		if ginR, err := Result(ginH); err != nil {
			return containerInfo, err
		} else {
			if ginR.Code == 0 {
				json := utils.GetJsonFromStruct(ginR.Result["container"])
				if json == "" {
					err := errors.New("获取容器信息失败")
					return containerInfo, err
				}
				if err := utils.GetStructFromJson(&containerInfo, json); err != nil {
					logger.Error(err)
					return containerInfo, err
				} else {
					return containerInfo, nil
				}
			}
			err := errors.New(ginR.Msg)
			return containerInfo, err
		}
	}
}

func (obj *node_) ContainerInfo(startupMark string) (structs.ContainerInfo, error) {
	postData := make(map[string]interface{})
	var containerInfo structs.ContainerInfo
	var instRecord model.InstRecord
	if err := instRecord.GetByStartupMark(startupMark); err != nil {
		logger.Error(err, startupMark)
		return containerInfo, err
	}
	postData["action"] = "ContainerInfo"
	postData["virtual_id"] = instRecord.StartupVirtualId
	postData["startup_mark"] = instRecord.StartupMark
	postData["container_id"] = instRecord.DockerId
	if ginH, err := obj.ActionVirtual(instRecord.StartupVirtualId, postData); err != nil {
		logger.Error(err)
		return containerInfo, err
	} else {
		if ginR, err := Result(ginH); err != nil {
			return containerInfo, err
		} else {
			if ginR.Code == 0 {
				json := utils.GetJsonFromStruct(ginR.Result["container_info"])
				if json == "" {
					err := errors.New("获取容器信息失败")
					return containerInfo, err
				}
				if err := utils.GetStructFromJson(&containerInfo, json); err != nil {
					logger.Error(err)
					return containerInfo, err
				} else {
					return containerInfo, nil
				}
			}
			logger.Error(fmt.Sprintf("container info err response: %+v ", ginH))
			err := errors.New(ginR.Msg)
			return containerInfo, err
		}
	}
}

func (obj *node_) SetVirtual(virtualId uint, postData map[string]interface{}) (gin.H, error) {
	var virtual model.Virtual
	if err := virtual.GetById(virtualId); err != nil {
		logger.Error(err, virtualId)
		return nil, err
	}
	postUrl := "api/node/virtual/set"
	//postData := make(map[string]interface{})
	//postData["virtual_id"] = virtualId
	return PostNodeForGin(virtual.NodeId, postUrl, postData)
}

func (obj *node_) GetVirtualDetail(virtualId uint) (gin.H, error) {
	var virtual model.Virtual
	if err := virtual.GetById(virtualId); err != nil {
		logger.Error(err, virtualId)
		return nil, err
	}

	postUrl := "api/node/virtual/detail"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	return PostNodeForGin(virtual.NodeId, postUrl, postData)
}

func (obj *node_) SetVirtualStatus(virtualId uint) (gin.H, error) {
	var virtual model.Virtual
	if err := virtual.GetById(virtualId); err != nil {
		logger.Error(err, virtualId)
		return nil, err
	}

	postUrl := "api/node/virtual/set_status"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["status"] = virtual.Status
	return PostNodeForGin(virtual.NodeId, postUrl, postData)
}

func (obj *node_) RemoveDocker(virtualId uint, startupMark, dockerId string) (gin.H, error) {
	var virtual model.Virtual
	if err := virtual.GetById(virtualId); err != nil {
		logger.Error(err, virtualId)
		return nil, err
	}

	postUrl := "api/node/virtual/remove_docker"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["startup_mark"] = startupMark
	postData["docker_id"] = dockerId
	return PostNodeForGin(virtual.NodeId, postUrl, postData)
}

func (obj *node_) GetVirtualLocalImages(virtualId uint) (gin.H, error) {
	var virtual model.Virtual
	if err := virtual.GetById(virtualId); err != nil {
		logger.Error(err, virtualId)
		return nil, err
	}

	postUrl := "api/node/virtual/local_images"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	return PostNodeForGin(virtual.NodeId, postUrl, postData)
}

func (obj *node_) RemoveVirtualLocalImage(virtualId uint, imageSha256 string) (gin.H, error) {
	var virtual model.Virtual
	if err := virtual.GetById(virtualId); err != nil {
		logger.Error(err, virtualId)
		return nil, err
	}

	postUrl := "api/node/virtual/remove_image"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["image_sha256"] = imageSha256
	return PostNodeForGin(virtual.NodeId, postUrl, postData)
}

func (obj *node_) PruneVirtualLocalImage(virtualId uint, withA bool) (gin.H, error) {
	var virtual model.Virtual
	if err := virtual.GetById(virtualId); err != nil {
		logger.Error(err, virtualId)
		return nil, err
	}

	postUrl := "api/node/virtual/prune_image"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["with_a"] = withA
	return PostNodeForGin(virtual.NodeId, postUrl, postData)
}

func (obj *node_) RefreshVirtualLocalImage(virtualId uint) (gin.H, error) {
	var virtual model.Virtual
	if err := virtual.GetById(virtualId); err != nil {
		logger.Error(err, virtualId)
		return nil, err
	}
	postUrl := "api/node/virtual/refresh_image"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	return PostNodeForGin(virtual.NodeId, postUrl, postData)
}

func (obj *node_) RemoveInstanceNginx(virtualId uint, podId uint, instanceUuid string) (gin.H, error) {
	var virtual model.Virtual
	if err := virtual.GetById(virtualId); err != nil {
		logger.Error(err, virtualId)
		return nil, err
	}
	postUrl := "api/node/nginx/remove_instance"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["pod_id"] = podId
	postData["instance_uuid"] = instanceUuid
	return PostNodeForGin(virtual.NodeId, postUrl, postData)
}

func (obj *node_) ListNginx(nodeId uint) (gin.H, error) {
	postUrl := "api/node/nginx/list"
	postData := make(map[string]interface{})
	return PostNodeForGin(nodeId, postUrl, postData)
}

func (obj *node_) GetNginx(nodeId uint, key string) (gin.H, error) {
	postUrl := "api/node/nginx/get"
	postData := make(map[string]interface{})
	postData["key"] = key
	return PostNodeForGin(nodeId, postUrl, postData)
}

func (obj *node_) GetNodeStatic(nodeId uint) (map[string]interface{}, error) {
	postUrl := "api/node/static"
	postData := make(map[string]interface{})
	mm, _ := PostNode(nodeId, postUrl, postData)
	if ginH, err := Result(mm); err != nil {
		logger.Error(err)
		return nil, err
	} else {
		if ginH.Code == 0 {
			return ginH.Result, nil
		} else {
			logger.Error(ginH)
			return nil, errors.New(ginH.Msg)
		}
	}
}

func (obj *node_) GetDockerDetail(virtualId uint, startupMark string) (docker *Docker, dockerState string, ginH gin.H, err error) {
	dockerState = enums.DockerStatusEnum.Unknow
	var virtual model.Virtual
	if err1 := virtual.GetById(virtualId); err1 != nil {
		logger.Error(err1)
		err = err1
		return
	}

	postUrl := "api/node/docker/detail"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["startup_mark"] = startupMark
	ginH, err = PostNodeForGin(virtual.NodeId, postUrl, postData)
	if err != nil {
		return
	}

	if rGinH, err1 := ResultGinH(ginH); err1 != nil {
		err = err1
		return
	} else {
		if _, ok := rGinH.Result["docker"]; ok {
			tmpJson := utils.GetJsonFromStruct(rGinH.Result["docker"])
			var tmpDocker Docker
			if err1 := utils.GetStructFromJson(&tmpDocker, tmpJson); err1 != nil {
				err = err1
				logger.Error(err)
			} else {
				docker = &tmpDocker
			}
		}
		if _, ok := rGinH.Result["state"]; ok {
			dockerState = rGinH.Result["state"].(string)
		}
		return
	}
}

/*
func (obj *node_) GetDockerDetail(virtualId uint, startupMark string) (*Docker, string, error) {

	dockerState := enums.DockerStatusEnum.Unknow
	var virtual model.Virtual
	if err := virtual.GetById(virtualId); err != nil {
		logger.Error(err)
		return nil, dockerState, err
	}

	postUrl := "api/node/docker/detail"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["startup_mark"] = startupMark
	mm, _ := PostNode(virtual.NodeId, postUrl, postData)
	if ginH, err := Result(mm); err != nil {
		logger.Error(err)
		return nil, dockerState, err
	} else {
		if ginH.Code == 0 {
			if _, ok := ginH.Result["docker"]; ok {
				tmpJson := utils.GetJsonFromStruct(ginH.Result["docker"])
				var tmpDocker Docker
				if err := utils.GetStructFromJson(&tmpDocker, tmpJson); err != nil {
					logger.Error(err)
					return nil, dockerState, err
				} else {
					return &tmpDocker, tmpDocker.State, nil
				}
			} else {
				if _, ok := ginH.Result["state"]; ok {
					return nil, ginH.Result["state"].(string), nil
				}
				return nil, dockerState, nil
			}
		} else {
			//logger.Error(ginH)
			return nil, dockerState, errors.New(ginH.Msg)
		}
	}
}*/

func (obj *node_) SpellRunCommand(nodeId uint, podId uint, imageId uint, needGpus int, userDataPath string) (*GinH, error) {

	//var virtual model.Virtual
	//virtualId := uint(11)
	//if err := virtual.GetById(virtualId); err != nil {
	//	logger.Error(err, virtualId)
	//	return nil, err
	//}

	postUrl := "api/node/docker/spell_run_command"
	postData := make(map[string]interface{})
	postData["pod_id"] = podId
	postData["image_id"] = imageId
	postData["need_gpus"] = needGpus
	postData["user_data_path"] = "usrtst"

	startupParm := make(map[string]interface{})
	startupParm["instance_type"] = 1
	startupParm["usr_data_path"] = "usrtst"
	postData["startup_parm"] = startupParm
	mm, _ := PostNode(nodeId, postUrl, postData)
	if ginH, err := Result(mm); err != nil {
		logger.Error(err)
		return nil, err
	} else {
		return &ginH, nil
	}
}

func (obj *node_) GetStartupLog(nodeId uint, startupMark string, all bool) ([]interface{}, error) {
	postUrl := "api/node/docker/startuplog"
	postData := make(map[string]interface{})
	postData["startup_mark"] = startupMark
	postData["all"] = all
	mm, _ := PostNode(nodeId, postUrl, postData)
	if ginH, err := Result(mm); err != nil {
		logger.Error(err)
		return nil, err
	} else {
		if ginH.Code == 0 {
			if _, ok := ginH.Result["startup_log"]; ok {
				return ginH.Result["startup_log"].([]interface{}), nil
			} else {
				return nil, errors.New("log字段不存在")
			}
		} else {
			logger.Error(ginH)
			return nil, errors.New(ginH.Msg)
		}
	}
	//msg = "获取失败"
	//logger.Error(msg)
	//return nil, errors.New(msg)
}

type Virtual struct {
	ID            uint         `json:"id"`
	Region        int          `json:"region"`
	HostPort      string       `json:"host_port"`
	Host          string       `json:"host"`
	Port          int          `json:"port"`
	SshUser       string       `json:"ssh_user"`
	SshPassword   string       `json:"ssh_password"`
	SshClient     *ssh.Client  `json:"-"`
	SshOpen       bool         `json:"ssh_open"`
	GpuFrees      int          `json:"gpu_frees"`
	Gpus          []GpuItem    `json:"gpus"` //[0,0,1,0,1,0]
	Dockers       []DockerItem `json:"dockers"`
	InitAt        int64        `json:"init_at"` //初始化时间
	GpuModelId    uint         `json:"gpu_model_id"`
	LastCheckTime time.Time    `json:"last_check_time"` //最后检测时间
	DockerAt      int64        `json:"docker_at"`       //启动docker时间戳,0为没有在启动
	TimeoutAt     int64        `json:"timeout_at"`      //链接超时时间点
	Status        int          `json:"status"`
}

type Docker struct {
	ID           string                 `json:"id"`
	InstanceUuid string                 `json:"instance_uuid"`
	StartupMark  string                 `json:"startup_mark"`
	Name         string                 `json:"-"`
	PodId        uint                   `json:"pod_id"`
	PodCategory  int                    `json:"pod_category"`
	PodName      string                 `json:"pod_name"`
	VirtualId    uint                   `json:"virtual_id"`
	VirtualHost  string                 `json:"virtual_host"` //在哪台虚拟机上
	VirtualPort  int                    `json:"virtual_port"`
	SshPort      int                    `json:"ssh_port"` //ssh操作docker的宿主机端口
	Gpus         []int                  `json:"gpus"`     //该docker启用的Gpu序列
	MapPref      string                 `json:"map_pref"`
	MapPorts     []string               `json:"map_ports"`
	CreatedAt    time.Time              `json:"created_at"`
	Heartbeat    time.Time              `json:"heartbeat"` //心跳包
	WebUrl       string                 `json:"web_url"`
	ApiBase      string                 `json:"api_base"`
	Info         map[string]interface{} `json:"-"`
	State        string                 `json:"state"`
}

package service

import (
	"context"
	"cpn-ai/common/logger"
	"cpn-ai/config"
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/prometheus/client_golang/api"
	v1 "github.com/prometheus/client_golang/api/prometheus/v1"
	"github.com/prometheus/common/model"
)

// AlertLevel 告警级别
type AlertLevel string

const (
	AlertLevelNormal   AlertLevel = "normal"
	AlertLevelWarning  AlertLevel = "warning"
	AlertLevelCritical AlertLevel = "critical"
	AlertLevelOffline  AlertLevel = "offline"
)

// GpuMetric GPU指标数据
type GpuMetric struct {
	GpuId         string     `json:"gpu_id" gorm:"-"`
	GpuModel      string     `json:"gpu_model" gorm:"-"`      // GPU型号
	PciBusId      string     `json:"pci_bus_id" gorm:"-"`     // PCI总线ID
	DriverVersion string     `json:"driver_version" gorm:"-"` // 驱动版本
	Uuid          string     `json:"uuid" gorm:"-"`
	Temperature   float64    `json:"temperature" gorm:"-"`
	MemoryUsed    float64    `json:"memory_used" gorm:"-"`  // MB
	MemoryTotal   float64    `json:"memory_total" gorm:"-"` // MB
	MemoryUsage   float64    `json:"memory_usage_percent" gorm:"-"`
	Utilization   float64    `json:"utilization" gorm:"-"`
	PowerUsage    float64    `json:"power_usage" gorm:"-"`
	EccErrors     int        `json:"ecc_errors" gorm:"-"`
	XidErrors     int        `json:"xid_errors" gorm:"-"`
	Status        AlertLevel `json:"status" gorm:"-"`
	StatusDesc    string     `json:"status_description" gorm:"-"`
	LastUpdate    time.Time  `json:"last_update" gorm:"-"`
}

// CpuMetric CPU指标数据（仅监控，不告警）
type CpuMetric struct {
	PhysicalCpuCount int        `json:"physical_cpu_count" gorm:"-"`    // 物理CPU数量
	CpuModel         string     `json:"cpu_model" gorm:"-"`             // CPU型号
	TotalCores       int        `json:"total_cores" gorm:"-"`           // 总核心数
	OverallUsage     float64    `json:"overall_usage_percent" gorm:"-"` // 整体CPU使用率
	LoadAverage1m    float64    `json:"load_average_1m" gorm:"-"`       // 1分钟负载
	LoadAverage5m    float64    `json:"load_average_5m" gorm:"-"`       // 5分钟负载
	LoadAverage15m   float64    `json:"load_average_15m" gorm:"-"`      // 15分钟负载
	Status           AlertLevel `json:"status" gorm:"-"`                // 固定为 normal
	StatusDesc       string     `json:"status_description" gorm:"-"`    // 固定为 "数据收集中"
	LastUpdate       time.Time  `json:"last_update" gorm:"-"`
}

// MemoryMetric 内存指标数据（仅监控，不告警）
type MemoryMetric struct {
	TotalMemory      float64    `json:"total_memory_gb" gorm:"-"`     // 总内存(GB)
	UsedMemory       float64    `json:"used_memory_gb" gorm:"-"`      // 已用内存(GB)
	FreeMemory       float64    `json:"free_memory_gb" gorm:"-"`      // 空闲内存(GB)
	AvailableMemory  float64    `json:"available_memory_gb" gorm:"-"` // 可用内存(GB)
	UsagePercent     float64    `json:"usage_percent" gorm:"-"`       // 使用率
	SwapTotal        float64    `json:"swap_total_gb" gorm:"-"`       // 交换分区总量
	SwapUsed         float64    `json:"swap_used_gb" gorm:"-"`        // 交换分区使用量
	SwapUsagePercent float64    `json:"swap_usage_percent" gorm:"-"`  // 交换分区使用率
	Status           AlertLevel `json:"status" gorm:"-"`              // 固定为 normal
	StatusDesc       string     `json:"status_description" gorm:"-"`  // 固定为 "数据收集中"
	LastUpdate       time.Time  `json:"last_update" gorm:"-"`
}

// NvmeServiceMetric NVMe存储设备监控
type NvmeServiceMetric struct {
	DeviceName    string     `json:"device_name" gorm:"-"`        // 设备名称 (如 /dev/nvme0n1)
	MountPoint    string     `json:"mount_point" gorm:"-"`        // 挂载点 (如 /)
	FileSystem    string     `json:"filesystem" gorm:"-"`         // 文件系统类型 (如 ext4)
	TotalSpace    float64    `json:"total_space_gb" gorm:"-"`     // 总空间(GB)
	UsedSpace     float64    `json:"used_space_gb" gorm:"-"`      // 已用空间(GB)
	AvailSpace    float64    `json:"avail_space_gb" gorm:"-"`     // 可用空间(GB)
	UsagePercent  float64    `json:"usage_percent" gorm:"-"`      // 使用率
	IsReadOnly    bool       `json:"is_readonly" gorm:"-"`        // 是否只读
	LastCheckTime time.Time  `json:"last_check_time" gorm:"-"`    // 最后检查时间
	Status        AlertLevel `json:"status" gorm:"-"`             // normal/warning/critical
	StatusDesc    string     `json:"status_description" gorm:"-"` // 状态描述
	ErrorMessage  string     `json:"error_message" gorm:"-"`      // 错误信息（如果有）
}

// VirtualMonitorInfo 虚拟机监控信息
type VirtualMonitorInfo struct {
	MonitorStatus AlertLevel  `json:"monitor_status" gorm:"-"` // 整体监控状态
	GpuMetrics    []GpuMetric `json:"gpu_metrics" gorm:"-"`    // GPU指标数组

	// 新增其他资源监控
	CpuMetric    *CpuMetric          `json:"cpu_metric" gorm:"-"`    // CPU指标
	MemoryMetric *MemoryMetric       `json:"memory_metric" gorm:"-"` // 内存指标
	NvmeServices []NvmeServiceMetric `json:"nvme_services" gorm:"-"` // NVMe服务数组

	ActiveAlerts  []string  `json:"active_alerts" gorm:"-"`   // 活跃告警描述
	LastCheckTime time.Time `json:"last_check_time" gorm:"-"` // 最后检查时间
	TotalGpus     int       `json:"total_gpus" gorm:"-"`      // GPU总数
	NormalGpus    int       `json:"normal_gpus" gorm:"-"`     // 正常GPU数
	WarningGpus   int       `json:"warning_gpus" gorm:"-"`    // 警告GPU数
	CriticalGpus  int       `json:"critical_gpus" gorm:"-"`   // 异常GPU数
	OfflineGpus   int       `json:"offline_gpus" gorm:"-"`    // 离线GPU数

	// NVMe设备统计
	TotalNvmeDevices    int `json:"total_nvme_devices" gorm:"-"`    // NVMe设备总数
	NormalNvmeDevices   int `json:"normal_nvme_devices" gorm:"-"`   // 正常NVMe设备数
	WarningNvmeDevices  int `json:"warning_nvme_devices" gorm:"-"`  // 警告NVMe设备数
	CriticalNvmeDevices int `json:"critical_nvme_devices" gorm:"-"` // 异常NVMe设备数
}

// XID错误编码含义映射
var XID_ERROR_CODES = map[int]map[string]string{
	8: {
		"en": "Page fault - GPU accessed an invalid memory page",
		"zh": "页面错误 - GPU访问了无效的内存页面",
	},
	13: {
		"en": "Graphics engine exception - Graphics processing unit exception",
		"zh": "图形引擎异常 - 图形处理单元发生异常",
	},
	31: {
		"en": "GPU memory page fault - Video memory access error",
		"zh": "GPU内存页面错误 - 显存访问错误",
	},
	32: {
		"en": "Invalid push buffer - GPU command buffer corrupted",
		"zh": "无效的推送缓冲区 - GPU命令缓冲区损坏",
	},
	43: {
		"en": "GPU has fallen off the bus - Hardware connection lost or GPU failure",
		"zh": "GPU已从总线掉落 - 硬件连接丢失或GPU故障",
	},
	45: {
		"en": "GPU pre-initialization failed - Error during GPU initialization",
		"zh": "GPU预初始化失败 - GPU初始化过程中出错",
	},
	48: {
		"en": "Double bit ECC error - Uncorrectable ECC error in video memory",
		"zh": "双位ECC错误 - 显存发生不可纠正的ECC错误",
	},
	56: {
		"en": "Display engine hang - Display output engine stopped responding",
		"zh": "显示引擎挂起 - 显示输出引擎停止响应",
	},
	61: {
		"en": "Internal microcontroller error - GPU internal controller failure",
		"zh": "内部微控制器错误 - GPU内部控制器故障",
	},
	62: {
		"en": "Internal microcontroller assertion failed - GPU firmware detected error state",
		"zh": "内部微控制器断言失败 - GPU固件检测到错误状态",
	},
	63: {
		"en": "ECC page retirement error - Memory page marked unusable due to excessive errors",
		"zh": "ECC页面退役错误 - 显存页面因错误过多被标记为不可用",
	},
	64: {
		"en": "Multiple ECC errors - Multiple ECC error events detected",
		"zh": "多个ECC错误 - 检测到多个ECC错误事件",
	},
	68: {
		"en": "NVLINK error - High-speed GPU interconnect error",
		"zh": "NVLINK错误 - GPU间高速连接发生错误",
	},
	69: {
		"en": "GPU out of memory - Video memory allocation failed",
		"zh": "GPU内存不足 - 显存分配失败",
	},
	74: {
		"en": "GPU kernel timeout - GPU compute task execution timeout",
		"zh": "GPU内核超时 - GPU计算任务执行超时",
	},
	79: {
		"en": "GPU has fallen off the bus - Similar to XID 43, hardware connection issue",
		"zh": "GPU已从总线掉落 - 类似XID 43，硬件连接问题",
	},
	92: {
		"en": "High priority error - GPU detected critical hardware error",
		"zh": "高优先级错误 - GPU检测到严重硬件错误",
	},
	94: {
		"en": "Accessed page with errors - Accessed memory page known to have errors",
		"zh": "包含错误的页面被访问 - 访问了已知有错误的内存页面",
	},
	95: {
		"en": "Accessed page with errors - Similar to XID 94",
		"zh": "包含错误的页面被访问 - 类似XID 94",
	},
	119: {
		"en": "GPU internal error - GPU internal state exception",
		"zh": "GPU内部错误 - GPU内部状态异常",
	},
	120: {
		"en": "Video memory ECC error - Correctable ECC error",
		"zh": "显存ECC错误 - 可纠正的ECC错误",
	},
	121: {
		"en": "Video memory ECC error - Uncorrectable ECC error",
		"zh": "显存ECC错误 - 不可纠正的ECC错误",
	},
	122: {
		"en": "Video memory ECC error - ECC error counter overflow",
		"zh": "显存ECC错误 - ECC错误计数器溢出",
	},
}

// getXidErrorDescription 获取XID错误编码的描述
func getXidErrorDescription(xidCode int) string {
	if xidCode == 0 {
		return ""
	}

	errorInfo, exists := XID_ERROR_CODES[xidCode]
	if exists {
		return fmt.Sprintf("XID-%d: %s | %s", xidCode, errorInfo["zh"], errorInfo["en"])
	}
	return fmt.Sprintf("未知XID错误(编码:%d) | Unknown XID error (code:%d)", xidCode, xidCode)
}

// roundToTwoDecimal 将浮点数保留两位小数
func roundToTwoDecimal(value float64) float64 {
	return math.Round(value*100) / 100
}

// GpuMonitorService GPU监控服务
type GpuMonitorService struct{}

var GpuMonitor GpuMonitorService

// GetVirtualMonitorInfo 获取虚拟机的完整监控信息
func (s *GpuMonitorService) GetVirtualMonitorInfo(serverIp string) (*VirtualMonitorInfo, error) {
	// 创建 Prometheus API 客户端
	client, err := api.NewClient(api.Config{
		Address: config.PrometheusUrl,
	})
	if err != nil {
		logger.Errorf("创建 Prometheus 客户端失败: %v", err)
		return nil, fmt.Errorf("创建 Prometheus 客户端失败: %w", err)
	}

	v1api := v1.NewAPI(client)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 获取GPU指标数据
	gpuMetrics, err := s.getGpuMetrics(ctx, v1api, serverIp)
	if err != nil {
		logger.Errorf("获取GPU指标失败: %v", err)
		return nil, err
	}

	// 获取CPU指标数据
	cpuMetric, err := s.getCpuMetrics(ctx, v1api, serverIp)
	if err != nil {
		logger.Warnf("获取CPU指标失败: %v", err)
		cpuMetric = nil // 失败时返回nil
	}

	// 获取内存指标数据
	memoryMetric, err := s.getMemoryMetrics(ctx, v1api, serverIp)
	if err != nil {
		logger.Warnf("获取内存指标失败: %v", err)
		memoryMetric = nil // 失败时返回nil
	}

	// 获取NVMe服务监控数据
	nvmeServices, err := s.getNvmeServiceMetrics(ctx, v1api, serverIp)
	if err != nil {
		logger.Warnf("获取NVMe服务指标失败: %v", err)
		nvmeServices = []NvmeServiceMetric{} // 失败时返回空数组
	}

	// 计算监控状态统计
	monitorInfo := &VirtualMonitorInfo{
		GpuMetrics:    gpuMetrics,
		CpuMetric:     cpuMetric,
		MemoryMetric:  memoryMetric,
		NvmeServices:  nvmeServices,
		LastCheckTime: time.Now(),
		TotalGpus:     len(gpuMetrics),
	}

	// 统计各状态GPU数量和生成告警信息
	var alerts []string
	for _, metric := range gpuMetrics {
		switch metric.Status {
		case AlertLevelNormal:
			monitorInfo.NormalGpus++
		case AlertLevelWarning:
			monitorInfo.WarningGpus++
			alerts = append(alerts, fmt.Sprintf("GPU %s: %s", metric.GpuId, metric.StatusDesc))
		case AlertLevelCritical:
			monitorInfo.CriticalGpus++
			alerts = append(alerts, fmt.Sprintf("GPU %s: %s", metric.GpuId, metric.StatusDesc))
		case AlertLevelOffline:
			monitorInfo.OfflineGpus++
			alerts = append(alerts, fmt.Sprintf("GPU %s: 离线", metric.GpuId))
		}
	}

	// 统计NVMe设备状态（只统计正常设备，不产生告警）
	monitorInfo.TotalNvmeDevices = len(nvmeServices)
	monitorInfo.NormalNvmeDevices = len(nvmeServices) // 所有能读取到的设备都认为是正常的
	monitorInfo.WarningNvmeDevices = 0
	monitorInfo.CriticalNvmeDevices = 0

	// 确定整体监控状态（仅考虑GPU和NVMe服务）
	s.determineOverallStatus(monitorInfo)

	monitorInfo.ActiveAlerts = alerts

	return monitorInfo, nil
}

// getGpuMetrics 获取GPU指标数据
func (s *GpuMonitorService) getGpuMetrics(ctx context.Context, v1api v1.API, serverIp string) ([]GpuMetric, error) {
	// 定义需要查询的指标
	queries := map[string]string{
		"temperature": "DCGM_FI_DEV_GPU_TEMP",
		"memory_used": "DCGM_FI_DEV_FB_USED",
		"memory_free": "DCGM_FI_DEV_FB_FREE",
		"utilization": "DCGM_FI_DEV_GPU_UTIL",
		"power_usage": "DCGM_FI_DEV_POWER_USAGE",
		"xid_errors":  "DCGM_FI_DEV_XID_ERRORS",
	}

	// 收集所有指标数据
	allMetrics := make(map[string]map[string]float64)
	gpuModels := make(map[string]string)      // 存储GPU型号信息
	pCIBusIds := make(map[string]string)      // 存储PCI总线ID信息
	driverVersions := make(map[string]string) // 存储驱动版本信息
	uuids := make(map[string]string)          // 存储UUID信息
	//{
	//	DCGM_FI_DRIVER_VERSION="550.90.07",
	//		Hostname="8c0d13375ea1",
	//	UUID="GPU-0b0fb7a7-7296-a616-a225-0e2993776141",
	//	__name__="DCGM_FI_DEV_DEC_UTIL",
	//	device="nvidia0",
	//	gpu="0",
	//	instance="*************:9400",
	//	job="gpu-servers",
	//	modelName="NVIDIA GeForce RTX 4080 SUPER",
	//	pci_bus_id="00000000:1B:00.0",
	//	server="*************"
	//}

	for metricName, query := range queries {
		fullQuery := fmt.Sprintf(`%s{server="%s"}`, query, serverIp)

		result, _, err := v1api.Query(ctx, fullQuery, time.Now())
		if err != nil {
			logger.Warnf("查询指标 %s 失败: %v", metricName, err)
			continue
		}

		vector, ok := result.(model.Vector)
		if !ok {
			logger.Warnf("指标 %s 返回了非 Vector 类型数据", metricName)
			continue
		}

		for _, sample := range vector {
			gpuId := string(sample.Metric["gpu"])
			if gpuId == "" {
				continue
			}

			if allMetrics[gpuId] == nil {
				allMetrics[gpuId] = make(map[string]float64)
			}
			allMetrics[gpuId][metricName] = float64(sample.Value)

			// 尝试从指标标签中获取GPU型号信息
			if gpuModels[gpuId] == "" {
				// 检查是否有model_name标签
				if modelName := string(sample.Metric["modelName"]); modelName != "" {
					gpuModels[gpuId] = modelName
				}
			}
			// 尝试获取PCI总线ID
			if pCIBusIds[gpuId] == "" {
				pCIBusIds[gpuId] = string(sample.Metric["pci_bus_id"])
			}
			// 尝试获取驱动版本
			if driverVersions[gpuId] == "" {
				driverVersions[gpuId] = string(sample.Metric["DCGM_FI_DRIVER_VERSION"])
			}
			// 尝试获取UUID
			if uuids[gpuId] == "" {
				uuids[gpuId] = string(sample.Metric["UUID"])
			}
		}
	}

	// 转换为GpuMetric结构
	var gpuMetrics []GpuMetric
	for gpuId, metrics := range allMetrics {
		// 获取GPU型号，如果没有则使用默认值
		gpuModel := gpuModels[gpuId]
		if gpuModel == "" {
			gpuModel = "未知GPU型号"
		}
		pCIBusId := pCIBusIds[gpuId]
		// 获取PCI总线ID，如果没有则使用默认值
		if pCIBusIds[gpuId] == "" {
			pCIBusId = "未知PCI总线ID"
		}
		driverVersion := driverVersions[gpuId]
		// 获取驱动版本，如果没有则使用默认值
		if driverVersions[gpuId] == "" {
			driverVersion = "未知驱动版本"
		}
		uuid := uuids[gpuId]
		// 获取UUID，如果没有则使用默认值
		if uuids[gpuId] == "" {
			uuid = "未知UUID"
		}

		metric := GpuMetric{
			GpuId:         gpuId,
			GpuModel:      gpuModel,
			PciBusId:      pCIBusId,
			DriverVersion: driverVersion,
			Uuid:          uuid,
			Temperature:   roundToTwoDecimal(metrics["temperature"]),
			MemoryUsed:    roundToTwoDecimal(metrics["memory_used"]),
			MemoryTotal:   roundToTwoDecimal(metrics["memory_used"] + metrics["memory_free"]),
			Utilization:   roundToTwoDecimal(metrics["utilization"]),
			PowerUsage:    roundToTwoDecimal(metrics["power_usage"]),
			XidErrors:     int(metrics["xid_errors"]),
			LastUpdate:    time.Now(),
		}

		// 计算显存使用率
		if metric.MemoryTotal > 0 {
			metric.MemoryUsage = roundToTwoDecimal((metric.MemoryUsed / metric.MemoryTotal) * 100)
		}

		// 判断GPU状态和生成状态描述
		metric.Status, metric.StatusDesc = s.determineGpuStatus(metric)

		gpuMetrics = append(gpuMetrics, metric)
	}

	return gpuMetrics, nil
}

// containsEmptyModels 检查是否有空的GPU型号
func containsEmptyModels(gpuModels map[string]string) bool {
	for _, model := range gpuModels {
		if model == "" {
			return true
		}
	}
	return false
}

// getCpuMetrics 获取CPU指标数据
func (s *GpuMonitorService) getCpuMetrics(ctx context.Context, v1api v1.API, serverIp string) (*CpuMetric, error) {
	// 定义需要查询的CPU指标
	queries := map[string]string{
		"overall_cpu_usage": fmt.Sprintf(`100 - (avg(rate(node_cpu_seconds_total{mode="idle",instance="%s:9100"}[5m])) * 100)`, serverIp),
		"load_average_1m":   fmt.Sprintf(`node_load1{instance="%s:9100"}`, serverIp),
		"load_average_5m":   fmt.Sprintf(`node_load5{instance="%s:9100"}`, serverIp),
		"load_average_15m":  fmt.Sprintf(`node_load15{instance="%s:9100"}`, serverIp),
	}

	// 收集所有指标数据
	metrics := make(map[string]float64)
	var cpuModel string = "未知CPU型号"
	var physicalCpuCount int = 1 // 默认1个物理CPU
	var totalCores int

	for metricName, fullQuery := range queries {
		result, _, err := v1api.Query(ctx, fullQuery, time.Now())
		if err != nil {
			logger.Warnf("查询CPU指标 %s 失败: %v", metricName, err)
			continue
		}

		vector, ok := result.(model.Vector)
		if !ok {
			logger.Warnf("CPU指标 %s 返回了非 Vector 类型数据", metricName)
			continue
		}

		// 处理指标数据
		if len(vector) > 0 {
			// 保留两位小数
			metrics[metricName] = roundToTwoDecimal(float64(vector[0].Value))
		}
	}

	// 获取CPU核心数
	coreQuery := fmt.Sprintf(`count(node_cpu_seconds_total{mode="idle",instance="%s:9100"}) by (instance)`, serverIp)
	result, _, err := v1api.Query(ctx, coreQuery, time.Now())
	if err == nil {
		if vector, ok := result.(model.Vector); ok && len(vector) > 0 {
			totalCores = int(vector[0].Value)
		}
	}

	// 尝试获取CPU型号信息（通过node_uname_info指标）
	unameQuery := fmt.Sprintf(`node_uname_info{instance="%s:9100"}`, serverIp)
	result, _, err = v1api.Query(ctx, unameQuery, time.Now())
	if err == nil {
		if vector, ok := result.(model.Vector); ok && len(vector) > 0 {
			// node_uname_info包含系统信息，但不包含CPU型号
			// 我们可以从machine字段获取架构信息
			machine := string(vector[0].Metric["machine"])
			if machine != "" {
				cpuModel = fmt.Sprintf("%s架构处理器", machine)
			}
		}
	}

	// 尝试通过node_hwmon_chip_names获取更多硬件信息
	hwmonQuery := fmt.Sprintf(`node_hwmon_chip_names{instance="%s:9100"}`, serverIp)
	result, _, err = v1api.Query(ctx, hwmonQuery, time.Now())
	if err == nil {
		if vector, ok := result.(model.Vector); ok {
			for _, sample := range vector {
				chipName := string(sample.Metric["chip"])
				if strings.Contains(strings.ToLower(chipName), "cpu") ||
					strings.Contains(strings.ToLower(chipName), "core") {
					cpuModel = fmt.Sprintf("CPU芯片: %s", chipName)
					break
				}
			}
		}
	}

	// 估算物理CPU数量（基于核心数的经验值）
	if totalCores > 0 {
		// 常见的CPU核心数配置：
		// 单路服务器：8-32核心
		// 双路服务器：32-128核心
		// 四路服务器：128+核心
		if totalCores <= 32 {
			physicalCpuCount = 1
		} else if totalCores <= 128 {
			physicalCpuCount = 2
		} else {
			physicalCpuCount = (totalCores + 63) / 64 // 假设每个CPU最多64核心
		}
	}

	cpuMetric := &CpuMetric{
		PhysicalCpuCount: physicalCpuCount,
		CpuModel:         cpuModel,
		TotalCores:       totalCores,
		OverallUsage:     roundToTwoDecimal(metrics["overall_cpu_usage"]),
		LoadAverage1m:    roundToTwoDecimal(metrics["load_average_1m"]),
		LoadAverage5m:    roundToTwoDecimal(metrics["load_average_5m"]),
		LoadAverage15m:   roundToTwoDecimal(metrics["load_average_15m"]),
		Status:           AlertLevelNormal,
		StatusDesc:       "数据收集中",
		LastUpdate:       time.Now(),
	}

	return cpuMetric, nil
}

// getMemoryMetrics 获取内存指标数据
func (s *GpuMonitorService) getMemoryMetrics(ctx context.Context, v1api v1.API, serverIp string) (*MemoryMetric, error) {
	// 定义需要查询的内存指标
	queries := map[string]string{
		"memory_total":     fmt.Sprintf(`node_memory_MemTotal_bytes{instance="%s:9100"}`, serverIp),
		"memory_available": fmt.Sprintf(`node_memory_MemAvailable_bytes{instance="%s:9100"}`, serverIp),
		"memory_free":      fmt.Sprintf(`node_memory_MemFree_bytes{instance="%s:9100"}`, serverIp),
		"swap_total":       fmt.Sprintf(`node_memory_SwapTotal_bytes{instance="%s:9100"}`, serverIp),
		"swap_free":        fmt.Sprintf(`node_memory_SwapFree_bytes{instance="%s:9100"}`, serverIp),
	}

	// 收集所有指标数据
	metrics := make(map[string]float64)

	for metricName, fullQuery := range queries {
		result, _, err := v1api.Query(ctx, fullQuery, time.Now())
		if err != nil {
			logger.Warnf("查询内存指标 %s 失败: %v", metricName, err)
			continue
		}

		vector, ok := result.(model.Vector)
		if !ok {
			logger.Warnf("内存指标 %s 返回了非 Vector 类型数据", metricName)
			continue
		}

		if len(vector) > 0 {
			metrics[metricName] = float64(vector[0].Value)
		}
	}

	// 转换单位从字节到GB
	const bytesToGB = 1024 * 1024 * 1024

	totalMemory := roundToTwoDecimal(metrics["memory_total"] / bytesToGB)
	availableMemory := roundToTwoDecimal(metrics["memory_available"] / bytesToGB)
	freeMemory := roundToTwoDecimal(metrics["memory_free"] / bytesToGB)
	swapTotal := roundToTwoDecimal(metrics["swap_total"] / bytesToGB)
	swapFree := roundToTwoDecimal(metrics["swap_free"] / bytesToGB)

	usedMemory := roundToTwoDecimal(totalMemory - availableMemory)
	swapUsed := roundToTwoDecimal(swapTotal - swapFree)

	var usagePercent, swapUsagePercent float64
	if totalMemory > 0 {
		usagePercent = roundToTwoDecimal((usedMemory / totalMemory) * 100)
	}
	if swapTotal > 0 {
		swapUsagePercent = roundToTwoDecimal((swapUsed / swapTotal) * 100)
	}

	memoryMetric := &MemoryMetric{
		TotalMemory:      totalMemory,
		UsedMemory:       usedMemory,
		FreeMemory:       freeMemory,
		AvailableMemory:  availableMemory,
		UsagePercent:     usagePercent,
		SwapTotal:        swapTotal,
		SwapUsed:         swapUsed,
		SwapUsagePercent: swapUsagePercent,
		Status:           AlertLevelNormal,
		StatusDesc:       "数据收集中",
		LastUpdate:       time.Now(),
	}

	return memoryMetric, nil
}

// getNvmeServiceMetrics 获取NVMe存储设备监控数据
func (s *GpuMonitorService) getNvmeServiceMetrics(ctx context.Context, v1api v1.API, serverIp string) ([]NvmeServiceMetric, error) {
	// 定义需要查询的文件系统指标
	queries := map[string]string{
		"filesystem_size":     fmt.Sprintf(`node_filesystem_size_bytes{instance="%s:9100",fstype!~"tmpfs|devtmpfs|overlay|squashfs|proc|sysfs|devpts"}`, serverIp),
		"filesystem_avail":    fmt.Sprintf(`node_filesystem_avail_bytes{instance="%s:9100",fstype!~"tmpfs|devtmpfs|overlay|squashfs|proc|sysfs|devpts"}`, serverIp),
		"filesystem_free":     fmt.Sprintf(`node_filesystem_free_bytes{instance="%s:9100",fstype!~"tmpfs|devtmpfs|overlay|squashfs|proc|sysfs|devpts"}`, serverIp),
		"filesystem_readonly": fmt.Sprintf(`node_filesystem_readonly{instance="%s:9100",fstype!~"tmpfs|devtmpfs|overlay|squashfs|proc|sysfs|devpts"}`, serverIp),
	}

	// 收集所有指标数据
	allMetrics := make(map[string]map[string]interface{})

	for metricName, fullQuery := range queries {
		result, _, err := v1api.Query(ctx, fullQuery, time.Now())
		if err != nil {
			logger.Warnf("查询文件系统指标 %s 失败: %v", metricName, err)
			continue
		}

		vector, ok := result.(model.Vector)
		if !ok {
			logger.Warnf("文件系统指标 %s 返回了非 Vector 类型数据", metricName)
			continue
		}

		for _, sample := range vector {
			device := string(sample.Metric["device"])
			mountpoint := string(sample.Metric["mountpoint"])
			fstype := string(sample.Metric["fstype"])
			// logger.Info("获取到文件系统指标: ", device, mountpoint, " "+fstype, " "+metricName, sample.Value)

			// 只监控 NVMe 设备 (设备名包含 nvme)
			if !strings.Contains(fstype, "xfs") && !strings.Contains(fstype, "nfs") {
				continue
			}

			deviceKey := fmt.Sprintf("%s@%s", device, mountpoint)

			if allMetrics[deviceKey] == nil {
				allMetrics[deviceKey] = make(map[string]interface{})
				allMetrics[deviceKey]["device"] = device
				allMetrics[deviceKey]["mountpoint"] = mountpoint
				allMetrics[deviceKey]["fstype"] = fstype
			}
			allMetrics[deviceKey][metricName] = float64(sample.Value)
		}
	}

	// 转换为NvmeServiceMetric结构
	var nvmeDevices []NvmeServiceMetric
	const bytesToGB = 1024 * 1024 * 1024

	for _, metrics := range allMetrics {
		device := metrics["device"].(string)
		mountpoint := metrics["mountpoint"].(string)
		fstype := metrics["fstype"].(string)

		totalSize := roundToTwoDecimal(getFloat64Value(metrics, "filesystem_size") / bytesToGB)
		availSize := roundToTwoDecimal(getFloat64Value(metrics, "filesystem_avail") / bytesToGB)
		freeSize := roundToTwoDecimal(getFloat64Value(metrics, "filesystem_free") / bytesToGB)
		isReadOnly := getFloat64Value(metrics, "filesystem_readonly") == 1

		usedSize := roundToTwoDecimal(totalSize - freeSize)
		var usagePercent float64
		if totalSize > 0 {
			usagePercent = roundToTwoDecimal((usedSize / totalSize) * 100)
		}

		status, statusDesc := s.determineNvmeDeviceStatus(usagePercent, isReadOnly)

		nvmeDevice := NvmeServiceMetric{
			DeviceName:    device,
			MountPoint:    mountpoint,
			FileSystem:    fstype,
			TotalSpace:    totalSize,
			UsedSpace:     usedSize,
			AvailSpace:    availSize,
			UsagePercent:  usagePercent,
			IsReadOnly:    isReadOnly,
			LastCheckTime: time.Now(),
			Status:        status,
			StatusDesc:    statusDesc,
			ErrorMessage:  "",
		}

		nvmeDevices = append(nvmeDevices, nvmeDevice)
	}

	// 如果没有找到 NVMe 设备，返回空数组
	if len(nvmeDevices) == 0 {
		logger.Warnf("未找到 NVMe 设备")
	}

	return nvmeDevices, nil
}

// getFloat64Value 安全获取 float64 值
func getFloat64Value(metrics map[string]interface{}, key string) float64 {
	if val, exists := metrics[key]; exists {
		if floatVal, ok := val.(float64); ok {
			return floatVal
		}
	}
	return 0
}

// determineNvmeDeviceStatus 判断NVMe设备状态
func (s *GpuMonitorService) determineNvmeDeviceStatus(usagePercent float64, isReadOnly bool) (AlertLevel, string) {
	// 只要能读取到数据就认为是正常的
	return AlertLevelNormal, fmt.Sprintf("设备可正常访问(使用率%.2f%%)", usagePercent)
}

// determineOverallStatus 确定整体监控状态
func (s *GpuMonitorService) determineOverallStatus(monitorInfo *VirtualMonitorInfo) {
	// GPU告警优先级最高
	if monitorInfo.CriticalGpus > 0 || monitorInfo.OfflineGpus > 0 {
		monitorInfo.MonitorStatus = AlertLevelCritical
		return
	}

	// GPU警告状态
	if monitorInfo.WarningGpus > 0 {
		monitorInfo.MonitorStatus = AlertLevelWarning
		return
	}

	// 所有服务正常（NVMe设备不参与整体状态判断）
	monitorInfo.MonitorStatus = AlertLevelNormal
}

// determineGpuStatus 判断GPU状态
func (s *GpuMonitorService) determineGpuStatus(metric GpuMetric) (AlertLevel, string) {
	var descriptions []string

	// 检查严重错误
	if metric.Temperature > 85 {
		descriptions = append(descriptions, fmt.Sprintf("温度过高(%.2f°C)", metric.Temperature))
	}
	if metric.XidErrors > 0 {
		xidDesc := getXidErrorDescription(metric.XidErrors)
		if xidDesc != "" {
			descriptions = append(descriptions, fmt.Sprintf("XID错误: %s", xidDesc))
		} else {
			descriptions = append(descriptions, fmt.Sprintf("XID错误(编码:%d)", metric.XidErrors))
		}
	}
	if metric.EccErrors > 0 {
		descriptions = append(descriptions, fmt.Sprintf("ECC错误(%d次)", metric.EccErrors))
	}

	// 如果有严重错误，返回Critical状态
	if len(descriptions) > 0 {
		return AlertLevelCritical, strings.Join(descriptions, "; ")
	}

	// 检查警告条件
	if metric.Temperature > 80 {
		descriptions = append(descriptions, fmt.Sprintf("温度偏高(%.2f°C)", metric.Temperature))
	}

	// 如果有警告条件，返回Warning状态
	if len(descriptions) > 0 {
		return AlertLevelWarning, strings.Join(descriptions, "; ")
	}

	// 正常状态
	return AlertLevelNormal, "运行正常"
}

// GetBatchVirtualMonitorInfo 批量获取虚拟机监控信息
func (s *GpuMonitorService) GetBatchVirtualMonitorInfo(serverIps []string) map[string]*VirtualMonitorInfo {
	result := make(map[string]*VirtualMonitorInfo)

	// 并发获取监控信息
	type monitorResult struct {
		serverIp string
		info     *VirtualMonitorInfo
		err      error
	}

	resultChan := make(chan monitorResult, len(serverIps))

	for _, serverIp := range serverIps {
		go func(ip string) {
			info, err := s.GetVirtualMonitorInfo(ip)
			resultChan <- monitorResult{
				serverIp: ip,
				info:     info,
				err:      err,
			}
		}(serverIp)
	}

	// 收集结果
	for i := 0; i < len(serverIps); i++ {
		res := <-resultChan
		if res.err != nil {
			logger.Warnf("获取服务器 %s 监控信息失败: %v", res.serverIp, res.err)
			// 创建一个离线状态的监控信息
			result[res.serverIp] = &VirtualMonitorInfo{
				MonitorStatus:       AlertLevelOffline,
				GpuMetrics:          []GpuMetric{},
				CpuMetric:           nil,
				MemoryMetric:        nil,
				NvmeServices:        []NvmeServiceMetric{},
				ActiveAlerts:        []string{"监控数据获取失败"},
				LastCheckTime:       time.Now(),
				TotalGpus:           0,
				OfflineGpus:         1, // 标记为离线
				TotalNvmeDevices:    0,
				NormalNvmeDevices:   0,
				WarningNvmeDevices:  0,
				CriticalNvmeDevices: 0,
			}
		} else {
			result[res.serverIp] = res.info
		}
	}

	return result
}

package router

import (
	"cpn-ai/controller/api"
	"cpn-ai/middleware"
	"github.com/gin-gonic/gin"
)

func SetOutApiRouter(router *gin.Engine) {
	router.Use(middleware.CORS())
	outV1Router := router.Group("/api/v1")
	outV1Router.Use(middleware.TokenAuth(), middleware.ApiAuth())
	{
		outV1Router.POST("/finances/balance", api.FinancesApi.GetBalance)   // 余额
		outV1Router.POST("/finances/recharge", api.FinancesApi.GetRecharge) // 充值记录
		outV1Router.POST("/finances/bill", api.FinancesApi.GetBill)         // 账单
		outV1Router.POST("/app/list", api.AppApi.GetPods)
		outV1Router.POST("/app/instance/create", api.AppApi.CreateInstance)
		outV1Router.POST("/app/instance/stop", api.AppApi.StopInstance)
		outV1Router.POST("/app/instance/start", api.AppApi.StartInstance)
		outV1Router.POST("/app/instance/restart", api.AppApi.RestartInstance)
		outV1Router.POST("/app/instance/status", api.AppApi.GetInstanceStatus)
		outV1Router.POST("/app/instance/scheduled/shutdown", api.AppApi.SetShutdownRegularTime)
		outV1Router.POST("/app/instance/list", api.AppApi.GetInstances)
		outV1Router.POST("/gpu/models", api.AppApi.GetResources)
	}
}

package redisqueue

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"
)

const (
	RedisQueueListInputPrefix  = "redisqueue:list:input:"
	RedisQueueListOutputPrefix = "redisqueue:list:output:"
	RedisQueueZsetPrefix       = "redisqueue:zset:"
	RedisQueueHsetPrefix       = "redisqueue:hset:"
)

const (
	RankStatusNoData  = "nodata"
	RankStatusWaiting = "waiting"
	RankStatusRunning = "running"
	RankStatusSuc     = "suc"
	RankStatusFail    = "fail"
	RankStatusErr     = "err"
)

var LastPopAt sync.Map

type Task struct {
	QTaskId          string  `json:"q_task_id"`
	QTaskName        string  `json:"q_task_name"`
	QTaskAbortAt     int64   `json:"q_task_abort_at"`
	QTaskPushAt      int64   `json:"q_task_push_at"`
	QTaskUpdatedAt   int64   `json:"q_task_updated_at"`
	QTaskStartAt     int64   `json:"q_task_start_at"`
	QTaskCompletedAt int64   `json:"q_task_completed_at"`
	QTaskProgress    float64 `json:"q_task_progress"`
	QTaskInput       any     `json:"q_task_input"`
	QTaskOutput      any     `json:"q_task_output"`
	QTaskErr         string  `json:"q_task_err"` //不空说明执行失败
	QTaskWarn        string  `json:"q_task_warn"`
}

// TaskOutputLogicFunc 是业务逻辑的函数类型
type TaskOutputLogicFunc func(task Task) error

type GetQueueNameFunc func(v string) (string, string, string, string)

func NewTask(qTaskInput any) Task {
	qTaskId := utils.GetUUID()
	if qTaskInput == nil {
		qTaskInput = make(map[string]any, 0)
	}
	qTaskOutput := make(map[string]any, 0)
	task := Task{
		QTaskId:        qTaskId,
		QTaskPushAt:    time.Now().UnixMilli(),
		QTaskUpdatedAt: time.Now().UnixMilli(),
		QTaskProgress:  -1,
		QTaskInput:     qTaskInput,
		QTaskOutput:    qTaskOutput,
	}
	return task
}

func Push(queueName string, getQueueNameFunc GetQueueNameFunc, taskJson string) (string, error) {
	if getQueueNameFunc == nil {
		return "", errors.New("为设置队列名称获取函数")
	}
	inputQueueName, _, zsetQueueName, _ := getQueueNameFunc(queueName)
	if inputQueueName == "" || zsetQueueName == "" {
		return "", errors.New("队列名称为空")
	}
	m := utils.GetMapFromJson(taskJson)
	qTaskId := ""
	if v, ok := m["q_task_id"]; ok {
		qTaskId = v.(string)
	}
	if qTaskId == "" {
		return qTaskId, errors.New("q_task_id field is not exist")
	}
	m["q_task_push_at"] = time.Now().UnixMilli()
	m["q_task_updated_at"] = time.Now().UnixMilli()
	m["q_task_start_at"] = 0
	m["q_task_completed_at"] = 0
	m["q_task_progress"] = -1 // -1排队中  >=0<100执行中 >=100执行完成

	taskJson = utils.GetJsonFromStruct(m)

	listKey := RedisQueueListInputPrefix + inputQueueName
	_, err := common.RedisLPush(listKey, qTaskId)
	if err != nil {
		return qTaskId, err
	} else {
		//hsetKey := RedisQueueHsetPrefix + queueName
		//if _, err := common.RedisHSet(hsetKey, qTaskId, taskJson); err != nil {
		//	return qTaskId, err
		//}

		zsetKey := RedisQueueZsetPrefix + zsetQueueName
		if _, err := common.RedisZAdd(zsetKey, qTaskId, float64(time.Now().UnixMilli())); err != nil {
			return qTaskId, err
		}
		return qTaskId, nil
	}
}

func PushTask(queueName string, getQueueNameFunc GetQueueNameFunc, task Task) (int64, error) {

	if getQueueNameFunc == nil {
		return 0, errors.New("为设置队列名称获取函数")
	}
	inputQueueName, _, zsetQueueName, _ := getQueueNameFunc(queueName)
	if inputQueueName == "" || zsetQueueName == "" {
		return 0, errors.New("队列名称为空")
	}

	qTaskId := task.QTaskId
	if qTaskId == "" {
		return 0, errors.New("qTaskId is empty")
	}
	//task := Task{
	//	QTaskId:        qTaskId,
	//	QTaskPushAt:    time.Now().UnixMilli(),
	//	QTaskUpdatedAt: time.Now().UnixMilli(),
	//	QTaskProgress:  -1,
	//	QTaskInput:     qTaskInput,
	//}

	taskJson := utils.GetJsonFromStruct(task)
	listKey := RedisQueueListInputPrefix + inputQueueName
	count, err := common.RedisLPush(listKey, taskJson)
	if err != nil {
		return 0, err
	} else {
		zsetKey := RedisQueueZsetPrefix + zsetQueueName
		if _, err := common.RedisZAdd(zsetKey, qTaskId, float64(time.Now().UnixMilli())); err != nil {
			return count, err
		}
		return count, nil
	}
}

/*
func Pop(queueName string) (string, error) {
	listKey := RedisQueueListPrefix + queueName
	if taskJson, err := common.RedisBRPop(listKey); err != nil {
		return "", err
	} else {
		m := utils.GetMapFromJson(taskJson)
		qTaskId := ""
		if v, ok := m["q_task_id"]; ok {
			qTaskId = v.(string)
		}
		if qTaskId == "" {
			return "", errors.New("q_task_id is empty string")
		}

		hsetKey := RedisQueueHsetPrefix + queueName
		if taskJson, err := common.RedisHGet(hsetKey, qTaskId); err != nil {
			return taskJson, err
		} else {
			if taskJson == "" {
				return "", errors.New("task is empty")
			}
			if err := Progress(queueName, qTaskId, 0); err != nil {

			} else {
				RemoveRank(queueName, qTaskId)
			}
			return taskJson, nil
		}
	}
}*/

/*
func PopTask(inputQueueName string) (Task, error) {
	var task Task
	listKey := RedisQueueListInputPrefix + inputQueueName
	if taskJson, err := common.RedisBRPop(listKey); err != nil {
		return task, err
	} else {
		if taskJson == "" {
			return task, errors.New("taskJson is empty string")
		}
		if err := utils.GetStructFromJson(&task, taskJson); err != nil {
			return task, err
		} else {
			LastPopAt.Store(queueName, task.QTaskPushAt)
		}
		hsetKey := RedisQueueHsetPrefix + queueName
		task.QTaskProgress = 0
		if _, err := common.RedisHSet(hsetKey, task.QTaskId, taskJson); err != nil {
			return task, err
		}
		RemoveRank(queueName, task.QTaskId)
		return task, nil
	}
}*/

func PushTaskOutput(queueName string, getQueueNameFunc GetQueueNameFunc, task Task) (int64, error) {

	if getQueueNameFunc == nil {
		return 0, errors.New("为设置队列名称获取函数")
	}
	_, outputQueueName, _, _ := getQueueNameFunc(queueName)
	if outputQueueName == "" {
		return 0, errors.New("队列名称为空")
	}

	qTaskId := task.QTaskId
	if qTaskId == "" {
		return 0, errors.New("qTaskId is empty")
	}

	taskJson := utils.GetJsonFromStruct(task)
	listKey := RedisQueueListOutputPrefix + outputQueueName
	count, err := common.RedisLPush(listKey, taskJson)
	if err != nil {
		return 0, err
	} else {
		return count, nil
	}
}

func PopTaskOutput(queueName string, getQueueNameFunc GetQueueNameFunc, logicFunc TaskOutputLogicFunc) (Task, error) {
	var task Task
	if getQueueNameFunc == nil {
		return task, errors.New("为设置队列名称获取函数")
	}
	_, outputQueueName, _, _ := getQueueNameFunc(queueName)
	if outputQueueName == "" {
		return task, errors.New("队列名称为空")
	}

	listKey := RedisQueueListOutputPrefix + outputQueueName
	if taskJson, err := common.RedisBRPop(listKey); err != nil {
		return task, err
	} else {
		if taskJson == "" {
			return task, errors.New("taskJson is empty string")
		}
		if err := utils.GetStructFromJson(&task, taskJson); err != nil {
			return task, err
		} else {
			//执行业务逻辑
			if logicFunc != nil {
				if err := logicFunc(task); err != nil {
					logger.Error(err, outputQueueName, task.QTaskId)
				}
			}
		}
		return task, nil
	}
}

func AbortTask(queueName string, getQueueNameFunc GetQueueNameFunc, qTaskId string) (Task, error) {
	var task Task
	if getQueueNameFunc == nil {
		return task, errors.New("为设置队列名称获取函数")
	}
	_, _, _, hsetQueueName := getQueueNameFunc(queueName)
	if hsetQueueName == "" {
		return task, errors.New("队列名称为空")
	}

	hsetKey := RedisQueueHsetPrefix + hsetQueueName
	if taskJson, err := common.RedisHGet(hsetKey, qTaskId); err != nil {
		return task, err
	} else {
		if taskJson == "" {
			return task, errors.New("task is empty")
		}
		if err := utils.GetStructFromJson(&task, taskJson); err != nil {
			return task, err
		}
		task.QTaskAbortAt = time.Now().UnixMilli()
		taskJson = utils.GetJsonFromStruct(task)
		if _, err := common.RedisHSet(hsetKey, task.QTaskId, taskJson); err != nil {
			return task, err
		} else {
			return task, nil
		}
	}
}

func GetTaskOutput(queueName string, getQueueNameFunc GetQueueNameFunc, qTaskId string) (Task, error) {
	var task Task
	if getQueueNameFunc == nil {
		return task, errors.New("为设置队列名称获取函数")
	}
	_, _, _, hsetQueueName := getQueueNameFunc(queueName)
	if hsetQueueName == "" {
		return task, errors.New("队列名称为空")
	}

	hsetKey := RedisQueueHsetPrefix + hsetQueueName
	if taskJson, err := common.RedisHGet(hsetKey, qTaskId); err != nil {
		return task, err
	} else {
		if taskJson == "" {
			return task, errors.New("task is empty")
		}
		if err := utils.GetStructFromJson(&task, taskJson); err != nil {
			return task, err
		}
		return task, nil
	}
}

/*
func Progress(hsetQueueName string, qTaskId string, progress float64) error { //设置任务进度 progress为0时设置自动设置开始时间
	hsetKey := RedisQueueHsetPrefix + hsetQueueName
	if taskJson, err := common.RedisHGet(hsetKey, qTaskId); err != nil {
		return err
	} else {
		if taskJson == "" {
			return errors.New("task is empty")
		}
		var task Task
		if err := utils.GetStructFromJson(&task, taskJson); err != nil {
			return err
		} else {
			task.QTaskProgress = progress
			task.QTaskUpdatedAt = time.Now().UnixMilli()
			if progress == 0 {
				task.QTaskStartAt = time.Now().UnixMilli()
			}
			taskJson = utils.GetJsonFromStruct(task)
			if _, err := common.RedisHSet(hsetKey, qTaskId, taskJson); err != nil {
				return err
			} else {
				return nil
			}
		}
	}
}

func Completed(hsetQueueName string, qTaskId string, qTaskOutput any) error { //设置任务进度 progress为0时设置自动设置开始时间 progress为100时自动设置完成时间
	hsetKey := RedisQueueHsetPrefix + hsetQueueName
	if taskJson, err := common.RedisHGet(hsetKey, qTaskId); err != nil {
		return err
	} else {
		if taskJson == "" {
			return errors.New("task is empty")
		}
		var task Task
		if err := utils.GetStructFromJson(&task, taskJson); err != nil {
			return err
		} else {
			task.QTaskOutput = qTaskOutput
			task.QTaskCompletedAt = time.Now().UnixMilli()
			task.QTaskUpdatedAt = time.Now().UnixMilli()
			taskJson = utils.GetJsonFromStruct(task)
			if _, err := common.RedisHSet(hsetKey, qTaskId, taskJson); err != nil {
				return err
			} else {
				return nil
			}
		}
	}
}*/

func RemoveRank(queueName string, getQueueNameFunc GetQueueNameFunc, qTaskId string) (int64, error) { //从排序队列中移除 qTaskId不存在 返回0,nil qTaskId存在并且移除成功返回1,nil

	if getQueueNameFunc == nil {
		return 0, errors.New("为设置队列名称获取函数")
	}
	_, _, zsetQueueName, _ := getQueueNameFunc(queueName)
	if zsetQueueName == "" {
		return 0, errors.New("队列名称为空")
	}
	zsetKey := RedisQueueZsetPrefix + zsetQueueName
	if exists, err := common.RedisZRem(zsetKey, qTaskId); err != nil {
		return exists, err
	} else {
		return exists, err
	}
}

func RemoveHash(queueName string, getQueueNameFunc GetQueueNameFunc, qTaskId string) (int64, error) { //从哈希中移除 qTaskId不存在 返回0,nil qTaskId存在并且移除成功返回1,nil
	if getQueueNameFunc == nil {
		return 0, errors.New("为设置队列名称获取函数")
	}
	_, _, zsetQueueName, hsetQueueName := getQueueNameFunc(queueName)
	if zsetQueueName == "" || hsetQueueName == "" {
		return 0, errors.New("队列名称为空")
	}
	zsetKey := RedisQueueZsetPrefix + zsetQueueName
	if index, err := common.RedisZRem(zsetKey, qTaskId); err != nil {
		return index, err
	} else {
		hsetKey := RedisQueueHsetPrefix + hsetQueueName
		if exists, err := common.RedisHDel(hsetKey, qTaskId); err != nil {
			return exists, err
		} else {
			return exists, err
		}
	}
}

func Rank(queueName string, getQueueNameFunc GetQueueNameFunc, qTaskId string) (string, string, int64, float64, error) {
	if getQueueNameFunc == nil {
		return RankStatusErr, "参数错误", 0, 0, errors.New("为设置队列名称获取函数")
	}
	_, _, zsetQueueName, hsetQueueName := getQueueNameFunc(queueName)
	if zsetQueueName == "" || hsetQueueName == "" {
		return RankStatusErr, "参数错误", 0, 0, errors.New("队列名称为空")
	}
	hsetKey := RedisQueueHsetPrefix + hsetQueueName
	if taskJson, err := common.RedisHGet(hsetKey, qTaskId); err != nil {
		return RankStatusErr, "获取任务信息失败", 0, 0, err
	} else {
		if taskJson == "" {
			zsetKey := RedisQueueZsetPrefix + zsetQueueName
			if index, err := common.RedisZRank(zsetKey, qTaskId); err != nil {
				return RankStatusErr, "获取任务位置信息失败", index, 0, err
			} else {
				if index >= 0 {
					return RankStatusWaiting, fmt.Sprintf("当前排在第%d位", index), index, 0, nil
				}
			}
			return RankStatusNoData, "未获取到任务信息", 0, 0, nil
		}
		var task Task
		if err := utils.GetStructFromJson(&task, taskJson); err != nil {
			return RankStatusWaiting, "解析任务信息失败", 0, task.QTaskProgress, err
		} else {
			if task.QTaskCompletedAt > 0 {
				if task.QTaskErr == "" {
					return RankStatusSuc, "任务执行成功", 0, task.QTaskProgress, nil
				} else {
					return RankStatusFail, "任务执行失败", 0, task.QTaskProgress, nil
				}
			} else {
				progress := task.QTaskProgress * 100
				progressStr := strings.TrimRight(strings.TrimRight(fmt.Sprintf("%.2f", progress), "0"), ".")
				return RankStatusRunning, fmt.Sprintf("正在执行任务，当前进度%s%%", progressStr), 0, task.QTaskProgress, nil
			}
		}
	}
}

func TaskInfo(queueName string, getQueueNameFunc GetQueueNameFunc, qTaskId string) (Task, error) {
	var task Task
	if getQueueNameFunc == nil {
		return task, errors.New("为设置队列名称获取函数")
	}
	_, _, _, hsetQueueName := getQueueNameFunc(queueName)
	if hsetQueueName == "" {
		return task, errors.New("队列名称为空")
	}

	zsetKey := RedisQueueHsetPrefix + hsetQueueName
	if taskJson, err := common.RedisHGet(zsetKey, qTaskId); err != nil {
		return task, err
	} else {
		if taskJson == "" {
			return task, common.ErrRecordNotFound
		}
		if err := utils.GetStructFromJson(&task, taskJson); err != nil {
			return task, err
		} else {
			return task, nil
		}
	}
}

/*
func Clear() error { //清除残余Redis数据
	hsetKeys, err := common.RedisScanKeys(RedisQueueHsetPrefix + "*")
	if err != nil {
		logger.Error(err)
		return err
	}
	count := int64(100)
	for _, hsetKey := range hsetKeys {
		cursor := uint64(0)
		queueName := strings.TrimLeft(hsetKey, RedisQueueHsetPrefix)
		CheckAndClearRank(queueName)
		for {
			if nextCursor, mTask, err := common.RedisHScan(hsetKey, cursor, count); err != nil {
				logger.Error(err)
				break
			} else {
				cursor = nextCursor
				CheckAndClearHash(queueName, mTask)
				if nextCursor == 0 || len(mTask) == 0 {
					break
				}
			}
		}
	}
	return nil
}

func CheckAndClearRank(zsetQueueName string) error {
	zsetKey := RedisQueueZsetPrefix + zsetQueueName
	slice := common.RedisZRangeWithScores(zsetKey, 50)
	if len(slice.Val()) == 0 {
		return nil
	}
	tenSeconds := int64(10 * 1000)
	for _, z := range slice.Val() {
		qTaskId := z.Member.(string)
		qTaskPushAt := int64(z.Score)

		if pushAt, ok := LastPopAt.Load(queueName); !ok {

		} else {
			lastPushAt := pushAt.(int64)
			if qTaskPushAt < lastPushAt-tenSeconds {
				RemoveRank(queueName, qTaskId)
			}
			if qTaskPushAt > lastPushAt {
				break
			}
		}
	}
	return nil
}

func CheckAndClearHash(queueName string, mTask map[string]string) {
	clearTaskIds := make([]string, 0)
	for taskId, taskValue := range mTask {
		if taskValue == "" {
			clearTaskIds = append(clearTaskIds, taskId)
			continue
		}
		var task Task
		if err := utils.GetStructFromJson(&task, taskValue); err != nil {
			logger.Error("CheckAndClear GetStructFromJson err:", err)
		} else {
			completedOutMilli := int64(24 * 1 * 3600000)
			fiveMinutes := int64(5 * 60 * 1000)
			tenSeconds := int64(10 * 1000)
			if task.QTaskCompletedAt <= 0 {
				if pushAt, ok := LastPopAt.Load(queueName); !ok {

				} else {
					lastPushAt := pushAt.(int64)
					if task.QTaskPushAt < lastPushAt-tenSeconds {
						if task.QTaskUpdatedAt < time.Now().UnixMilli()-fiveMinutes { //5分钟没有进度更新
							if task.QTaskUpdatedAt < time.Now().UnixMilli()-completedOutMilli {
								clearTaskIds = append(clearTaskIds, taskId)
								continue
							}
						}
					}
				}

			} else {
				if task.QTaskCompletedAt < time.Now().UnixMilli()-completedOutMilli {
					clearTaskIds = append(clearTaskIds, taskId)
					continue
				}
			}
		}
	}
	for _, taskId := range clearTaskIds {
		RemoveHash(queueName, taskId)
	}
}*/

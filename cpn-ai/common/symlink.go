package common

import (
	"fmt"
	"os"
	"path/filepath"
)

// CreateSymbolLink 创建一个指向目标文件的符号链接，使用提供的链接名称。
// 如果链接已经存在，则返回错误。
func CreateSymbolLink(target, linkName string) error {
	// 检查目标是否存在
	_, err := os.Stat(target)
	if err != nil && os.IsNotExist(err) {
		return fmt.Errorf("目标文件不存在: %s", target)
	}

	// 转换为绝对路径，避免相对路径问题
	targetAbs, err := filepath.Abs(target)
	if err != nil {
		return fmt.Errorf("获取目标文件的绝对路径失败: %w", err)
	}

	linkNameAbs, err := filepath.Abs(linkName)
	if err != nil {
		return fmt.Errorf("获取链接文件的绝对路径失败: %w", err)
	}

	// 检查链接是否已经存在
	_, err = os.Lstat(linkNameAbs)
	if err == nil {
		return fmt.Errorf("链接已存在: %s", linkNameAbs)
	}

	// 创建符号链接
	err = os.Symlink(targetAbs, linkNameAbs)
	if err != nil {
		return fmt.Errorf("创建符号链接失败: %w", err)
	}

	return nil
}

// RemoveSymbolLink 删除一个符号链接。
// 确保只删除符号链接，而不是普通文件或目录。
func RemoveSymbolLink(linkName string) error {
	// 获取绝对路径
	linkNameAbs, err := filepath.Abs(linkName)
	if err != nil {
		return fmt.Errorf("获取链接文件的绝对路径失败: %w", err)
	}

	// 检查路径是否存在且为符号链接
	info, err := os.Lstat(linkNameAbs)
	if err != nil {
		if os.IsNotExist(err) {
			return fmt.Errorf("链接不存在: %s", linkNameAbs)
		}
		return fmt.Errorf("访问链接失败: %w", err)
	}

	// 确保是符号链接
	if info.Mode()&os.ModeSymlink == 0 {
		return fmt.Errorf("路径不是符号链接: %s", linkNameAbs)
	}

	// 删除符号链接
	err = os.Remove(linkNameAbs)
	if err != nil {
		return fmt.Errorf("删除符号链接失败: %w", err)
	}

	return nil
}

// IsSymlink 检查给定路径是否为符号链接。
func IsSymlink(path string) (bool, error) {
	// 获取绝对路径
	pathAbs, err := filepath.Abs(path)
	if err != nil {
		return false, fmt.Errorf("获取绝对路径失败: %w", err)
	}

	// 获取文件信息，不跟随符号链接
	info, err := os.Lstat(pathAbs)
	if err != nil {
		if os.IsNotExist(err) {
			return false, nil // 路径不存在，所以不是符号链接
		}
		return false, fmt.Errorf("访问路径失败: %w", err)
	}

	// 检查是否为符号链接
	return info.Mode()&os.ModeSymlink != 0, nil
}

// GetSymbolLinkTarget 返回符号链接指向的目标路径。
func GetSymbolLinkTarget(linkName string) (string, error) {
	// 获取绝对路径
	linkNameAbs, err := filepath.Abs(linkName)
	if err != nil {
		return "", fmt.Errorf("获取链接文件的绝对路径失败: %w", err)
	}

	// 检查是否为符号链接
	isLink, err := IsSymlink(linkNameAbs)
	if err != nil {
		return "", err
	}
	if !isLink {
		return "", fmt.Errorf("路径不是符号链接: %s", linkNameAbs)
	}

	// 读取链接
	target, err := os.Readlink(linkNameAbs)
	if err != nil {
		return "", fmt.Errorf("读取符号链接失败: %w", err)
	}

	return target, nil
}

// EnsureDirExists 确保指定的目录路径存在，如果不存在则创建它。
// 支持创建多级目录，类似于 mkdir -p 命令。
func EnsureDirExists(dirPath string) error {
	// 获取绝对路径
	absPath, err := filepath.Abs(dirPath)
	if err != nil {
		return fmt.Errorf("获取绝对路径失败: %w", err)
	}

	// 检查目录是否存在
	info, err := os.Stat(absPath)
	if err == nil {
		// 路径存在，检查是否为目录
		if !info.IsDir() {
			return fmt.Errorf("路径存在但不是目录: %s", absPath)
		}
		return nil // 目录已存在，无需创建
	}

	// 如果错误不是因为路径不存在，则返回错误
	if !os.IsNotExist(err) {
		return fmt.Errorf("检查目录时出错: %w", err)
	}

	// 创建目录，包括所有必需的父目录
	err = os.MkdirAll(absPath, 0755) // 0755 是常用的目录权限
	if err != nil {
		return fmt.Errorf("创建目录失败: %w", err)
	}

	return nil
}

// EnsureFilePathExists 确保文件的父目录路径存在，如果不存在则创建它。
// 此函数对于保存文件前确保目录存在非常有用。
func EnsureFilePathExists(filePath string) error {
	// 获取文件的目录部分
	dirPath := filepath.Dir(filePath)
	return EnsureDirExists(dirPath)
}

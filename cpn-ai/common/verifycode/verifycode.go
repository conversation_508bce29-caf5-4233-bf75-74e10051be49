package verifycode

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"encoding/json"
	"errors"
	"github.com/go-redis/redis/v8"
	"github.com/wenlng/go-captcha-assets/resources/imagesv2"
	"github.com/wenlng/go-captcha-assets/resources/tiles"
	"github.com/wenlng/go-captcha/v2/slide"
	"time"
)

const (
	CaptchaExpiration = 2 * time.Minute
)

// SlideData 滑块验证码数据结构
type SlideData struct {
	X int `json:"x"`
	Y int `json:"y"`
}

// SlideCaptchaResponse 滑块验证码响应结构
type SlideCaptchaResponse struct {
	CodeID      string `json:"code_id"`
	MasterImage string `json:"master_image"`
	TileImage   string `json:"tile_image"`
}

type RedisStore struct{}

// SetSlideData 存储滑块验证码数据
func (s *RedisStore) SetSlideData(id string, data *SlideData) error {
	key := enums.RedisKeyEnum.CaptCha + id
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	return common.RedisSet(key, string(jsonData), CaptchaExpiration)
}

// GetSlideData 获取滑块验证码数据
func (s *RedisStore) GetSlideData(id string, clear bool) (*SlideData, error) {
	key := enums.RedisKeyEnum.CaptCha + id
	val, err := common.RedisGet(key)
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			logger.Error("Failed to get captcha from Redis:", err)
		}
		return nil, err
	}

	if clear {
		if err := common.RedisDel(key); err != nil {
			logger.Error("Failed to delete captcha from Redis:", err)
		}
	}

	var data SlideData
	if err := json.Unmarshal([]byte(val), &data); err != nil {
		return nil, err
	}

	return &data, nil
}

// 保留原有方法以兼容其他可能的使用
func (s *RedisStore) Set(id string, value string) error {
	key := enums.RedisKeyEnum.CaptCha + id
	return common.RedisSet(key, value, CaptchaExpiration)
}

func (s *RedisStore) Get(id string, clear bool) string {
	key := enums.RedisKeyEnum.CaptCha + id
	val, err := common.RedisGet(key)
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			logger.Error("Failed to get captcha from Redis:", err)
		}
		return ""
	}

	if clear {
		if err := common.RedisDel(key); err != nil {
			logger.Error("Failed to delete captcha from Redis:", err)
		}
	}

	return val
}

func (s *RedisStore) Verify(id, answer string, clear bool) bool {
	key := enums.RedisKeyEnum.CaptCha + id
	val, err := common.RedisGet(key)
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			logger.Error("Failed to get captcha from Redis:", err)
		}
		return false
	}

	match := val == answer

	if clear {
		if err := common.RedisDel(key); err != nil {
			logger.Error("Failed to delete captcha from Redis:", err)
		}
	}

	return match
}

var result = &RedisStore{}
var slideCapt slide.Captcha

// 初始化滑块验证码
func init() {
	builder := slide.NewBuilder(
		//slide.WithImageSize(option.Size{Width: 300, Height: 220}),
		//slide.WithRangeGraphSize(option.RangeVal{Min: 50, Max: 80}),
		//slide.WithEnableGraphVerticalRandom(true),
	)

	// 创建简单的背景图片和滑块图片
	backgrounds, err := imagesv2.GetImages()
	if err != nil {
		logger.Error("Failed to create background images:", err)
		return
	}
	graphImages, err := tiles.GetTiles()
	if err != nil {
		logger.Error("Failed to create graph images:", err)
		return
	}
	var newGraphs = make([]*slide.GraphImage, 0, len(graphImages))
	for i := 0; i < len(graphImages); i++ {
		graph := graphImages[i]
		newGraphs = append(newGraphs, &slide.GraphImage{
			OverlayImage: graph.OverlayImage,
			MaskImage:    graph.MaskImage,
			ShadowImage:  graph.ShadowImage,
		})
	}

	builder.SetResources(
		slide.WithBackgrounds(backgrounds),
		slide.WithGraphImages(newGraphs),
	)

	slideCapt = builder.Make()
}

// GetNewCode 生成新的滑块验证码
// @Result id 验证码id
// @Result masterImage 主图base64编码
// @Result tileImage 滑块图base64编码
// @Result err 错误
func GetNewCode() (string, string, string, int, error) {
	captData, err := slideCapt.Generate()
	if err != nil {
		return "", "", "", 0, err
	}

	blockData := captData.GetData()
	if blockData == nil {
		return "", "", "", 0, errors.New("生成验证码数据失败")
	}

	// 生成唯一ID
	id := common.GenerateVerificationCode(32)

	// 存储验证码坐标数据
	slideData := &SlideData{
		X: blockData.X,
		Y: blockData.Y,
	}

	if err := result.SetSlideData(id, slideData); err != nil {
		return "", "", "", 0, err
	}

	// 获取图片base64
	masterBase64, err := captData.GetMasterImage().ToBase64()
	if err != nil {
		return "", "", "", 0, err
	}

	tileBase64, err := captData.GetTileImage().ToBase64()
	if err != nil {
		return "", "", "", 0, err
	}

	return id, masterBase64, tileBase64, blockData.Y, nil
}

// VerifySlideCaptcha 验证滑块验证码
// @Param id 验证码id
// @Param x 用户滑动的X坐标
// @Param y 用户滑动的Y坐标
// @Result true：正确，false：失败
func VerifySlideCaptcha(id string, x, y int) bool {
	slideData, err := result.GetSlideData(id, true)
	if err != nil {
		logger.Error("Failed to get slide captcha data:", err)
		return false
	}

	// 使用go-captcha库的验证方法，允许一定的误差范围
	return slide.Validate(x, y, slideData.X, slideData.Y, 5)
}

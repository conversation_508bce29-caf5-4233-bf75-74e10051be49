package enums

type trainTaggerEnum_ struct {
	WD14, JoyCaption2, Florence2, <PERSON>wen string
}

type trainLevelEnum_ struct {
	Idle, Normal, Member string
}

var TrainTaggerEnum = trainTaggerEnum_{
	WD14:        "WD14",
	JoyCaption2: "JoyCaption2",
	Florence2:   "Florence2",
	Qwen:        "Qwen",
}

var TrainLevelEnum = trainLevelEnum_{
	Idle:   "Idle",
	Normal: "Normal",
	Member: "Member",
}

var TrainTaggerTitle = map[string]string{
	"WD14":        "WD14",
	"JoyCaption2": "JoyCaption2",
	"Florence2":   "TaggerFlorence2",
	"Qwen":        "通义千问",
}

func (obj trainTaggerEnum_) GetTaggerTitle(v string) string {
	if val, ok := TrainTaggerTitle[v]; ok {
		//存在
		return val
	}
	return ""
}

func (obj trainLevelEnum_) GetTrainLevelTitle(v string) string {
	if val, ok := TrainLevelTitle[v]; ok {
		//存在
		return val
	}
	return ""
}

var TrainLevelTitle = map[string]string{
	"Idle":   "闲时队列",
	"Normal": "普通队列",
	"Member": "会员队列",
}

type trainQueueNameEnum_ struct {
	TaggerWD14, TaggerJoyCaption2, TaggerFlorence2, TaggerQwen, TrainIdel, TrainNormal, TrainMember string
}

var TrainQueueNameEnum = trainQueueNameEnum_{
	TaggerWD14:        "tagger:WD14",        //打标普通队列
	TaggerJoyCaption2: "tagger:JoyCaption2", //打标会员队列
	TaggerFlorence2:   "tagger:Florence2",
	TaggerQwen:        "tagger:Qwen",
	TrainIdel:         "train:Idel",   //闲时队列
	TrainNormal:       "train:Normal", //训练普通队列
	TrainMember:       "train:Member", //训练会员队列
}

func (obj trainQueueNameEnum_) GetTaggerQueueName(tagger string) string {
	if tagger == TrainTaggerEnum.WD14 {
		return TrainQueueNameEnum.TaggerWD14
	} else if tagger == TrainTaggerEnum.JoyCaption2 {
		return TrainQueueNameEnum.TaggerJoyCaption2
	} else if tagger == TrainTaggerEnum.Florence2 {
		return TrainQueueNameEnum.TaggerFlorence2
	} else if tagger == TrainTaggerEnum.Qwen {
		return TrainQueueNameEnum.TaggerQwen
	}
	return ""
}

func (obj trainQueueNameEnum_) GetJobQueueName(levelName string) string {
	if levelName == TrainLevelEnum.Normal {
		return TrainQueueNameEnum.TrainNormal
	} else if levelName == TrainLevelEnum.Member {
		return TrainQueueNameEnum.TrainMember
	}
	return ""
}

//
//var TrainQueueTitle = map[string]string{
//	"TaggerWD14":        "WD14",
//	"TaggerJoyCaption2": "JoyCaption2",
//	"TaggerFlorence2":   "TaggerFlorence2",
//	"TaggerQwen":        "通义千问",
//	"train_normal":      "训练普通队列",
//	"train_member":      "训练会员队列",
//}

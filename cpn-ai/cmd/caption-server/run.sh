docker run \
  --gpus '"device=0"' \
  --shm-size 16g \
  --cpus="30" \
  --memory="90g" \
  -p 11090:8888 \
  -p 12090:8195 \
  -v /root/suanyun-share/e4f7d38ed893414fa0b070adebdb4835:/models:ro \
  -v /root/suanyun-share/e4f7d38ed893414fa0b070adebdb4835:/poddata:ro \
  -v /mnt/nvme/chenyu-nvme:/mnt/chenyu-nvme:ro \
  -v /mnt/nvme/chenyu-nvme:/chenyudata:ro \
  -v /mnt/user-data/store0:/chenyu_train_suanyun_user \
  --label 'gpus=0' \
  -e BATCH_SIZE=8 \
  -d 10.20.103.240/chenyu/public/e4f7d38ed893414fa0b070adebdb4835:v1.1

docker run \
  --gpus '"device=1"' \
  --shm-size 16g \
  --cpus="30" \
  --memory="90g" \
  -p 11091:8888 \
  -p 12091:8195 \
  -v /root/suanyun-share/e4f7d38ed893414fa0b070adebdb4835:/models:ro \
  -v /root/suanyun-share/e4f7d38ed893414fa0b070adebdb4835:/poddata:ro \
  -v /mnt/nvme/chenyu-nvme:/mnt/chenyu-nvme:ro \
  -v /mnt/nvme/chenyu-nvme:/chenyudata:ro \
  -v /mnt/user-data/store0:/chenyu_train_suanyun_user \
  --label 'gpus=1' \
  -e BATCH_SIZE=8 \
  -d 10.20.103.240/chenyu/public/e4f7d38ed893414fa0b070adebdb4835:v1.1

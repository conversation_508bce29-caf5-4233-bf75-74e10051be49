import os
import sys
from pathlib import Path
from typing import Dict, Any, List
from pathlib import Path
from tqdm.auto import tqdm

import dataclasses
from PIL import Image

from transformers import (
    AutoTokenizer,
    LlavaForConditionalGeneration,
    PreTrainedTokenizer,
    PreTrainedTokenizerFast,
)

import torch
import torch.amp
from torch.utils.data import DataLoader, Dataset
import torchvision.transforms.functional as TVF

from loguru import logger

LOG_DIR = "/root/caption-server/logs"
LOG_LEVEL_STR = "INFO"
LOG_SYS_BOOL = True
JOY2_MODEL_STR = "/chenyudata/ComfyUI/models/LLM/llama-joycaption-alpha-two-hf-llava"
IMAGE_EXTENSIONS_LIST = [".jpg", ".jpeg", ".png", ".webp"]

os.makedirs(LOG_DIR, exist_ok=True)
log_file_path = os.path.join(LOG_DIR, "file_{time}.log")

logger.remove()
logger.add(log_file_path, rotation="1 MB", level=LOG_LEVEL_STR)  # 每个文件最大1MB

if LOG_SYS_BOOL:
    logger.add(sys.stdout, level=LOG_LEVEL_STR)


@dataclasses.dataclass
class Prompt:
    prompt: str
    weight: float


class ImageDataset(Dataset):
    def __init__(
        self,
        prompt: str,
        paths: list[Path],
        tokenizer: PreTrainedTokenizer | PreTrainedTokenizerFast,
        image_token_id: int,
        image_seq_length: int,
    ):
        self.prompt = prompt
        self.paths = paths
        self.tokenizer = tokenizer
        self.image_token_id = image_token_id
        self.image_seq_length = image_seq_length
        self.pad_token_id = tokenizer.pad_token_id  # 填充标记的ID

    def __len__(self):
        return len(self.paths)  # 返回数据集的长度

    def __getitem__(self, idx: int) -> dict:
        path = self.paths[idx]  # 获取图像路径

        try:
            image = Image.open(path)
            if image.size != (384, 384):
                image = image.resize((384, 384), Image.LANCZOS)
            image = image.convert("RGB")  # 转换为RGB格式
            pixel_values = TVF.pil_to_tensor(image)  # 将图像转换为张量
        except Exception as e:
            logger.error(f"Failed to load image {path}: {str(e)}")
            pixel_values = None  # 后续将被过滤掉

        # 构建对话内容
        convo = [
            {
                "role": "system",
                "content": "You are a helpful image captioner.",
            },
            {
                "role": "user",
                "content": self.prompt,
            },
        ]

        # 格式化对话内容
        convo_string = self.tokenizer.apply_chat_template(
            convo, tokenize=False, add_generation_prompt=True
        )
        assert isinstance(convo_string, str)

        # 对对话进行分词
        convo_tokens = self.tokenizer.encode(
            convo_string, add_special_tokens=False, truncation=False
        )

        # 根据图像序列长度重复图像标记
        input_tokens = []
        for token in convo_tokens:
            if token == self.image_token_id:
                input_tokens.extend(
                    [self.image_token_id] * self.image_seq_length
                )  # 扩展图像标记
            else:
                input_tokens.append(token)

        input_ids = torch.tensor(input_tokens, dtype=torch.long)  # 转换为张量
        attention_mask = torch.ones_like(input_ids)  # 创建注意力掩码

        return {
            "path": path,  # 返回路径
            "pixel_values": pixel_values,  # 返回像素值
            "input_ids": input_ids,  # 返回输入ID
            "attention_mask": attention_mask,  # 返回注意力掩码
        }

    def collate_fn(self, batch: list[dict]) -> dict:
        # 过滤掉未成功加载的图像
        batch = [item for item in batch if item["pixel_values"] is not None]

        if not batch:
            return {
                "paths": [],
                "pixel_values": torch.empty(0),  # 空的像素值张量
                "input_ids": torch.empty(0),  # 空的输入ID张量
                "attention_mask": torch.empty(0),  # 空的注意力掩码张量
            }

        # 填充 input_ids 和 attention_mask
        max_length = max(item["input_ids"].shape[0] for item in batch)
        n_pad = [max_length - item["input_ids"].shape[0] for item in batch]
        input_ids = torch.stack(
            [
                torch.nn.functional.pad(
                    item["input_ids"], (n, 0), value=self.pad_token_id
                )
                for item, n in zip(batch, n_pad)
            ]
        )
        attention_mask = torch.stack(
            [
                torch.nn.functional.pad(item["attention_mask"], (n, 0), value=0)
                for item, n in zip(batch, n_pad)
            ]
        )

        # 堆叠像素值
        pixel_values = torch.stack([item["pixel_values"] for item in batch])

        # 图像路径
        paths = [item["path"] for item in batch]

        return {
            "paths": paths,
            "pixel_values": pixel_values,
            "input_ids": input_ids,
            "attention_mask": attention_mask,
        }


def verify_gpu_input_right(gpu_ids: str):
    if gpu_ids.strip().lower() == "all":
        pass
    else:
        valid_chars = set("0123456789, ")
        if not all(char in valid_chars for char in gpu_ids):
            logger.error(
                "Input error: Please use a proper format like '0,1,2', 'all', or a single available GPU id."
            )
            raise ValueError("Invalid GPU ID format.")
    return True


def get_all_gpus():
    return [f"cuda:{i}" for i in range(torch.cuda.device_count())]


def convert_to_device_list(gpu_ids: str):
    if gpu_ids.strip().lower() == "all":
        return get_all_gpus()
    else:
        verify_gpu_input_right(gpu_ids)
        available_gpus = []
        gpu_ids = gpu_ids.replace("，", ",")  # 处理中文逗号
        for gpu_id in gpu_ids.split(","):
            gpu_id = gpu_id.strip()  # 清理多余的空格
            if torch.cuda.is_available() and int(gpu_id) < torch.cuda.device_count():
                available_gpus.append(f"cuda:{gpu_id}")
            else:
                logger.error(f"GPU {gpu_id} is not available or is out of range.")
                raise ValueError("GPU ID is not available or is out of range.")
    return available_gpus


class CaptionBase:
    def __init__(
        self,
        model_name,
        devices_key="all",
        local=True,
        download=True,
    ):
        self.model_name = model_name
        self.devices = convert_to_device_list(devices_key)
        self.is_load_model = False
        self.model_urls = {}
        self.is_download_model = download

    def is_model_exists_in_path(self):
        # 查看是否存在路径或模型文件
        if os.path.exists(self.model_name):
            return True
        else:
            logger.info("Model path does not exist / Input name is not a local path")
            return False

    def download_model(self):
        # TODO
        pass

    def load_model(self):
        pass

    def load_model_in_one_gpu(self, device):
        pass

    def generlate_base(self):
        pass

    def generate_captions(self):
        pass

    def generate_caption(self):
        pass

    def create_service(self):
        pass

    def generate_caption_remote(self):
        pass


def find_images_in_directory(directory: Path, recursive: bool = True):
    images = []
    for item in directory.iterdir():
        if item.is_file() and item.suffix.lower() in IMAGE_EXTENSIONS_LIST:
            images.append(item)
        elif item.is_dir() and recursive:
            images.extend(find_images_in_directory(item, recursive))
    return images


def filter_images_with_txt(image_paths: List[Path]):
    image_paths_with_caption = [
        path for path in image_paths if Path(path).with_suffix(".txt").exists()
    ]
    logger.info(f"existing {len(image_paths_with_caption)} captions")
    image_paths_set = set(image_paths)
    image_paths_with_caption_set = set(image_paths_with_caption)
    difference = image_paths_set - image_paths_with_caption_set
    return list(difference)


def none_or_type(value, desired_type):
    if value == "None":
        return None
    return desired_type(value)


def trim_off_prompt(input_ids: list[int], eoh_id: int, eot_id: int) -> list[int]:
    # Trim off the prompt
    while True:
        try:
            i = input_ids.index(eoh_id)
        except ValueError:
            break

        input_ids = input_ids[i + 1 :]

    # Trim off the end
    try:
        i = input_ids.index(eot_id)
    except ValueError:
        return input_ids

    return input_ids[:i]


def write_caption(image_path: Path, caption: str):
    caption_path = image_path.with_suffix(".txt")

    try:
        f = os.open(
            caption_path, os.O_WRONLY | os.O_CREAT | os.O_EXCL
        )  # Write-only, create if not exist, fail if exists
    except FileExistsError:
        # logging.warning(f"Caption file '{caption_path}' already exists")
        return
    except Exception as e:
        # logging.error(f"Failed to open caption file '{caption_path}': {e}")
        return

    try:
        os.write(f, caption.encode("utf-8"))
        os.close(f)
    except Exception as e:
        # logging.error(f"Failed to write caption to '{caption_path}': {e}")
        return


class Joy2(CaptionBase):
    def __init__(
        self,
        model_name="llama-joycaption-alpha-two-hf-llava",
        devices_key=0,  # 只接受0-7的单个数字
    ):
        if not isinstance(devices_key, int) or not (0 <= devices_key <= 7):
            raise ValueError("devices_key must be an integer between 0 and 7")

        self.device = f"cuda:{devices_key}"
        self.model_name = model_name
        self.is_load_tokenizer = False
        self.model = None

        self.load_model()

    def _verify_model_name(self):
        if self.model_name not in self.model_urls:
            raise ValueError(f"Model name {self.model_name} not found in model_urls")

    def load_tokenizer(self):
        if self.is_load_tokenizer:
            return self.tokenizer

        self.tokenizer = AutoTokenizer.from_pretrained(self.model_name, use_fast=True)
        is_valid_tokenizer = isinstance(
            self.tokenizer, PreTrainedTokenizer
        ) or isinstance(self.tokenizer, PreTrainedTokenizerFast)
        tokenizer_valid_error_info = f"Tokenizer is of type {type(self.tokenizer)}"
        assert is_valid_tokenizer, tokenizer_valid_error_info

        self.end_of_header_id = self.tokenizer.convert_tokens_to_ids(
            "<|end_header_id|>"
        )
        self.end_of_turn_id = self.tokenizer.convert_tokens_to_ids("<|eot_id|>")
        assert isinstance(self.end_of_header_id, int) and isinstance(
            self.end_of_turn_id, int
        )

        self.is_load_tokenizer = True
        logger.info("joycaption2: load tokenizer done")
        return self.tokenizer

    def load_model(self):
        """加载模型到指定GPU"""
        if not self.is_load_tokenizer:
            self.load_tokenizer()

        self.model = LlavaForConditionalGeneration.from_pretrained(
            JOY2_MODEL_STR, torch_dtype="bfloat16"
        ).to(self.device)

        self.image_token_id = self.model.config.image_token_index
        self.image_seq_length = self.model.config.image_seq_length

        self.vision_dtype = (
            self.model.vision_tower.vision_model.embeddings.patch_embedding.weight.dtype
        )
        self.vision_device = (
            self.model.vision_tower.vision_model.embeddings.patch_embedding.weight.device
        )

        logger.info(f"joycaption2: load model done on {self.device}")

    def find_images_by_dir(self, image_dir, filter_captoion=True, recursive=True):
        image_paths = find_images_in_directory(image_dir, recursive=recursive)
        if filter_captoion:
            return filter_images_with_txt(image_paths)
        else:
            return image_paths

    def images_dataloader(self, prompt, image_paths, batch_size, num_workers=1):
        dataset = ImageDataset(
            prompt=prompt,
            paths=image_paths,
            tokenizer=self.tokenizer,
            image_token_id=self.image_token_id,
            image_seq_length=self.image_seq_length,
        )
        return DataLoader(
            dataset,
            collate_fn=dataset.collate_fn,
            num_workers=num_workers,
            shuffle=False,
            drop_last=False,
            batch_size=batch_size,
        )

    def generate_base(self, data, max_tokens, temperature, top_k, top_p, is_greedy):
        """使用模型生成图片描述"""
        # 将数据移动到正确的设备上
        pixel_values = data["pixel_values"].to(self.vision_device, non_blocking=True)
        input_ids = data["input_ids"].to(self.device, non_blocking=True)
        attention_mask = data["attention_mask"].to(self.device, non_blocking=True)

        # 标准化图像
        pixel_values = pixel_values / 255.0
        pixel_values = TVF.normalize(pixel_values, [0.5], [0.5])
        pixel_values = pixel_values.to(self.vision_dtype)

        # 使用模型生成描述
        generate_ids = self.model.generate(
            input_ids=input_ids,
            pixel_values=pixel_values,
            attention_mask=attention_mask,
            max_new_tokens=max_tokens,
            do_sample=not is_greedy,
            suppress_tokens=None,
            use_cache=True,
            temperature=temperature,
            top_k=top_k,
            top_p=top_p,
        )

        # 处理生成的ID
        generate_ids = generate_ids.tolist()
        generate_ids = [
            trim_off_prompt(ids, self.end_of_header_id, self.end_of_turn_id)
            for ids in generate_ids
        ]

        # 解码生成的文本
        captions = self.tokenizer.batch_decode(
            generate_ids, skip_special_tokens=False, clean_up_tokenization_spaces=False
        )
        return [c.strip() for c in captions]

    def generate_caption_by_path(
        self,
        image: str,
        prompt: str,
        batch_size=1,
        prepend_string="",
        append_string="",
        max_tokens=300,
        temperature=0.5,
        top_p=0.9,
        top_k=10,
        is_greedy=False,
    ):
        """为单张图片生成描述"""
        dataloader = self.images_dataloader(prompt, [image], batch_size)
        for batch in dataloader:
            captions = self.generate_base(
                batch, max_tokens, temperature, top_k, top_p, is_greedy
            )
            caption_str = prepend_string + captions[0] + append_string
            logger.debug(f"Caption: {caption_str}")
            return caption_str

    def generate_captions_by_paths(
        self,
        image_dir,
        image_paths,
        prompt,
        batch_size=1,
        prepend_string="",
        append_string="",
        max_tokens=300,
        temperature=0.5,
        top_p=0.9,
        top_k=10,
        is_greedy=False,
        overwrite=False,
    ):
        """为图片列表生成描述"""
        if not overwrite:
            original_count = len(image_paths)
            image_paths = filter_images_with_txt(image_paths)
            filtered_count = len(image_paths)
            print(
                f"{image_dir}: Found images need processing: {original_count}/{filtered_count}"
            )

        if not image_paths:
            print(f"{image_dir}: No images to process")
            return

        dataloader = self.images_dataloader(prompt, image_paths, batch_size)
        total_batches = len(dataloader)
        processed_images = 0

        print(
            f"{image_dir}: Starting caption generation for {len(image_paths)} images in {total_batches} batches"
        )

        for batch_idx, batch in enumerate(
            tqdm(dataloader, desc="Generating captions", total=total_batches)
        ):
            captions = self.generate_base(
                batch, max_tokens, temperature, top_k, top_p, is_greedy
            )

            for path, caption in zip(batch["paths"], captions):
                caption_str = prepend_string + caption + append_string
                write_caption(Path(path), caption_str)
                processed_images += 1

            # if (batch_idx + 1) % 10 == 0 or (batch_idx + 1) == total_batches:
            print(f"{image_dir}: Processed images: {processed_images}")

        print(
            f"{image_dir}: Caption generation completed. Total images processed: {processed_images}"
        )

    def generate_captions_by_dir(
        self,
        image_dir,
        prompt,
        batch_size=1,
        prepend_string="",
        append_string="",
        max_tokens=300,
        temperature=0.5,
        top_p=0.9,
        top_k=10,
        is_greedy=False,
        overwrite=False,
        recursive=True,
    ):
        """为目录中的所有图片生成描述"""
        print(f"{image_dir}: Scanning directory")
        image_paths = find_images_in_directory(Path(image_dir), recursive=recursive)

        if not image_paths:
            print(f"{image_dir}: No images found in directory")
            return

        print(f"{image_dir}: Found images in directory: {len(image_paths)}")

        self.generate_captions_by_paths(
            image_dir=image_dir,
            image_paths=image_paths,
            prompt=prompt,
            batch_size=batch_size,
            prepend_string=prepend_string,
            append_string=append_string,
            max_tokens=max_tokens,
            temperature=temperature,
            top_p=top_p,
            top_k=top_k,
            is_greedy=is_greedy,
            overwrite=overwrite,
        )


joy2 = Joy2(model_name=JOY2_MODEL_STR)
for line in sys.stdin:
    a = line.split("\t")
    if len(a) == 3:
        joy2.generate_captions_by_dir(
            image_dir=a[0],
            prompt=a[1],
            batch_size=int(float(a[2])),
        )
    else:
        print(line)
    sys.stdout.flush()

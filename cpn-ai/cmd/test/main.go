package main

import (
	"cpn-ai/service"
	"fmt"
)

func main() {
	instance_id := "56b12abc549142cabd9b98715bdcd206"
	startTime := 1745203800000
	endTime := 1745210729000
	gpus := []int{1}
	serverIp := "************"
	metricsToQuery := []string{"container_memory_working_set_bytes", "DCGM_FI_DEV_FB_USED"}

	err, data := service.MonitorData(instance_id, gpus, serverIp, metricsToQuery, int64(startTime), int64(endTime))
	if err != nil {
		fmt.Println(err)
		return
	}
	print(data)

	metrics, err := service.GetAvailableMetrics()
	if err != nil {
		fmt.Println(err)
	}

	fmt.Println(metrics)
}

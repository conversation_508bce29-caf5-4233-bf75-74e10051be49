package main

import (
	"cpn-ai/service"
	"fmt"
	"log"
)

func main() {
	// 测试扩展后的监控功能
	fmt.Println("开始测试扩展后的监控功能...")

	// 测试单个虚拟机监控信息获取
	testServerIp := "************" // 替换为实际的服务器IP

	fmt.Printf("正在获取服务器 %s 的完整监控信息...\n", testServerIp)

	monitorInfo, err := service.GpuMonitor.GetVirtualMonitorInfo(testServerIp)
	if err != nil {
		log.Printf("获取监控信息失败: %v", err)
		return
	}

	// 显示整体监控状态
	fmt.Printf("\n=== 整体监控状态 ===\n")
	fmt.Printf("监控状态: %s\n", monitorInfo.MonitorStatus)
	fmt.Printf("最后检查时间: %s\n", monitorInfo.LastCheckTime.Format("2006-01-02 15:04:05"))

	// 显示GPU监控信息
	fmt.Printf("\n=== GPU监控信息 ===\n")
	fmt.Printf("GPU总数: %d\n", monitorInfo.TotalGpus)
	fmt.Printf("正常GPU: %d\n", monitorInfo.NormalGpus)
	fmt.Printf("警告GPU: %d\n", monitorInfo.WarningGpus)
	fmt.Printf("异常GPU: %d\n", monitorInfo.CriticalGpus)
	fmt.Printf("离线GPU: %d\n", monitorInfo.OfflineGpus)

	if len(monitorInfo.GpuMetrics) > 0 {
		fmt.Println("\nGPU详细信息:")
		for _, gpu := range monitorInfo.GpuMetrics {
			fmt.Printf("GPU %s:\n", gpu.GpuId)
			fmt.Printf("  GPU型号: %s\n", gpu.GpuModel)
			fmt.Printf("  PCI总线ID: %s\n", gpu.PciBusId)
			fmt.Printf("  驱动版本: %s\n", gpu.DriverVersion)
			fmt.Printf("  UUID: %s\n", gpu.Uuid) // 添加UUID信息
			fmt.Printf("  状态: %s (%s)\n", gpu.Status, gpu.StatusDesc)
			fmt.Printf("  温度: %.1f°C\n", gpu.Temperature)
			fmt.Printf("  显存使用率: %.1f%%\n", gpu.MemoryUsage)
			fmt.Printf("  GPU利用率: %.1f%%\n", gpu.Utilization)
			fmt.Printf("  功耗: %.1fW\n", gpu.PowerUsage)
			if gpu.XidErrors > 0 {
				fmt.Printf("  XID错误: %d\n", gpu.XidErrors)
			}
			if gpu.EccErrors > 0 {
				fmt.Printf("  ECC错误: %d\n", gpu.EccErrors)
			}
			fmt.Println()
		}
	} else {
		fmt.Println("未获取到GPU监控数据")
	}

	// 显示CPU监控信息
	fmt.Printf("\n=== CPU监控信息 ===\n")
	if monitorInfo.CpuMetric != nil {
		cpu := monitorInfo.CpuMetric
		fmt.Printf("物理CPU数量: %d\n", cpu.PhysicalCpuCount)
		fmt.Printf("CPU型号: %s\n", cpu.CpuModel)
		fmt.Printf("总核心数: %d\n", cpu.TotalCores)
		fmt.Printf("整体使用率: %.1f%%\n", cpu.OverallUsage)
		fmt.Printf("负载 (1m/5m/15m): %.2f / %.2f / %.2f\n",
			cpu.LoadAverage1m, cpu.LoadAverage5m, cpu.LoadAverage15m)
		fmt.Printf("状态: %s (%s)\n", cpu.Status, cpu.StatusDesc)
		fmt.Printf("最后更新: %s\n", cpu.LastUpdate.Format("2006-01-02 15:04:05"))
	} else {
		fmt.Println("未获取到CPU监控数据")
	}

	// 显示内存监控信息
	fmt.Printf("\n=== 内存监控信息 ===\n")
	if monitorInfo.MemoryMetric != nil {
		mem := monitorInfo.MemoryMetric
		fmt.Printf("状态: %s (%s)\n", mem.Status, mem.StatusDesc)
		fmt.Printf("总内存: %.2f GB\n", mem.TotalMemory)
		fmt.Printf("已用内存: %.2f GB\n", mem.UsedMemory)
		fmt.Printf("空闲内存: %.2f GB\n", mem.FreeMemory)
		fmt.Printf("可用内存: %.2f GB\n", mem.AvailableMemory)
		fmt.Printf("内存使用率: %.1f%%\n", mem.UsagePercent)
		fmt.Printf("交换分区总量: %.2f GB\n", mem.SwapTotal)
		fmt.Printf("交换分区使用量: %.2f GB\n", mem.SwapUsed)
		fmt.Printf("交换分区使用率: %.1f%%\n", mem.SwapUsagePercent)
		fmt.Printf("最后更新: %s\n", mem.LastUpdate.Format("2006-01-02 15:04:05"))
	} else {
		fmt.Println("未获取到内存监控数据")
	}

	// 显示NVMe设备监控信息
	fmt.Printf("\n=== NVMe设备监控信息 ===\n")
	fmt.Printf("NVMe设备总数: %d\n", monitorInfo.TotalNvmeDevices)
	fmt.Printf("正常设备数: %d\n", monitorInfo.NormalNvmeDevices)
	fmt.Printf("警告设备数: %d\n", monitorInfo.WarningNvmeDevices)
	fmt.Printf("异常设备数: %d\n", monitorInfo.CriticalNvmeDevices)

	if len(monitorInfo.NvmeServices) > 0 {
		fmt.Println("\nNVMe设备详细信息:")
		for _, nvme := range monitorInfo.NvmeServices {
			fmt.Printf("设备 %s:\n", nvme.DeviceName)
			fmt.Printf("  挂载点: %s\n", nvme.MountPoint)
			fmt.Printf("  文件系统: %s\n", nvme.FileSystem)
			fmt.Printf("  状态: %s (%s)\n", nvme.Status, nvme.StatusDesc)
			fmt.Printf("  总空间: %.2f GB\n", nvme.TotalSpace)
			fmt.Printf("  已用空间: %.2f GB\n", nvme.UsedSpace)
			fmt.Printf("  可用空间: %.2f GB\n", nvme.AvailSpace)
			fmt.Printf("  使用率: %.1f%%\n", nvme.UsagePercent)
			fmt.Printf("  只读状态: %t\n", nvme.IsReadOnly)
			if nvme.ErrorMessage != "" {
				fmt.Printf("  错误信息: %s\n", nvme.ErrorMessage)
			}
			fmt.Printf("  最后检查: %s\n", nvme.LastCheckTime.Format("2006-01-02 15:04:05"))
			fmt.Println()
		}
	} else {
		fmt.Println("未获取到NVMe设备监控数据")
	}

	// 显示活跃告警
	fmt.Printf("\n=== 活跃告警 ===\n")
	if len(monitorInfo.ActiveAlerts) > 0 {
		fmt.Printf("告警数量: %d\n", len(monitorInfo.ActiveAlerts))
		for i, alert := range monitorInfo.ActiveAlerts {
			fmt.Printf("%d. %s\n", i+1, alert)
		}
	} else {
		fmt.Println("当前无活跃告警")
	}

	// 测试批量获取
	fmt.Printf("\n=== 批量监控测试 ===\n")
	fmt.Println("测试批量获取监控信息...")
	serverIps := []string{testServerIp}
	batchResult := service.GpuMonitor.GetBatchVirtualMonitorInfo(serverIps)

	for ip, info := range batchResult {
		fmt.Printf("服务器 %s:\n", ip)
		fmt.Printf("  整体状态: %s\n", info.MonitorStatus)
		fmt.Printf("  GPU数量: %d (正常:%d, 警告:%d, 异常:%d, 离线:%d)\n",
			info.TotalGpus, info.NormalGpus, info.WarningGpus, info.CriticalGpus, info.OfflineGpus)
		if info.CpuMetric != nil {
			fmt.Printf("  CPU: %d颗/%d核心/%.1f%%使用率\n",
				info.CpuMetric.PhysicalCpuCount, info.CpuMetric.TotalCores, info.CpuMetric.OverallUsage)
		} else {
			fmt.Printf("  CPU: 数据获取失败\n")
		}
		if info.MemoryMetric != nil {
			fmt.Printf("  内存使用率: %.1f%%\n", info.MemoryMetric.UsagePercent)
		}
		fmt.Printf("  NVMe设备: %d (正常:%d, 警告:%d, 异常:%d)\n",
			info.TotalNvmeDevices, info.NormalNvmeDevices, info.WarningNvmeDevices, info.CriticalNvmeDevices)
		fmt.Printf("  告警数量: %d\n", len(info.ActiveAlerts))
		fmt.Println()
	}

	// 测试监控功能的完整性
	fmt.Printf("\n=== 功能完整性检查 ===\n")
	fmt.Printf("✓ GPU监控: %s\n", getCheckStatus(len(monitorInfo.GpuMetrics) >= 0))
	fmt.Printf("✓ CPU监控: %s\n", getCheckStatus(monitorInfo.CpuMetric != nil))
	fmt.Printf("✓ 内存监控: %s\n", getCheckStatus(monitorInfo.MemoryMetric != nil))
	fmt.Printf("✓ NVMe设备监控: %s\n", getCheckStatus(len(monitorInfo.NvmeServices) >= 0))
	fmt.Printf("✓ 整体状态判断: %s\n", getCheckStatus(monitorInfo.MonitorStatus != ""))
	fmt.Printf("✓ 告警机制: %s\n", getCheckStatus(monitorInfo.ActiveAlerts != nil))

	fmt.Println("\n测试完成!")
}

// getCheckStatus 获取检查状态的显示文本
func getCheckStatus(success bool) string {
	if success {
		return "通过"
	}
	return "失败"
}

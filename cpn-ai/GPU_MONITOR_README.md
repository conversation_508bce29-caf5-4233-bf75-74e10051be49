# GPU监控告警功能

## 功能概述

本功能在虚拟机列表查询中集成了GPU监控告警信息，基于Prometheus和DCGM-exporter实现GPU状态监控和告警判断。

## 主要特性

### 1. GPU指标监控
- **温度监控**: 实时监控GPU温度，支持高温告警
- **显存监控**: 监控显存使用情况和使用率
- **利用率监控**: 监控GPU计算利用率
- **功耗监控**: 监控GPU功耗状态
- **错误监控**: 监控ECC错误和XID错误

### 2. 告警级别
- **normal**: 正常状态
- **warning**: 警告状态（温度偏高等）
- **critical**: 严重状态（温度过高、硬件错误等）
- **offline**: 离线状态（无法获取监控数据）

### 3. XID错误码解析
支持常见XID错误码的中英文描述，包括：
- XID-8: 页面错误
- XID-43: GPU从总线掉落
- XID-48: 双位ECC错误
- XID-74: GPU内核超时
- 等等...

## API接口

### 1. 虚拟机列表（已增强）
**接口**: `POST /api/virtual/list`

**响应示例**:
```json
{
  "code": 0,
  "msg": "",
  "result": {
    "virtuals": [
      {
        "id": 1,
        "host": "*************",
        "status": 1,
        "status_txt": "有效",
        "monitor_info": {
          "monitor_status": "normal",
          "total_gpus": 8,
          "normal_gpus": 8,
          "warning_gpus": 0,
          "critical_gpus": 0,
          "offline_gpus": 0,
          "gpu_metrics": [
            {
              "gpu_id": "0",
              "temperature": 45.0,
              "memory_usage_percent": 85.2,
              "utilization": 95.0,
              "power_usage": 250.5,
              "status": "normal",
              "status_description": "运行正常",
              "last_update": "2024-01-01T12:00:00Z"
            }
          ],
          "active_alerts": [],
          "last_check_time": "2024-01-01T12:00:00Z"
        }
      }
    ],
    "total": 1
  }
}
```

### 2. 获取单个虚拟机GPU监控信息
**接口**: `POST /api/virtual/gpu-monitor-info`

**请求参数**:
```json
{
  "virtual_id": 1
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "获取成功",
  "result": {
    "monitor_info": {
      "monitor_status": "warning",
      "total_gpus": 8,
      "normal_gpus": 6,
      "warning_gpus": 2,
      "critical_gpus": 0,
      "offline_gpus": 0,
      "gpu_metrics": [
        {
          "gpu_id": "0",
          "temperature": 82.0,
          "memory_used": 10240,
          "memory_total": 12288,
          "memory_usage_percent": 83.3,
          "utilization": 95.0,
          "power_usage": 280.5,
          "ecc_errors": 0,
          "xid_errors": 0,
          "status": "warning",
          "status_description": "温度偏高(82.0°C)",
          "last_update": "2024-01-01T12:00:00Z"
        }
      ],
      "active_alerts": [
        "GPU 0: 温度偏高(82.0°C)",
        "GPU 3: 温度偏高(81.5°C)"
      ],
      "last_check_time": "2024-01-01T12:00:00Z"
    }
  }
}
```

## 告警规则

### 温度告警
- **警告阈值**: > 80°C
- **严重阈值**: > 85°C

### 硬件错误告警
- **ECC错误**: > 0 (严重)
- **XID错误**: > 0 (严重)

## 技术实现

### 1. 服务架构
```
虚拟机列表查询 -> 并行执行 -> [数据库查询] + [Prometheus查询GPU指标]
                           ↓
                    数据整合 -> 返回带监控信息的虚拟机列表
```

### 2. 核心组件
- **GpuMonitorService**: GPU监控服务
- **VirtualMonitorInfo**: 虚拟机监控信息结构
- **GpuMetric**: GPU指标数据结构

### 3. Prometheus查询指标
- `DCGM_FI_DEV_GPU_TEMP`: GPU温度
- `DCGM_FI_DEV_FB_USED`: 显存已用
- `DCGM_FI_DEV_FB_FREE`: 显存空闲
- `DCGM_FI_DEV_GPU_UTIL`: GPU利用率
- `DCGM_FI_DEV_POWER_USAGE`: 功耗
- `DCGM_FI_DEV_XID_ERRORS`: XID错误

## 配置说明

系统复用现有的Prometheus配置：
```ini
[server]
PrometheusUrl = http://*************:9090
```

## 性能优化

1. **批量查询**: 使用并发goroutine批量获取多台服务器的监控信息
2. **超时控制**: 设置10秒查询超时，避免长时间等待
3. **错误处理**: 单台服务器查询失败不影响其他服务器
4. **条件查询**: 仅为状态为"有效"的虚拟机获取监控信息

## 测试方法

运行测试程序：
```bash
cd cpn-ai
go run test/gpu_monitor_test.go
```

## 注意事项

1. 确保Prometheus服务正常运行
2. 确保DCGM-exporter正确配置并采集GPU指标
3. 监控信息获取失败时会标记为离线状态，不影响虚拟机列表的正常返回
4. 建议在生产环境中适当调整查询超时时间和并发数量 
package model

import (
	"errors"
	"gorm.io/gorm"
)

type ShareModel struct {
	ID            int64  `json:"id" gorm:"primary_key"`
	FileSize      int64  `json:"file_size" gorm:"column:file_size"`
	OriginalPath  string `json:"original_path" gorm:"column:original_path"`
	ContainerPath string `json:"container_path" gorm:"column:container_path"`
	SHA256        string `json:"sha256" gorm:"column:sha256"`
	HeaderHash    string `json:"header_hash" gorm:"column:header_hash"`
	MiddleHash    string `json:"middle_hash" gorm:"column:middle_hash"`
	TailHash      string `json:"tail_hash" gorm:"column:tail_hash"`
}

// TableName 返回数据库表名
func (*ShareModel) TableName() string {
	return "T_ShareModel"
}

// GetById 根据ID查询记录
func (s *ShareModel) GetById(tx *gorm.DB, id int64) error {
	return GetTX(tx).First(s, id).Error
}

// ListBySha256In 根据sha256进行in操作查询
func (s *ShareModel) ListBySha256In(tx *gorm.DB, sha256 []string) ([]ShareModel, error) {
	var list []ShareModel
	err := GetTX(tx).Where("sha256 in (?)", sha256).Find(&list).Error
	return list, err
}

// GetByHash 根据header_hash、middle_hash、tail_hash查询
func (s *ShareModel) GetByHash(tx *gorm.DB, headerHash, middleHash, tailHash string) error {
	err := GetTX(tx).Where("header_hash = ? and middle_hash = ? and tail_hash = ?", headerHash, middleHash, tailHash).First(s).Error
	if errors.Is(gorm.ErrRecordNotFound, err) {
		return nil
	}
	return err
}

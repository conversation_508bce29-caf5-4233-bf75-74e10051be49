package model

import (
	"gorm.io/gorm"
	"time"
)

type TrainJobsStatusEnum int

const (
	TrainJobsStatusReady     TrainJobsStatusEnum = 0
	TrainJobsStatusQueue                         = 1
	TrainJobsStatusTraining                      = 2
	TrainJobsStatusTrainSuc                      = 3
	TrainJobsStatusTrainFail                     = 4
)

func TrainJobsStatusName(i TrainJobsStatusEnum) string {
	switch i {
	case TrainJobsStatusReady:
		return "准备中"
	case TrainJobsStatusQueue:
		return "排队中"
	case TrainJobsStatusTraining:
		return "训练中"
	case TrainJobsStatusTrainSuc:
		return "训练成功"
	case TrainJobsStatusTrainFail:
		return "训练失败" //预留
	}
	return ""
}

type TrainJobs struct {
	gorm.Model
	Uuid        string              `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:训练集字符串ID;uniqueIndex"`
	UserId      uint                `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID;index"`
	ImagesUuid  string              `json:"images_uuid" gorm:"type:varchar(50);not null;default:'';comment:原始图集字符串ID;index"`
	TagsUuid    string              `json:"tags_uuid" gorm:"type:varchar(50);not null;default:'';comment:打标数据集字符串ID;index"`
	Title       string              `json:"title" gorm:"type:varchar(50);not null;default:'';comment:文本"`
	Level       string              `json:"level" gorm:"type:varchar(50);not null;default:'';comment:任务等级"`
	TrainParams string              `json:"train_params" gorm:"type:json;comment:训练参数"`
	TriggerWord string              `json:"trigger_word" gorm:"type:varchar(150);not null;default:'';comment:模型触发词"`
	LoraName    string              `json:"lora_name" gorm:"type:varchar(150);not null;default:'';comment:lora名称"`
	Count       int                 `json:"count" gorm:"type:integer;not null;default:0;comment:图片集数量"`
	TotalSteps  int                 `json:"total_steps" gorm:"type:integer;not null;default:0;comment:训练总步数"`
	Remark      string              `json:"remark" gorm:"type:varchar(150);not null;default:'';comment:备注"`
	PushAt      time.Time           `json:"push_at" gorm:"type:datetime;default:'1900-01-01';comment:推送队列时间"`
	StartAt     time.Time           `json:"start_at" gorm:"type:datetime;default:'1900-01-01';comment:训练开始时间"`
	EndAt       time.Time           `json:"end_at" gorm:"type:datetime;default:'1900-01-01';comment:训练结束时间"`
	LastUseAt   time.Time           `json:"last_use_at" gorm:"type:datetime;default:'1900-01-01';comment:最后使用时间"`
	Reason      string              `json:"reason" gorm:"type:varchar(1500);not null;default:'';comment:原因"`
	Status      TrainJobsStatusEnum `json:"status" gorm:"type:tinyint;not null;default:0;comment:状态 1排队中 2训练中 3训练成功 4训练失败"`
	QTaskId     string              `json:"q_task_id" gorm:"type:varchar(50);not null;default:'';comment:训练队列ID;index"`
}

func (TrainJobs) TableName() string {
	return "T_TrainJobs"
}

func (o *TrainJobs) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *TrainJobs) GetByUuid(uuid string) error {
	err := DB.Debug().First(o, "uuid = ?", uuid).Error
	return err
}

func (o *TrainJobs) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["user_id"]; ok {
			tx.Where("user_id=?", queryParm["user_id"])
		}
		if _, ok := queryParm["tags_uuid"]; ok {
			tx.Where("uuid=?", queryParm["tags_uuid"])
		}
		if _, ok := queryParm["images_uuid"]; ok {
			tx.Where("images_uuid=?", queryParm["images_uuid"])
		}
		if _, ok := queryParm["out_trade_no"]; ok {
			tx.Where("out_trade_no=?", queryParm["out_trade_no"])
		}
		if _, ok := queryParm["business_type"]; ok {
			tx.Where("business_type=?", queryParm["business_type"])
		}
		if _, ok := queryParm["order_id"]; ok {
			tx.Where("order_id=?", queryParm["order_id"])
		}
		if _, ok := queryParm["pay_status"]; ok {
			tx.Where("pay_status=?", queryParm["pay_status"])
		}
		tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Scan(dest)
	return total, tx.Error
}

func (o *TrainJobs) SetStatus(status TrainJobsStatusEnum) error {
	if status == TrainJobsStatusQueue {
		return DB.Model(o).Updates(map[string]interface{}{"status": status, "push_at": time.Now()}).Error
	}
	return DB.Model(o).Updates(map[string]interface{}{"status": status}).Error
}

func (o *TrainJobs) SetCompleted(startAt time.Time, endAt time.Time, reason string) error {
	status := o.Status
	if reason == "" {
		status = TrainTagsStatusTagSuc
	} else {
		status = TrainTagsStatusTagFail
	}
	return DB.Model(o).Updates(map[string]interface{}{"start_at": startAt, "end_at": endAt, "status": status, "reason": reason}).Error
}

func (o *TrainJobs) Delete() error {
	return DB.Debug().Delete(o).Error
}

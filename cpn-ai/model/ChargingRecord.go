package model

import (
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"time"
)

type ChargingRecord struct {
	gorm.Model
	UserId          uint            `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	BusinessType    int             `json:"business_type" gorm:"type:int;not null;default:0;comment:业务类型"`
	BusinessId      uint            `json:"business_id" gorm:"type:bigint;not null;default:0;comment:业务ID"`
	ChargingType    int             `json:"charging_type" gorm:"type:int;not null;default:0;comment:计费类型"`
	ChargingNum     int             `json:"charging_num" gorm:"type:int;not null;default:0;comment:计费数量 包天多少天 包月几个月"`
	StartTime       time.Time       `json:"start_time" gorm:"type:datetime;default:'1900-01-01';comment:开始时间"`
	EndTime         time.Time       `json:"end_time" gorm:"type:datetime;default:'1900-01-01';comment:结束时间"`    //包日包月的结束时间，续费时以这个时间往上累加
	SettleTime      time.Time       `json:"settle_time" gorm:"type:datetime;default:'1900-01-01';comment:结算时间"` //同Instance的LastSettleTime
	Amount          decimal.Decimal `json:"amount" gorm:"type:decimal(16, 8);not null;default:0;comment:结算金额"`
	RefundAmount    decimal.Decimal `json:"refund_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:返还金额"`
	IsRefund        bool            `json:"is_refund" gorm:"type:tinyint;not null;default:0;comment:是返还金额"`
	ElapsedSeconds  uint            `json:"elapsed_seconds" gorm:"type:bigint;not null;default:0;comment:间隔时间 单位秒"`
	SettleStartTime time.Time       `json:"settle_start_time" gorm:"type:datetime;default:'1900-01-01';comment:结算开始时间"` //同Instance的LastSettleTime
	OrderNo         string          `json:"order_no" gorm:"type:varchar(50);not null;default:'';comment:扣费订单编号"`
	Remark          string          `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注信息"`
	Status          int             `json:"status" gorm:"type:int;not null;default:0;comment:状态 1业务运行中 2业务已结束"`
}

func (ChargingRecord) TableName() string {
	return "T_ChargingRecord"
}

//func (o *ChargingRecord) Save() error {
//	return DB.Debug().Save(o).Error
//}

/*
func (o *ChargingRecord) New(inst *Instance) error {
	return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		orderType := enums.OrderTypeEnum.CostINST
		chargingRecord := ChargingRecord{
			UserId:       inst.UserId,
			BusinessType: 1,
			BusinessId:   inst.ID,
			ChargingType: inst.ChargingType,
			ChargingNum:  inst.ChargingNum,
			StartTime:    inst.StartTime,
			EndTime:      inst.EndTime,
			SettleTime:   inst.LastSettleTime,
			Amount:       inst.LastSettleAmount,
		}
		if o.IsRefund {
			orderType = enums.OrderTypeEnum.Refund
			chargingRecord.RefundAmount = o.RefundAmount //返还金额
			chargingRecord.IsRefund = o.IsRefund
		}
		if tmpOrderNo, err := OrderNo.NewByOrderType(orderType, int64(0)); err != nil {
			logger.Error(err)
			return err
		} else {
			if tmpOrderNo == "" {
				er := errors.New("orderNo 为空")
				logger.Error(er)
				return er
			}
			chargingRecord.OrderNo = tmpOrderNo
		}

		if err := tx.Debug().Save(&chargingRecord).Error; err != nil {
			return err
		}

		var balance AmountBalance

		if o.IsRefund {
			if o.RefundAmount.GreaterThan(decimal.Zero) {
				if err := balance.GetBalanceObject(chargingRecord.OrderNo, 0, inst.UserId, enums.OrderTypeEnum.Refund, chargingRecord.RefundAmount, "容器实例ID："+inst.Uuid, fmt.Sprintf("inst:%s", inst.Uuid), inst.UserId, "用户ID"); err != nil {
					logger.Error(err)
					return err
				}
				if err := balance.New(tx); err != nil {
					logger.Error(err)
					return err
				}
			}
		} else {
			if err := balance.GetBalanceObject(chargingRecord.OrderNo, inst.UserId, enums.OrderTypeEnum.CostINST, chargingRecord.Amount.Neg(), "容器实例ID："+inst.Uuid, fmt.Sprintf("inst:%s", inst.Uuid), inst.UserId, "用户ID"); err != nil {
				logger.Error(err)
				return err
			}
			if err := balance.New(tx); err != nil {
				logger.Error(err)
				return err
			}
		}

		return nil
	})
}*/

func (o *ChargingRecord) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *ChargingRecord) List(dest interface{}, id uint, userId uint, category int, status int, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if id > 0 {
		tx.Where("id=?", id)
	}
	if userId > 0 {
		tx.Where("user_id=?", userId)
	}
	if category > 0 {
		tx.Where("category=?", category)
	}
	if status >= 0 {
		tx.Where("status=?", status)
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

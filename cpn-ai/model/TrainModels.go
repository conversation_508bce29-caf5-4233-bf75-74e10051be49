package model

import (
	"gorm.io/gorm"
	"time"
)

type TrainModelsStatusEnum int

const (
	TrainModelsStatusUnVisible TrainModelsStatusEnum = 0
	TrainModelsStatusActive                          = 1
	TrainModelsStatusUnActive                        = 2
)

func TrainModelsStatusName(i TrainModelsStatusEnum) string {
	switch i {
	case TrainModelsStatusUnVisible:
		return "不可见"
	case TrainModelsStatusActive:
		return "可用"
	case TrainModelsStatusUnActive:
		return "不可用"
	}
	return ""
}

type TrainModels struct {
	gorm.Model
	Uuid      string                `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID;uniqueIndex"`
	CatId     uint                  `json:"cat_id" gorm:"type:bigint;not null;default:0;comment:所属类型ID"`
	Title     string                `json:"title" gorm:"type:varchar(50);not null;default:'';comment:展示标题"`
	Name      string                `json:"name" gorm:"type:varchar(100);not null;default:'';comment:唯一名称"`
	Path      string                `json:"path" gorm:"type:varchar(150);not null;default:'';comment:模型路径"`
	Param     string                `json:"param" gorm:"type:json;comment:训练默认参数"`
	Index     int                   `json:"index" gorm:"type:integer;not null;default:0;comment:排序"`
	UseCount  int                   `json:"use_count" gorm:"type:integer;not null;default:0;comment:使用次数"`
	LastUseAt time.Time             `json:"last_use_at" gorm:"type:datetime;default:'1900-01-01';comment:最后使用时间"`
	Remark    string                `json:"remark" gorm:"type:varchar(150);not null;default:'';comment:备注"`
	Status    TrainModelsStatusEnum `json:"status" gorm:"type:tinyint;not null;default:0;comment:状态 0不显示 1有效 2暂时不可用"`
}

func (TrainModels) TableName() string {
	return "T_TrainModels"
}

func (o *TrainModels) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *TrainModels) GetByUuid(uuid string) error {
	err := DB.Debug().First(o, "uuid = ?", uuid).Error
	return err
}
func (o *TrainModels) GetByName(name string) error {
	err := DB.Debug().First(o, "name = ?", name).Error
	return err
}

func (o *TrainModels) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["user_id"]; ok {
			tx.Where("user_id=?", queryParm["user_id"])
		}
		if _, ok := queryParm["order_no"]; ok {
			tx.Where("order_no=?", queryParm["order_no"])
		}
		if _, ok := queryParm["out_trade_no"]; ok {
			tx.Where("out_trade_no=?", queryParm["out_trade_no"])
		}
		if _, ok := queryParm["business_type"]; ok {
			tx.Where("business_type=?", queryParm["business_type"])
		}
		if _, ok := queryParm["order_id"]; ok {
			tx.Where("order_id=?", queryParm["order_id"])
		}
		tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Scan(dest)
	return total, tx.Error
}

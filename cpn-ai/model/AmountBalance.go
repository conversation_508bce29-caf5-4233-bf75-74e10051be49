package model

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"time"
)

type AmountBalance struct {
	gorm.Model
	CardId         uint            `json:"card_id" gorm:"type:bigint;not null;default:0;comment:扣款CardID"`
	UserId         uint            `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID;index"`
	OrderType      int             `json:"order_type" gorm:"type:int;not null;default:0;comment:业务类型;index"`
	OrderNo        string          `json:"order_no" gorm:"type:varchar(50);not null;default:'';comment:业务单号;index"`
	BeforeOccurred decimal.Decimal `json:"before_occurred" gorm:"type:decimal(16, 8);not null;default:0;comment:变动前额度"`
	OccurredAmount decimal.Decimal `json:"occurred_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:发生额"`
	AfterOccurred  decimal.Decimal `json:"after_occurred" gorm:"type:decimal(16, 8);not null;default:0;comment:变动后额度"`
	Show           string          `json:"show" gorm:"type:varchar(50);not null;default:'';comment:前端显示文本"`
	InstanceId     uint            `json:"instance_id" gorm:"type:bigint;not null;default:0;comment:实例ID"`
	StartupMark    string          `json:"startup_mark" gorm:"type:varchar(50);not null;default:'';comment:启动标记"`
	PodId          uint            `json:"pod_id" gorm:"type:bigint;not null;default:0;comment:PodID"`
	PodUserId      uint            `json:"pod_user_id" gorm:"type:bigint;not null;default:0;comment:Pod开发者ID;index"`
	PodEarn        decimal.Decimal `json:"pod_earn" gorm:"type:decimal(16, 8);not null;default:0;comment:Pod赚取佣金"`
	PodCalcAt      time.Time       `json:"pod_calc_at" gorm:"type:datetime;default:'1900-01-01';comment:Pod佣金计算时间"`
	Remark         string          `json:"remark" gorm:"type:varchar(250);not null;default:'';comment:备注"`
	Operator       string          `json:"operator" gorm:"type:varchar(50);not null;default:'';comment:操作者"`
	OperatorId     uint            `json:"operator_id" gorm:"type:bigint;not null;default:0;comment:操作者ID"`
	//OrderInfo      string          `json:"order_info" gorm:"type:json;comment:订单信息"`
	OrderInfo string `json:"OrderInfo" gorm:"type:text;comment:订单信息"`
}

func (AmountBalance) TableName() string {
	return "T_AmountBalance"
}

func (o *AmountBalance) List(dest interface{}, userId uint, kw string, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o).Unscoped().Where("user_id=?", userId)
	if kw != "" {
		tx.Where("remark like ?", "%"+kw+"%")
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *AmountBalance) ListPro(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["user_id"]; ok {
			tx.Where("user_id=?", queryParm["user_id"])
		}
		if _, ok := queryParm["card_id"]; ok {
			tx.Where("card_id=?", queryParm["card_id"])
		}
		if _, ok := queryParm["pod_id"]; ok {
			tx.Where("pod_id=?", queryParm["pod_id"])
		}
		if _, ok := queryParm["instance_id"]; ok {
			tx.Where("instance_id=?", queryParm["instance_id"])
		}
		if _, ok := queryParm["order_no"]; ok {
			tx.Where("order_no=?", queryParm["order_no"])
		}
		if _, ok := queryParm["order_type"]; ok {
			tx.Where("order_type=?", queryParm["order_type"])
		}
		if _, ok := queryParm["pod_user_id"]; ok {
			tx.Where("pod_user_id=?", queryParm["pod_user_id"])
		}
		if _, ok := queryParm["created_start"]; ok {
			tx.Where("created_at>=?", queryParm["created_start"])
		}
		if _, ok := queryParm["created_end"]; ok {
			tx.Where("created_at<?", queryParm["created_end"])
		}
		if _, ok := queryParm["kw"]; ok {
			tx.Where("remark like ?", "%"+queryParm["kw"].(string)+"%")
		}

		if page == 1 {
			if err := tx.Count(&total).Error; err != nil {
				return 0, err
			}
		}
		tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	}
	if dest != nil {
		tx.Scan(dest)
	}
	return total, tx.Error
}

func (o *AmountBalance) ListForCheckBalance(dest interface{}, userId uint, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o).Where("user_id=?", userId)
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id asc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *AmountBalance) ListForCalcPodReward(dest interface{}, lastAutoId uint, pageSize int) error {
	tx := DB.Debug().Model(o)
	tx.Where("id>? and order_type=?", lastAutoId, enums.OrderTypeEnum.CostPod)
	tx.Order("id asc").Limit(pageSize).Scan(dest)
	return tx.Error
}

func (o *AmountBalance) GetLastCalcPodReward() error {
	return DB.Debug().Where("order_type=? and pod_calc_at>?", enums.OrderTypeEnum.CostPod, common.NationalDay).Order("id desc").First(o).Error
}

func (o *AmountBalance) ListPodUserId(dest interface{}, statAt time.Time, statEndAt time.Time) (decimal.Decimal, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := time.Date(statEndAt.Year(), statEndAt.Month(), statEndAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd = statEnd.AddDate(0, 0, 1)
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(o).
		Select("distinct pod_user_id").
		Where("order_type=?", enums.OrderTypeEnum.CostPod).
		Where("create_at>=?", statStart)
	if statEndAt.After(common.NationalDay) {
		tx.Where("create_at<?", statEnd)
	}
	tx.Order("pod_user_id asc")
	tx.Scan(&totalAmount)
	if tx.Error != nil {
		return decimal.Zero, tx.Error
	}
	return totalAmount.Neg(), nil
}

func (o *AmountBalance) StatPodRewardOfCurrentMonth(dest interface{}, podUserId uint, statAt time.Time, page int, pageSize int) error {
	statStart := time.Date(statAt.Year(), statAt.Month(), 1, 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 1, 0)
	return o.StatPodReward(dest, podUserId, statStart, statEnd, page, pageSize)
}

func (o *AmountBalance) StatPodReward(dest interface{}, podUserId uint, statAt time.Time, statEndAt time.Time, page int, pageSize int) error {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := time.Date(statEndAt.Year(), statEndAt.Month(), statEndAt.Day(), 0, 0, 0, 0, time.Now().Location())
	tx := DB.Debug().Model(o).
		Select("pod_user_id,sum(pod_earn) as pod_user_earn").
		Where("order_type=?", enums.OrderTypeEnum.CostPod)
	if podUserId > 0 {
		tx.Where("pod_user_id=?", podUserId)
	}
	tx.Where("created_at>=?", statStart)
	if statEndAt.After(common.NationalDay) {
		tx.Where("created_at<?", statEnd)
	}
	if podUserId == 0 {
		tx.Group("pod_user_id")
	}
	tx.Order("pod_user_id asc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	if tx.Error != nil {
		return tx.Error
	}
	return nil
}

func (o *AmountBalance) CheckPodReward(statAt time.Time) error {
	statStart := time.Date(statAt.Year(), statAt.Month(), 1, 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 1, 0)

	return DB.Debug().Where("order_type=? and created_at>=? and created_at<? and pod_calc_at<?", enums.OrderTypeEnum.CostPod, statStart, statEnd, common.NationalDay).First(o).Error
}

func (o *AmountBalance) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *AmountBalance) GetByOrderNo(orderNo string) error {
	return DB.First(o, "order_no=?", orderNo).Error
}

func (o *AmountBalance) ExistsOrderNo(orderNo string) (bool, error) {
	err := DB.First(o, "order_no=?", orderNo).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return false, nil // 记录不存在
	}
	if err != nil {
		return false, err // 出现其他错误
	}
	return true, nil // 记录存在
}

func (balance *AmountBalance) New(tx *gorm.DB) error {

	if err := balance.checkBalance(); err != nil {
		logger.Error(err)
		return err
	}

	if balance.CardId > 0 {
		var card Card
		if err := tx.First(&card, balance.CardId).Error; err != nil || card.ID == 0 {
			logger.Error(err, " orderNo:", balance.OrderNo, " balance.OccurredAmount:", balance.OccurredAmount, " cardID:", card.ID)
			return err
		}

		after := card.LeaveAmount.Add(balance.OccurredAmount)
		balance.BeforeOccurred = card.LeaveAmount
		balance.AfterOccurred = after

		if err := tx.Save(balance).Error; err != nil {
			logger.Error(err)
			return err
		}
		if balance.ID == 0 {
			logger.Error("生成流水记录失败")
			return errors.New("生成流水记录失败")
		}

		result := tx.Model(&card).Updates(map[string]interface{}{"leave_amount": after})
		if err := result.Error; err != nil {
			logger.Error(err)
			return err
		}

		if result.RowsAffected != 1 {
			err := errors.New(fmt.Sprintf("更新卡leaveAmount失败 cardId:%d", card.ID))
			logger.Error(err)
			return err
		}
	} else {
		var user User
		if err := tx.First(&user, balance.UserId).Error; err != nil || user.ID == 0 {
			logger.Error(err, " orderNo:", balance.OrderNo, " balance.OccurredAmount:", balance.OccurredAmount, " userID:", user.ID)
			return err
		}

		after := user.Amount.Add(balance.OccurredAmount)
		balance.BeforeOccurred = user.Amount
		balance.AfterOccurred = after
		//if balance.OrderInfo == "" {
		//	balance.OrderInfo = "{}"
		//}
		if err := tx.Save(balance).Error; err != nil {
			logger.Error(err)
			return err
		}

		result := tx.Model(&user).Updates(map[string]interface{}{"amount": after})
		if err := result.Error; err != nil {
			logger.Error(err)
			return err
		}

		if result.RowsAffected != 1 {
			err := errors.New(fmt.Sprintf("更新用户amount失败 userId:%d", user.ID))
			logger.Error(err)
			return err
		}
	}

	return nil
}

func (balance *AmountBalance) GetBalanceObject(orderNo string, cardId uint, userId uint, orderType int, occurred decimal.Decimal, show string, remark string, operatorId uint, operator string) error {
	balance.OrderNo = orderNo
	balance.CardId = cardId
	balance.UserId = userId
	balance.OrderType = orderType
	balance.Show = show
	balance.Remark = remark
	balance.Operator = operator
	balance.OperatorId = operatorId
	balance.OccurredAmount = occurred
	err := balance.checkBalance()
	if err != nil {
		return err
	}
	return nil
}

func (balance *AmountBalance) checkBalance() error {
	if len(balance.OrderNo) == 0 {
		return errors.New("单号不正确")
	}

	if balance.UserId == 0 {
		return errors.New("未找到用户")
	}
	if len(balance.OrderNo) == 0 {
		return errors.New("业务单号为空")
	}
	if balance.OccurredAmount == decimal.NewFromInt(0) {
		return errors.New("没有发生金额")
	}
	if balance.OrderType == 0 {
		return errors.New("没有业务类型")
	}

	if balance.CardId > 0 {
		if balance.OccurredAmount.GreaterThanOrEqual(decimal.Zero) {
			return errors.New("算力卡只支持扣款")
		}
	}
	if balance.OrderType == enums.OrderTypeEnum.ManagerAdd || balance.OrderType == enums.OrderTypeEnum.RechargeBuy || balance.OrderType == enums.OrderTypeEnum.UserGift || balance.OrderType == enums.OrderTypeEnum.Subscribe || balance.OrderType == enums.OrderTypeEnum.Refund {
		//if balance.OccurredAmount.Cmp(decimal.NewFromInt(0)) == -1 {
		//	return errors.New("金额与业务类型不匹配")
		//}
		if balance.OccurredAmount.LessThanOrEqual(decimal.Zero) {
			return errors.New("金额与业务类型不匹配")
		}
	}
	if balance.OrderType == enums.OrderTypeEnum.Cost || balance.OrderType == enums.OrderTypeEnum.CostLLM || balance.OrderType == enums.OrderTypeEnum.CostINST || balance.OrderType == enums.OrderTypeEnum.ManagerCost {
		//if balance.OccurredAmount.Cmp(decimal.NewFromInt(0)) == 1 {
		//	return errors.New("金额与业务类型不匹配")
		//}
		if balance.OccurredAmount.GreaterThanOrEqual(decimal.Zero) {
			return errors.New("金额与业务类型不匹配")
		}
		if balance.OccurredAmount.LessThanOrEqual(decimal.NewFromInt(-1000)) {
			return errors.New("扣款金额大于1000")
		}
	}
	return nil
}

func (o *AmountBalance) StatsUserAmountCost(userId uint, rewardMonth time.Time) (decimal.Decimal, error) {
	var totalAmount decimal.Decimal

	firstOfMonth := time.Date(rewardMonth.Year(), rewardMonth.Month(), 1, 0, 0, 0, 0, rewardMonth.Location())
	lastOfMonth := firstOfMonth.AddDate(0, 1, 0)

	tx := DB.Debug().Model(o).
		Select("IFNULL(sum(occurred_amount), 0) as total_occurred").
		//Select("sum(occurred_amount) as total_occurred").
		Where("user_id = ?", userId).
		Where("card_id=0").
		Where("occurred_amount<0").
		Where("created_at >=? and created_at <?", firstOfMonth, lastOfMonth).
		Scan(&totalAmount)
	if tx.Error != nil {
		return decimal.Zero, tx.Error
	}

	return totalAmount, tx.Error
}

func (o *AmountBalance) UpdatePodReward(podUserId uint, podEarn decimal.Decimal, orderInfo string) error {
	return DB.Model(o).Updates(map[string]interface{}{"pod_user_id": podUserId, "pod_earn": podEarn, "order_info": orderInfo, "pod_calc_at": time.Now()}).Error
}

package model

import (
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"cpn-ai/structs"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"sort"
	"time"
)

type SettleRecord struct {
	gorm.Model
	UserId          uint            `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	BusinessType    int             `json:"business_type" gorm:"type:int;not null;default:0;comment:业务类型"`
	BusinessId      uint            `json:"business_id" gorm:"type:bigint;not null;default:0;comment:业务ID"`
	StartupMark     string          `json:"startup_mark" gorm:"type:varchar(50);not null;default:'';comment:启动标记"`
	PodId           uint            `json:"pod_id" gorm:"type:bigint;not null;default:0;comment:PodId"`
	ImageId         uint            `json:"image_id" gorm:"type:bigint;not null;default:0;comment:ImageId"`
	GpuModelId      uint            `json:"gpu_model_id" gorm:"type:bigint;not null;default:0;comment:显卡型号ID"`
	Gpus            int             `json:"gpus" gorm:"type:int;not null;default:0;comment:GPU数量"`
	ChargingType    int             `json:"charging_type" gorm:"type:int;not null;default:0;comment:计费类型"`
	ChargingNum     int             `json:"charging_num" gorm:"type:int;not null;default:0;comment:计费数量 包天多少天 包月几个月"`
	UnitPrice       decimal.Decimal `json:"unit_price" gorm:"type:decimal(16, 8);not null;default:0;comment:结算单价"`
	SettleReason    int             `json:"settle_reason" gorm:"type:int;not null;default:0;comment:结算原因"`
	SettleStartTime time.Time       `json:"settle_start_time" gorm:"type:datetime;default:'1900-01-01';comment:结算开始时间"`
	SettleEndTime   time.Time       `json:"settle_end_time" gorm:"type:datetime;default:'1900-01-01';comment:结算结束时间"` //可以理解为支付了这笔金额的截止时间
	Amount          decimal.Decimal `json:"amount" gorm:"type:decimal(16, 8);not null;default:0;comment:结算金额"`
	RefundAmount    decimal.Decimal `json:"refund_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:返还金额"`
	ElapsedSeconds  uint            `json:"elapsed_seconds" gorm:"type:bigint;not null;default:0;comment:间隔时间 单位秒"`
	IsRefund        bool            `json:"is_refund" gorm:"type:tinyint;not null;default:0;comment:是返还金额"`
	OrderNo         string          `json:"order_no" gorm:"type:varchar(50);not null;default:'';comment:扣费订单编号"`
	Remark          string          `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注信息"`
	Status          int             `json:"status" gorm:"type:int;not null;default:0;comment:未启用"`
}

//续费 SettleStartTime为

func (SettleRecord) TableName() string {
	return "T_SettleRecord"
}

//func (o *ChargingRecord) Save() error {
//	return DB.Debug().Save(o).Error
//}

func (o *SettleRecord) New(tx *gorm.DB, inst *Instance, businessType int, settleReason int, settleStartTime time.Time, settleEndTime time.Time, amount decimal.Decimal) error {
	pre := "实例结算" + inst.Uuid + " "
	//return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
	orderType := enums.OrderTypeEnum.CostINST

	unitPrice := decimal.Zero
	if businessType == enums.BusinessTypeEnum.PodInstance {
		var gpuModel GpuModel
		if err := gpuModel.GetById(inst.GpuModelId); err != nil {
			logger.Error(err)
			return err
		}
		unitPrice = gpuModel.UnitPrice(inst.ChargingType)
	} else if businessType == enums.BusinessTypeEnum.PodService {
		var pod Pod
		if err := pod.GetById(inst.PodId); err != nil {
			logger.Error(err)
			return err
		}
		unitPrice = pod.UnitPrice(enums.ChargingTypeEnum.Usage) //目前只有按量计费
	}

	imageId := inst.ImageId
	if inst.StartupImageId > 0 {
		imageId = inst.StartupImageId
	}
	settleRecord := SettleRecord{
		UserId:          inst.UserId,
		BusinessId:      inst.ID,
		StartupMark:     inst.StartupMark,
		ChargingType:    inst.ChargingType,
		ChargingNum:     inst.ChargingNum,
		PodId:           inst.PodId,
		ImageId:         imageId,
		GpuModelId:      inst.GpuModelId,
		Gpus:            inst.Gpus,
		BusinessType:    businessType,
		SettleReason:    settleReason,
		SettleStartTime: settleStartTime,
		SettleEndTime:   settleEndTime,
		Amount:          amount,
		UnitPrice:       unitPrice,
	}
	if settleRecord.ChargingType == enums.ChargingTypeEnum.Usage {
		seconds := settleEndTime.Sub(settleStartTime).Seconds()
		settleRecord.ElapsedSeconds = uint(seconds)
	}
	if o.IsRefund {
		orderType = enums.OrderTypeEnum.Refund
		settleRecord.RefundAmount = o.RefundAmount //返还金额
		settleRecord.IsRefund = o.IsRefund

		seconds := settleEndTime.Sub(settleStartTime).Seconds()
		settleRecord.ElapsedSeconds = uint(seconds)
	}
	if tmpOrderNo, err := OrderNo.NewByOrderType(orderType, int64(0)); err != nil {
		logger.Error(err)
		return err
	} else {
		if tmpOrderNo == "" {
			er := errors.New("orderNo 为空")
			logger.Error(er)
			return er
		}
		settleRecord.OrderNo = tmpOrderNo
	}

	if err := tx.Debug().Save(&settleRecord).Error; err != nil {
		return err
	}

	if o.IsRefund {
		if o.RefundAmount.GreaterThan(decimal.Zero) {
			show := fmt.Sprintf("转按量返还") + "-" + inst.Uuid
			var balance AmountBalance
			if err := balance.GetBalanceObject(settleRecord.OrderNo, 0, inst.UserId, enums.OrderTypeEnum.Refund, settleRecord.RefundAmount, show, fmt.Sprintf("inst:%s", inst.Uuid), inst.UserId, "用户ID"); err != nil {
				logger.Error(err)
				return err
			}
			balance.InstanceId = inst.ID
			balance.StartupMark = inst.StartupMark
			balance.PodId = inst.PodId
			if err := balance.New(tx); err != nil {
				logger.Error(err)
				return err
			}
		}
	} else {
		show := fmt.Sprintf("%s扣费", enums.ChargingTypeEnum.Name(inst.ChargingType)) + "-" + inst.Uuid
		if businessType == enums.BusinessTypeEnum.PodService {
			show = fmt.Sprintf("Pod服务") + "-" + inst.Uuid

			if inst.UserId > 0 {
				//*从余额扣费
				var balance AmountBalance
				if err := balance.GetBalanceObject(settleRecord.OrderNo, 0, inst.UserId, enums.OrderTypeEnum.CostPod, settleRecord.Amount.Neg(), show, fmt.Sprintf("inst:%s", inst.Uuid), inst.UserId, "用户ID"); err != nil {
					logger.Error(err)
					return err
				}
				balance.InstanceId = inst.ID
				balance.StartupMark = inst.StartupMark
				balance.PodId = inst.PodId
				if err := balance.New(tx); err != nil {
					logger.Error(err)
					return err
				}
			} else {
				cards := make([]structs.RankCard, 0) //从算力卡扣费
				var card Card
				if _, err := card.ListForSettle(&cards, settleRecord.UserId, settleRecord.PodId); err != nil {
					msg := "查询用户可用算力卡失败"
					logger.Error(pre, msg, err)
				} else {
					logger.Info(pre, fmt.Sprintf("检索到%d张算力卡", len(cards)))
					for i := 0; i < len(cards); i++ {
						cards[i].RankScore = cards[i].ExpireDate.Unix()
						if cards[i].PodIds == "" {
							cards[i].RankScore = cards[i].ExpireDate.AddDate(1, 0, 0).Unix()
						}
					}
					sort.Slice(cards, func(i, j int) bool {
						return cards[i].RankScore < cards[j].RankScore
					})
				}

				leaveSettleAmount := settleRecord.Amount
				logger.Info(pre, fmt.Sprintf("需要扣款的金额%s", leaveSettleAmount.String()))
				for _, tmpCard := range cards {
					if tmpCard.Status != 1 {
						logger.Info(pre, fmt.Sprintf("CardId：%d,状态不为1 status:%d", tmpCard.ID, tmpCard.Status))
						continue
					}
					if tmpCard.BindUserId != inst.UserId {
						logger.Info(pre, fmt.Sprintf("CardId：%d,用户不匹配 buyUserId:%d   instUserId:%d", tmpCard.ID, tmpCard.BuyUserId, inst.Uuid))
						continue
					}
					if tmpCard.LeaveAmount.LessThanOrEqual(decimal.Zero) {
						logger.Info(pre, fmt.Sprintf("CardId：%d,卡内可用额度为0 leaveAmount:%s", tmpCard.ID, tmpCard.LeaveAmount.String()))
						continue
					}

					costAmount := leaveSettleAmount
					if tmpCard.LeaveAmount.LessThan(leaveSettleAmount) {
						costAmount = tmpCard.LeaveAmount
					}
					leaveSettleAmount = leaveSettleAmount.Sub(costAmount)

					logger.Info(pre, fmt.Sprintf("CardId：%d,开始扣款 costAmount:%s    settleAmount：%s", tmpCard.ID, costAmount.String(), leaveSettleAmount.String()))
					var balance AmountBalance
					if err := balance.GetBalanceObject(settleRecord.OrderNo, tmpCard.ID, inst.UserId, enums.OrderTypeEnum.CostPod, costAmount.Neg(), show, fmt.Sprintf("inst:%s", inst.Uuid), inst.UserId, "用户ID"); err != nil {
						logger.Error(pre, err)
						return err
					}
					balance.InstanceId = inst.ID
					balance.StartupMark = inst.StartupMark
					balance.PodId = inst.PodId
					if err := balance.New(tx); err != nil {
						logger.Error(pre, err)
						return err
					}
					if leaveSettleAmount.LessThanOrEqual(decimal.Zero) {
						logger.Info(pre, fmt.Sprintf("CardId：%d,结算金额已经全部完成  settleAmount：%s", tmpCard.ID, leaveSettleAmount.String()))
						break
					}

				}
				if leaveSettleAmount.GreaterThan(decimal.Zero) {
					logger.Info(pre, fmt.Sprintf("从账户余额中扣款  settleAmount：%s", leaveSettleAmount.String()))
					var balance AmountBalance
					if err := balance.GetBalanceObject(settleRecord.OrderNo, 0, inst.UserId, enums.OrderTypeEnum.CostPod, leaveSettleAmount.Neg(), show, fmt.Sprintf("inst:%s", inst.Uuid), inst.UserId, "用户ID"); err != nil {
						logger.Error(pre, err)
						return err
					}
					balance.InstanceId = inst.ID
					balance.StartupMark = inst.StartupMark
					balance.PodId = inst.PodId
					if err := balance.New(tx); err != nil {
						logger.Error(pre, err)
						return err
					}
				}
			}

		} else if businessType == enums.BusinessTypeEnum.PodInstance {
			show = fmt.Sprintf("Gpu算力") + "-" + inst.Uuid

			cards := make([]structs.RankCard, 0) //从算力卡扣费 定向Pod不能用于存储扣费
			var card Card
			if _, err := card.ListForSettle(&cards, settleRecord.UserId, settleRecord.PodId); err != nil {
				msg := "查询用户可用算力卡失败"
				logger.Error(pre, msg, err)
			} else {
				logger.Info(pre, fmt.Sprintf("检索到%d张算力卡", len(cards)))
				for i := 0; i < len(cards); i++ {
					cards[i].RankScore = cards[i].ExpireDate.Unix()
					if cards[i].PodIds == "" {
						cards[i].RankScore = cards[i].ExpireDate.AddDate(1, 0, 0).Unix()
					}
				}
				sort.Slice(cards, func(i, j int) bool {
					return cards[i].RankScore < cards[j].RankScore
				})
			}

			leaveSettleAmount := settleRecord.Amount
			logger.Info(pre, fmt.Sprintf("需要扣款的金额%s", leaveSettleAmount.String()))
			for _, tmpCard := range cards {
				if tmpCard.Status != 1 {
					logger.Info(pre, fmt.Sprintf("CardId：%d,状态不为1 status:%d", tmpCard.ID, tmpCard.Status))
					continue
				}
				if tmpCard.BindUserId != inst.UserId {
					logger.Info(pre, fmt.Sprintf("CardId：%d,用户不匹配 buyUserId:%d   instUserId:%d", tmpCard.ID, tmpCard.BuyUserId, inst.Uuid))
					continue
				}
				if tmpCard.LeaveAmount.LessThanOrEqual(decimal.Zero) {
					logger.Info(pre, fmt.Sprintf("CardId：%d,卡内可用额度为0 leaveAmount:%s", tmpCard.ID, tmpCard.LeaveAmount.String()))
					continue
				}

				costAmount := leaveSettleAmount
				if tmpCard.LeaveAmount.LessThan(leaveSettleAmount) {
					costAmount = tmpCard.LeaveAmount
				}
				leaveSettleAmount = leaveSettleAmount.Sub(costAmount)

				logger.Info(pre, fmt.Sprintf("CardId：%d,开始扣款 costAmount:%s    settleAmount：%s", tmpCard.ID, costAmount.String(), leaveSettleAmount.String()))
				var balance AmountBalance
				if err := balance.GetBalanceObject(settleRecord.OrderNo, tmpCard.ID, inst.UserId, enums.OrderTypeEnum.CostINST, costAmount.Neg(), show, fmt.Sprintf("inst:%s", inst.Uuid), inst.UserId, "用户ID"); err != nil {
					logger.Error(pre, err)
					return err
				}
				balance.InstanceId = inst.ID
				balance.StartupMark = inst.StartupMark
				balance.PodId = inst.PodId
				if err := balance.New(tx); err != nil {
					logger.Error(pre, err)
					return err
				}
				if leaveSettleAmount.LessThanOrEqual(decimal.Zero) {
					logger.Info(pre, fmt.Sprintf("CardId：%d,结算金额已经全部完成  settleAmount：%s", tmpCard.ID, leaveSettleAmount.String()))
					break
				}
			}
			if leaveSettleAmount.GreaterThan(decimal.Zero) {
				logger.Info(pre, fmt.Sprintf("从账户余额中扣款  settleAmount：%s", leaveSettleAmount.String()))
				var balance AmountBalance
				if err := balance.GetBalanceObject(settleRecord.OrderNo, 0, inst.UserId, enums.OrderTypeEnum.CostINST, leaveSettleAmount.Neg(), show, fmt.Sprintf("inst:%s", inst.Uuid), inst.UserId, "用户ID"); err != nil {
					logger.Error(pre, err)
					return err
				}
				balance.InstanceId = inst.ID
				balance.StartupMark = inst.StartupMark
				balance.PodId = inst.PodId
				if err := balance.New(tx); err != nil {
					logger.Error(pre, err)
					return err
				}
			}
		} else {
			msg := "未设计的业务类型"
			logger.Error(msg, " businessType:", businessType)
			return errors.New(msg)
		}

	}
	logger.Info(pre, fmt.Sprintf("结算完成"))
	return nil
	//})
}

func (o *SettleRecord) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *SettleRecord) List(dest interface{}, id uint, userId uint, category int, status int, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if id > 0 {
		tx.Where("id=?", id)
	}
	if userId > 0 {
		tx.Where("user_id=?", userId)
	}
	if category > 0 {
		tx.Where("category=?", category)
	}
	if status >= 0 {
		tx.Where("status=?", status)
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

package model

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"gorm.io/gorm"
	"time"
)

type TrainTagsStatusEnum int

const (
	TrainTagsStatusReady   TrainTagsStatusEnum = 0
	TrainTagsStatusQueue                       = 1
	TrainTagsStatusTagging                     = 2
	TrainTagsStatusTagSuc                      = 3
	TrainTagsStatusTagFail                     = 4
)

func TrainTagsStatusName(i TrainTagsStatusEnum) string {
	switch i {
	case TrainTagsStatusReady:
		return "准备中"
	case TrainTagsStatusQueue:
		return "排队中"
	case TrainTagsStatusTagging:
		return "打标中"
	case TrainTagsStatusTagSuc:
		return "打标成功"
	case TrainTagsStatusTagFail:
		return "打标失败" //预留
	}
	return ""
}

type TrainTags struct {
	gorm.Model
	Uuid         string              `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:训练集字符串ID;uniqueIndex"`
	UserId       uint                `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID;index"`
	ImagesUuid   string              `json:"images_uuid" gorm:"type:varchar(50);not null;default:'';comment:原始图集字符串ID;index"`
	Title        string              `json:"title" gorm:"type:varchar(50);not null;default:'';comment:文本"`
	CropMethod   string              `json:"crop_method" gorm:"type:varchar(50);not null;default:'';comment:裁剪方式 center focus none"`
	CropSize     string              `json:"crop_size" gorm:"type:varchar(250);not null;default:'';comment:裁剪尺寸224*224,512*512"`
	TagThreshold float64             `json:"tag_threshold" gorm:"type:float;not null;default:0;comment:打标阈值"`
	TagAlg       string              `json:"tag_alg" gorm:"type:varchar(150);not null;default:'';comment:打标算法"`
	TriggerWord  string              `json:"trigger_word" gorm:"type:varchar(150);not null;default:'';comment:模型触发词"`
	TagParams    string              `json:"tag_params" gorm:"type:json;comment:打标参数"`
	Count        int                 `json:"count" gorm:"type:integer;not null;default:0;comment:图片集数量"`
	Remark       string              `json:"remark" gorm:"type:varchar(150);not null;default:'';comment:备注"`
	PushAt       time.Time           `json:"push_at" gorm:"type:datetime;default:'1900-01-01';comment:推送队列时间"`
	StartAt      time.Time           `json:"start_at" gorm:"type:datetime;default:'1900-01-01';comment:打标开始时间"`
	EndAt        time.Time           `json:"end_at" gorm:"type:datetime;default:'1900-01-01';comment:打标结束时间"`
	LastUseAt    time.Time           `json:"last_use_at" gorm:"type:datetime;default:'1900-01-01';comment:最后使用时间"`
	Reason       string              `json:"reason" gorm:"type:varchar(1500);not null;default:'';comment:原因"`
	Status       TrainTagsStatusEnum `json:"status" gorm:"type:tinyint;not null;default:0;comment:状态 1排队中 2打标中 3打标完成 4打标失败"`
	QTaskId      string              `json:"q_task_id" gorm:"type:varchar(50);not null;default:'';comment:打标队列ID;index"`
}

func (TrainTags) TableName() string {
	return "T_TrainTags"
}

func (o *TrainTags) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *TrainTags) GetByUuid(uuid string) error {
	err := DB.Debug().First(o, "uuid = ?", uuid).Error
	return err
}

func (o *TrainTags) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["user_id"]; ok {
			tx.Where("user_id=?", queryParm["user_id"])
		}
		if _, ok := queryParm["tags_uuid"]; ok {
			tx.Where("uuid=?", queryParm["tags_uuid"])
		}
		if _, ok := queryParm["images_uuid"]; ok {
			tx.Where("images_uuid=?", queryParm["images_uuid"])
		}
		if _, ok := queryParm["out_trade_no"]; ok {
			tx.Where("out_trade_no=?", queryParm["out_trade_no"])
		}
		if _, ok := queryParm["business_type"]; ok {
			tx.Where("business_type=?", queryParm["business_type"])
		}
		if _, ok := queryParm["order_id"]; ok {
			tx.Where("order_id=?", queryParm["order_id"])
		}
		if _, ok := queryParm["pay_status"]; ok {
			tx.Where("pay_status=?", queryParm["pay_status"])
		}
		tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Scan(dest)
	return total, tx.Error
}

func (o *TrainTags) IsTrainning(tagsUuid string) (bool, error) {
	//tx := DB.Debug().Model(&TrainJobs{})
	//tx.Where("tags_uuid=?", tagsUuid)
	//tx.Where(" status in(1,2)")

	if err := DB.Debug().First(&TrainJobs{}, "tags_uuid=? and status in(1,2)", tagsUuid).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil
		}
		return true, err
	} else {
		return true, nil
	}
}

type TrainTagsLoopForClearLogicFunc func(item TrainTags) error

func (o *TrainTags) LoopForClear(outDays int, pageSize int, logicFunc TrainTagsLoopForClearLogicFunc) (int64, error) {
	ary := make([]TrainTags, 0)
	lastCreatedAt := common.NationalDay
	var total int64
	tx := DB.Debug().Model(o)
	tx.Where("created_at>?", lastCreatedAt)
	tx.Where("created_at<?", time.Now().AddDate(0, 0, -outDays))
	tx.Order("created_at asc")
	tx.Limit(pageSize)
	tx.Scan(&ary)

	for i := 0; i < len(ary); i++ {
		lastCreatedAt = ary[i].CreatedAt
		total += 1
		if logicFunc != nil {
			if err := logicFunc(ary[i]); err != nil {
				logger.Error(err)
				return total, err
			}
		}
	}

	return total, tx.Error
}

func (o *TrainTags) SetCount(count int) error {
	return DB.Model(o).Updates(map[string]interface{}{"count": count}).Error
}

func (o *TrainTags) SetStatus(status TrainTagsStatusEnum) error {
	if status == TrainTagsStatusQueue {
		return DB.Model(o).Updates(map[string]interface{}{"status": status, "push_at": time.Now()}).Error
	}
	return DB.Model(o).Updates(map[string]interface{}{"status": status}).Error
}

func (o *TrainTags) SetCompleted(startAt time.Time, endAt time.Time, reason string) error {
	status := o.Status
	if reason == "" {
		status = TrainTagsStatusTagSuc
	} else {
		status = TrainTagsStatusTagFail
	}
	return DB.Model(o).Updates(map[string]interface{}{"start_at": startAt, "end_at": endAt, "status": status, "reason": reason}).Error
}

func (o *TrainTags) Delete() error {
	return DB.Debug().Delete(o).Error
}

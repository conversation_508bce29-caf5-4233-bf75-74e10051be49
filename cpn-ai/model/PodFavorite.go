package model

import (
	"gorm.io/gorm"
)

type PodFavorite struct {
	gorm.Model
	UserId uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID;index"`
	PodId  uint   `json:"pod_id" gorm:"type:bigint;not null;default:0;comment:PodID"`
	Remark string `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注"`
	Status int    `json:"status" gorm:"type:int;not null;default:0;comment:状态"`
}

func (PodFavorite) TableName() string {
	return "T_PodFavorite"
}

func (o *PodFavorite) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *PodFavorite) Delete() error {
	return DB.Debug().Unscoped().Delete(o).Error
}

func (o *PodFavorite) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *PodFavorite) GetByPodAndUser(podId uint, userId uint) error {
	return DB.First(o, "user_id=? and pod_id=?", userId, podId).Error
}

func (o *PodFavorite) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["user_id"]; ok {
			tx.Where("user_id=?", queryParm["user_id"])
		}
		if _, ok := queryParm["pod_id"]; ok {
			tx.Where("pod_id=?", queryParm["pod_id"])
		}
		if _, ok := queryParm["pod_uuid"]; ok {
			tx.Where("pod_uuid=?", queryParm["pod_uuid"])
		}
		tx.Where("status<?", 9)
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	tx.Scan(dest)
	return total, tx.Error
}

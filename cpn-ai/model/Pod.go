package model

import (
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type PodClassTypeEnum int

const (
	PodClassTypeNormal  PodClassTypeEnum = 0
	PodClassTypeXueTang                  = 1
	PodClassTypeGaoXiao                  = 2
	PodClassTypeCCM                      = 9
)

func PodClassTypeEnumName(i PodClassTypeEnum) string {
	switch i {
	case PodClassTypeNormal:
		//return "晨羽Pod"
		return "应用商店"
	case PodClassTypeXueTang:
		return "学堂"
	case PodClassTypeGaoXiao:
		return "高校"
	case PodClassTypeCCM:
		return "算力市场"
	}
	return ""
}

type Pod struct {
	gorm.Model
	UserId           uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID;index"`
	UserShortId      string `json:"user_short_id" gorm:"type:varchar(50);not null;default:'';comment:用户短字符串ID"`
	AuthorName       string `json:"author_name" gorm:"type:varchar(50);not null;default:'';comment:作者名称"`
	Uuid             string `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID"`
	Title            string `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	PodName          string `json:"pod_name" gorm:"type:varchar(50);not null;default:'';comment:名称"`
	ImageTags        string `json:"image_tags" gorm:"type:varchar(500);not null;default:'';comment:发布的镜像标签(空格隔开)"`
	Desc             string `json:"desc" gorm:"type:varchar(1500);not null;default:'';comment:说明(pod类型会对外显示)"`
	ClassRoomIds     string `json:"class_room_ids" gorm:"type:varchar(500);not null;default:'';comment:课堂IDs"`
	LabelIds         string `json:"label_ids" gorm:"type:varchar(1500);not null;default:'';comment:标签IDs"`
	Category         int    `json:"category" gorm:"type:int;not null;default:0;comment:类型"` //1语言模型
	NeedGpus         int    `json:"need_gpus" gorm:"type:int;not null;default:0;comment:所需GPU数量"`
	NeedMemory       int    `json:"need_memory" gorm:"type:int;not null;default:0;comment:最低单卡显存(单位G)"`
	StartupElapse    int    `json:"startup_elapse" gorm:"type:bigint;not null;default:0;comment:预计启动消耗时间(单位秒)"`
	Price            string `json:"price" gorm:"type:varchar(200);not null;default:'';comment:单价"`
	DataFolder       string `json:"data_folder" gorm:"type:varchar(50);not null;default:'';comment:容器数据文件夹"`
	SoftLinks        string `json:"soft_links" gorm:"type:json;comment:软链接"`
	PortMaps         string `json:"port_maps" gorm:"type:varchar(500);not null;default:'';comment:端口映射"`
	ImageName        string `json:"image_name" gorm:"type:varchar(500);not null;default:'';comment:带路径镜像名称"` //镜像ID为0时启用
	ImageTag         string `json:"image_tag" gorm:"type:varchar(250);not null;default:'';comment:默认镜像标签"`
	Keywords         string `json:"keywords" gorm:"type:varchar(500);not null;default:'';comment:查询关键字"`
	Command          string `json:"command" gorm:"type:varchar(1000);not null;default:'';comment:docker启动命令"`
	Remark           string `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注"`
	Catalogue        string `json:"catalogue" gorm:"type:varchar(500);not null;default:'';comment:容器文件夹Key,多个用,隔开"`
	Suggest          int    `json:"suggest" gorm:"type:int;not null;default:0;comment:推荐"`
	Popular          int    `json:"popular" gorm:"type:int;not null;default:0;comment:热门"`
	Insider          int    `json:"insider" gorm:"type:int;not null;default:0;comment:1只有内部可见"`
	ClassType        int    `json:"class_type" gorm:"type:integer;not null;default:0;comment:0晨羽POD 1AI学堂 9算力市场"`
	Cover            string `json:"cover" gorm:"type:varchar(100);not null;default:'';comment:封面地址"`
	Logo             string `json:"logo" gorm:"type:varchar(100);not null;default:'';comment:logo地址"`
	AccessCount      int    `json:"access_count" gorm:"type:int;not null;default:0;comment:访问次数"`
	UsageCount       int    `json:"usage_count" gorm:"type:int;not null;default:0;comment:使用次数"`
	FavoriteCount    int    `json:"favorite_count" gorm:"type:int;not null;default:0;comment:收藏数量"`
	UsageDuration    uint   `json:"usage_duration" gorm:"type:bigint;not null;default:0;comment:使用时长(以秒为单位)"`
	Status           int    `json:"status" gorm:"type:int;not null;default:0;comment:状态"`         //1生效
	AuditStatus      int    `json:"audit_status" gorm:"type:int;not null;default:0;comment:审核状态"` //
	AuditContent     string `json:"audit_content" gorm:"type:json;comment:审核内容"`
	InstanceUuid     string `json:"instance_uuid" gorm:"type:varchar(50);not null;default:'';comment:绑定的实例ID"`
	StartupVirtualId uint   `json:"startup_virtual_id" gorm:"type:bigint;not null;default:0;comment:指定虚拟机"`
	VirtualIds       string `json:"virtual_ids" gorm:"type:varchar(500);not null;default:'';comment:定向使用VirtualIDs"`
	Markdown         string `json:"markdown" gorm:"type:text;comment:详情页markdown格式"`
	OperatorUserId   uint   `json:"operator_user_id" gorm:"type:bigint;not null;default:0;comment:操作员用户ID"`
	AllowGrayTest    int    `json:"allow_gray_test" gorm:"type:int;not null;default:0;comment:允许灰度测试"`
}

//Price {"hour":1.66,"day":38.65,"week":245.43,"month":847.80}

func (Pod) TableName() string {
	return "T_Pod"
}

func (o *Pod) Save() error {
	if o.SoftLinks == "" {
		o.SoftLinks = "[]"
	}
	return DB.Debug().Save(o).Error
}

func (o *Pod) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *Pod) GetByIdFromCache(id uint) error {

	key := fmt.Sprintf("model_pod_%d", id)
	if data, err := cache.Get(key); err == nil {
		if err := json.Unmarshal(data, o); err == nil {
			return nil
		} else {
			logger.Error(key, " err:", err)
		}
	}

	if err := DB.First(o, id).Error; err != nil {
		return err
	} else {
		// 将结果缓存
		if data, err := json.Marshal(*o); err == nil {
			if err := cache.Set(key, data); err == nil {
				return nil
			}
		}
		return nil
	}
}

func (o *Pod) GetByUuid(uuid string) error {
	if err := DB.First(o, "uuid=?", uuid).Error; err != nil {
		return err
	} else if o.ID == 0 {
		return gorm.ErrRecordNotFound
	} else {
		return nil
	}
}

func (o *Pod) GetByUuidFromCache(uuid string) error {

	key := fmt.Sprintf("model_pod_%s", uuid)
	if data, err := cache.Get(key); err == nil {
		if err := json.Unmarshal(data, o); err == nil {
			return nil
		} else {
			logger.Error(key, " err:", err)
		}
	}

	if err := DB.First(o, "uuid=?", uuid).Error; err != nil {
		return err
	} else {
		// 将结果缓存
		if data, err := json.Marshal(*o); err == nil {
			if err := cache.Set(key, data); err == nil {
				return nil
			}
		}
		return nil
	}
}

func (o *Pod) GetByIds(dest interface{}, ids []uint) error {
	tx := DB.Debug().Model(o)
	tx.Where("id in ? ", ids)
	return tx.Error
}

func (o *Pod) ListByLastAutoId(dest interface{}, lastAutoId uint, pageSize int) error {
	tx := DB.Debug().Model(o)
	tx.Where("id>? ", lastAutoId)
	tx.Order("id asc").Limit(pageSize).Scan(dest)
	return tx.Error
}

func (o *Pod) List(dest interface{}, id uint, userId uint, category int, suggest int, popular int, insider int, kw string, status, auditStatus int, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if id > 0 {
		tx.Where("id=?", id)
	} else {
		if userId > 0 {
			tx.Where("user_id=?", userId)
		}
		if category > 0 {
			tx.Where("category=?", category)
		}
		if suggest > 0 && popular > 0 {
			tx.Where("suggest>?", 0)
			tx.Where("popular>?", 0)
		} else {
			if suggest == 1 {
				tx.Where("suggest>?", 0)
				tx.Order("suggest desc")
			} else if suggest == 2 {
				tx.Order("suggest desc")
			}
			if popular == 1 {
				tx.Where("popular>?", 0)
				tx.Order("popular desc")
			} else if popular == 2 {
				tx.Order("popular desc")
			}
		}

		if insider >= 0 {
			tx.Where("insider=?", insider)
		}
		if kw != "" {
			kw = "%" + kw + "%"
			tx.Where("(title like ? or T_Pod.desc like ? or audit_content like ?)", kw, kw, kw)
		}
		if status >= 0 {
			tx.Where("status=?", status)
		}
		if auditStatus > 0 {
			tx.Where("audit_status=?", auditStatus)
		}
		tx.Where("status<?", 9)
		if page == 1 {
			if err := tx.Count(&total).Error; err != nil {
				return 0, err
			}
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Pod) ListPro(dest any, queryParm map[string]any, page, pageSize int, sortBy string) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["user_id"]; ok {
			tx.Where("user_id=?", queryParm["user_id"])
		}
		if _, ok := queryParm["category"]; ok {
			tx.Where("category=?", queryParm["category"])
		}
		if _, ok := queryParm["status"]; ok {
			tx.Where("status=?", queryParm["status"])
		}
		if _, ok := queryParm["suggest"]; ok {
			if queryParm["suggest"].(int) == 1 {
				tx.Where("suggest>?", 0)
			}
			tx.Order("suggest desc")
		}
		if _, ok := queryParm["popular"]; ok {
			if queryParm["popular"].(int) == 1 {
				tx.Where("popular>?", 0)
			}
			tx.Order("popular desc")
		}
		if _, ok := queryParm["insider"]; ok {
			tx.Where("insider=?", queryParm["insider"])
		}
		if _, ok := queryParm["class_type"]; ok {
			tx.Where("class_type=?", queryParm["class_type"])
		}

		if _, ok := queryParm["audit_status"]; ok {
			tx.Where("audit_status=?", queryParm["audit_status"])
		}

		if _, ok := queryParm["instance_uuid"]; ok {
			tx.Where("instance_uuid=?", queryParm["instance_uuid"])
		}

		if _, ok := queryParm["pod_ids"]; ok {
			tx.Where("id in (?)", queryParm["pod_ids"].([]uint))
		}

		if _, ok := queryParm["pod_uuids"]; ok {
			tx.Where("uuid in (?)", queryParm["pod_uuids"].([]string))
		}

		//if _, ok := queryParm["kw"]; ok {
		//	kw := "%" + queryParm["kw"].(string) + "%"
		//	tx.Where("T_Pod.desc like ? or title like ? or audit_content like ? or author_name like ? or keywords like ?", kw, kw, kw, kw, kw)
		//}

		if _, ok := queryParm["kw"]; ok {
			kw := strings.ToLower("%" + queryParm["kw"].(string) + "%")
			tx.Where("LOWER(CONCAT(T_Pod.desc, ' ', T_Pod.title, ' ', IFNULL(T_Pod.audit_content, ''), ' ', T_Pod.author_name, ' ', T_Pod.keywords)) LIKE ?", kw)
		}

		if _, ok := queryParm["label_id"]; ok {
			kw := fmt.Sprintf("%%|%d|%%", queryParm["label_id"].(uint))
			tx.Where("label_ids like ?", kw)
		}

		if _, ok := queryParm["label_ids"]; ok {
			labelIds := queryParm["label_ids"].(string)
			ary := strings.Split(labelIds, "|")
			out := make([]string, 0)
			//select * from T_Pod where label_ids REGEXP '\\|(8|12|13)\\|';
			for _, val := range ary {
				if val == "" {
					continue
				}
				if len(val) > 5 {
					continue
				}
				out = append(out, val)
			}
			if len(out) > 0 {
				str := strings.Join(out, "|")
				regexp := fmt.Sprintf(`\|(%s)\|`, str)
				tx.Where("label_ids REGEXP ?", regexp)
			}
		}

		tx.Where("status<?", 9)
		if page == 1 {
			if err := tx.Count(&total).Error; err != nil {
				return 0, err
			}
		}
		if sortBy != "" {
			tx.Order(sortBy + " desc")
		}
		tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	}
	tx.Scan(dest)
	return total, tx.Error
}

func (o *Pod) CountByUserId(userId uint) int64 {
	var count int64
	if err := DB.Model(o).Where("user_id=?", userId).Count(&count).Error; err != nil {
		logger.Error(err, "userId:", userId)
		// 处理错误
		return -1
	} else {
		return count
	}
}

func (o *Pod) SetImageTags(tags string) error {
	//return DB.Model(o).Updates(Pod{ImageTags: tags}).Error
	return DB.Model(o).Update("image_tags", tags).Error
}

func (o *Pod) SetLogo(path string) error {
	return DB.Model(o).Updates(Pod{Logo: path}).Error
}
func (o *Pod) SetCover(path string) error {
	return DB.Model(o).Updates(Pod{Cover: path}).Error
}
func (o *Pod) SetMarkdown(txt string) error {
	return DB.Model(o).Updates(Pod{Markdown: txt}).Error
}

func (o *Pod) SetAuditStatus(status int) error {
	return DB.Model(o).Updates(Pod{AuditStatus: status}).Error
}

func (o *Pod) SetStatus(status int) error {
	return DB.Model(o).Update("status", status).Error
}

func (o *Pod) SetAuditContent(txt string) error {
	return DB.Model(o).Updates(Pod{AuditContent: txt, AuditStatus: enums.PodAuditStatusEnum.Makeing}).Error
}

func (o *Pod) UpFavoriteCount() error {
	return DB.Model(o).UpdateColumn("favorite_count", gorm.Expr("favorite_count + ?", 1)).Error
}

func (o *Pod) UpAccessCount() error {
	return DB.Model(o).UpdateColumn("access_count", gorm.Expr("access_count + ?", 1)).Error
}

func (o *Pod) UpUsageCount() error {
	return DB.Model(o).UpdateColumn("usage_count", gorm.Expr("usage_count + ?", 1)).Error
}

func (o *Pod) UpUsageDuration(secondes uint) error {
	return DB.Model(o).UpdateColumn("usage_duration", gorm.Expr("usage_duration + ?", secondes)).Error
}

func (o *Pod) SetInstanceUuid(instanceUuid string) error {
	return DB.Model(o).Updates(Pod{InstanceUuid: instanceUuid}).Error
}

func (o *Pod) SetInstanceUuidEmpty(podId uint, instanceUuid string) error {
	return DB.Model(o).Where("id=? and instance_uuid=?", podId, instanceUuid).Update("instance_uuid", "").Error
}

func (o *Pod) SetAuthorName(authorName string) error {
	return DB.Model(o).Update("author_name", authorName).Error
}

func (o *Pod) SetKeywords(keywords string) error {
	return DB.Model(o).Update("keywords", keywords).Error
}

func (o *Pod) RemoveClassRoomId(tx *gorm.DB, roomId uint) error {
	str := strings.Replace(o.ClassRoomIds, fmt.Sprintf("|%d|", roomId), "|", -1)
	if str == "|" {
		str = ""
	}
	return tx.Model(o).Update("class_room_ids", str).Error
}

func (o *Pod) AddClassRoomId(tx *gorm.DB, roomId uint) error {
	if strings.Contains(o.ClassRoomIds, fmt.Sprintf("|%d|", roomId)) {
		return nil
	}
	if o.ClassRoomIds == "" {
		o.ClassRoomIds = "|"
	}
	o.ClassRoomIds = o.ClassRoomIds + fmt.Sprintf("%d|", roomId)
	return tx.Model(o).Update("class_room_ids", o.ClassRoomIds).Error
}

func (o *Pod) Updates(m map[string]interface{}) error {
	return DB.Model(o).Debug().Updates(m).Error
}

func (o *Pod) CalculateAmount(chargingType int, num int) decimal.Decimal {

	var m map[string]interface{}
	// 反序列化 JSON 字符串
	err := json.Unmarshal([]byte(o.Price), &m)
	if err != nil {
		logger.Error(err, o.Price)
		return decimal.Zero
	}
	amount := decimal.Zero
	if chargingType == enums.ChargingTypeEnum.Usage { //num是秒数
		if v, ok := m["hour"]; ok {
			hourPrice := decimal.NewFromFloat(v.(float64))

			duration := time.Second * time.Duration(num)
			hours := int64(duration.Hours())
			remainingSeconds := int(duration.Seconds()) % 3600
			if hours > 0 {
				amount = hourPrice.Mul(decimal.NewFromInt(hours))
			}
			if remainingSeconds > 0 {
				tmp := hourPrice.Mul(decimal.NewFromInt(int64(remainingSeconds))).Div(decimal.NewFromInt(3600))
				amount = amount.Add(tmp)
			}
			return amount
		}
	}
	if chargingType == enums.ChargingTypeEnum.Day {
		if v, ok := m["day"]; ok {
			tmp := decimal.NewFromFloat(v.(float64))
			return tmp.Mul(decimal.NewFromInt(int64(num)))
		}
	} else if chargingType == enums.ChargingTypeEnum.Week {
		if v, ok := m["week"]; ok {
			tmp := decimal.NewFromFloat(v.(float64))
			return tmp.Mul(decimal.NewFromInt(int64(num)))
		}
	} else if chargingType == enums.ChargingTypeEnum.Month {
		if v, ok := m["month"]; ok {
			tmp := decimal.NewFromFloat(v.(float64))
			return tmp.Mul(decimal.NewFromInt(int64(num)))
		}
	}
	return decimal.Zero
}

func (o *Pod) UnitPrice(chargingType int) decimal.Decimal {

	var m map[string]interface{}
	// 反序列化 JSON 字符串
	err := json.Unmarshal([]byte(o.Price), &m)
	if err != nil {
		logger.Error(err, o.Price)
		return decimal.Zero
	}
	amount := decimal.Zero
	if chargingType == enums.ChargingTypeEnum.Usage { //num是秒数
		if v, ok := m["hour"]; ok {
			amount = decimal.NewFromFloat(v.(float64))
		}
	}
	if chargingType == enums.ChargingTypeEnum.Day {
		if v, ok := m["day"]; ok {
			amount = decimal.NewFromFloat(v.(float64))

		}
	} else if chargingType == enums.ChargingTypeEnum.Week {
		if v, ok := m["week"]; ok {
			amount = decimal.NewFromFloat(v.(float64))
		}
	} else if chargingType == enums.ChargingTypeEnum.Month {
		if v, ok := m["month"]; ok {
			amount = decimal.NewFromFloat(v.(float64))
		}
	}
	return amount
}

package model

import (
	"errors"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type CertifyStudentCertTypeEnum string

const (
	CertifyStudentCertType_ID_CAED CertifyStudentCertTypeEnum = "ID_CAED"
)

func CertifyStudentCertTypeEnumName(i CertifyStudentCertTypeEnum) string {
	switch i {
	case CertifyStudentCertType_ID_CAED:
		return "学生证"
	}
	return ""
}

type CertifyStudent struct {
	gorm.Model
	Nanoid      string                     `json:"nanoid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID;index"`
	UserId      uint                       `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	CertChannel string                     `json:"cert_channel" gorm:"type:varchar(50);not null;default:'';comment:验证渠道"`
	CertType    CertifyStudentCertTypeEnum `json:"cert_type" gorm:"type:varchar(50);not null;default:'';comment:证件类型"`
	SchoolName  string                     `json:"school_name" gorm:"type:varchar(100);not null;default:'';comment:学校名称"`
	TrueName    string                     `json:"true_name" gorm:"type:varchar(100);not null;default:'';comment:真实姓名"`
	CertNo      string                     `json:"cert_no" gorm:"type:varchar(100);not null;default:'';comment:真实身份证号"`
	IdCardImg   string                     `json:"id_card_img" gorm:"type:varchar(100);not null;default:'';comment:学生证"`
	IdCardNo    string                     `json:"id_card_no" gorm:"type:varchar(100);not null;default:'';comment:学生证号码"`
	EduEmail    string                     `json:"edu_email" gorm:"type:varchar(100);not null;default:'';comment:教育邮箱"`
	VerifyCode  string                     `json:"verify_code" gorm:"type:varchar(50);not null;default:'';comment:验证码"`
	FrontImg    string                     `json:"front_img" gorm:"type:varchar(100);not null;default:'';comment:身份正面照片"`
	BackImg     string                     `json:"back_img" gorm:"type:varchar(100);not null;default:'';comment:身份背面照片"`
	CertTime    time.Time                  `json:"cert_time" gorm:"type:datetime;default:'1900-01-01';comment:认证时间"`
	Status      int                        `json:"status" gorm:"type:tinyint;not null;default:0;comment:状态"` //1审核通过 2不通过
	Reason      string                     `json:"reason" gorm:"type:varchar(50);not null;default:'';comment:审核失败原因"`
}

func (CertifyStudent) TableName() string {
	return "T_CertifyStudent"
}

func (o *CertifyStudent) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *CertifyStudent) GetByNanoid(nanoid string) error {
	err := DB.Debug().First(o, "nanoid = ?", nanoid).Error
	return err
}

func (o *CertifyStudent) GetByUserId(userId uint) error {
	return DB.First(o, "user_id=?", userId).Error
}

func (o *CertifyStudent) GetByCertNo(certNo string) error {
	return DB.First(o, "cert_no=?", certNo).Error
}
func (o *CertifyStudent) GetByIdCardNo(idCardNo string) error {
	return DB.First(o, "id_card_no=?", idCardNo).Error
}

func (o *CertifyStudent) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["user_id"]; ok {
			tx.Where("user_id=?", queryParm["user_id"])
		}
		if _, ok := queryParm["status"]; ok {
			tx.Where("status=?", queryParm["status"])
		}
		tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Scan(dest)
	return total, tx.Error
}

func (o *CertifyStudent) SetStatusPass() error {
	return DB.Transaction(func(tx *gorm.DB) error {
		now := time.Now()
		var user User
		if err := tx.First(&user, o.UserId).Error; err != nil {
			return err
		}
		result := tx.Model(o).Updates(map[string]interface{}{"status": 1, "cert_time": now})
		if result.Error != nil {
			return result.Error
		}
		if result.RowsAffected != 1 {
			return errors.New(fmt.Sprintf("更新学生认证记录失败,影响记录为%d", result.RowsAffected))
		}
		result = tx.Model(&user).Updates(map[string]interface{}{"student_cerify_id": o.ID, "student_cerify_at": now})
		if result.Error != nil {
			return result.Error
		}
		if result.RowsAffected != 1 {
			return errors.New(fmt.Sprintf("更新用户学生认证失败,影响记录为%d", result.RowsAffected))
		}
		return nil
	})
}

func (o *CertifyStudent) SetCertifyCancel() error {
	return DB.Transaction(func(tx *gorm.DB) error {
		var user User
		if err := tx.First(&user, o.UserId).Error; err != nil {
			return err
		}
		result := tx.Model(o).Updates(map[string]interface{}{"status": 0})
		if result.Error != nil {
			return result.Error
		}
		if result.RowsAffected != 1 {
			return errors.New(fmt.Sprintf("更新学生认证记录失败,影响记录为%d", result.RowsAffected))
		}
		result = tx.Model(&user).Updates(map[string]interface{}{"student_cerify_id": 0})
		if result.Error != nil {
			return result.Error
		}
		if result.RowsAffected != 1 {
			return errors.New(fmt.Sprintf("更新用户学生认证失败,影响记录为%d", result.RowsAffected))
		}
		return nil
	})
}

func (o *CertifyStudent) SetStatusRefuse(reason string) error {
	return DB.Model(o).Updates(map[string]interface{}{"status": 2, "reason": reason}).Error
}

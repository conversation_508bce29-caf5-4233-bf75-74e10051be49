package model

import (
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"time"
)

type AmountCalculate struct { //暂时废弃不用
	AmountBalance
	PodUserId uint            `json:"pod_user_id" gorm:"type:bigint;not null;default:0;comment:Pod开发者ID"`
	PodEarn   decimal.Decimal `json:"pod_earn" gorm:"type:decimal(16, 8);not null;default:0;comment:Pod赚取佣金"`
	PodCalcAt time.Time       `json:"pod_calc_at" gorm:"type:datetime;default:'1900-01-01';comment:Pod佣金计算时间"`
	CalcInfo  string          `json:"calc_info" gorm:"type:json;comment:附属信息"`
}

func (AmountCalculate) TableName() string {
	return "T_AmountCalculate"
}

func (o *AmountCalculate) Create() error {
	return DB.Debug().Create(o).Error
}

func (o *AmountCalculate) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["user_id"]; ok {
			tx.Where("user_id=?", queryParm["user_id"])
		}
		if _, ok := queryParm["card_id"]; ok {
			tx.Where("card_id=?", queryParm["card_id"])
		}
		if _, ok := queryParm["pod_id"]; ok {
			tx.Where("pod_id=?", queryParm["pod_id"])
		}
		if _, ok := queryParm["instance_id"]; ok {
			tx.Where("instance_id=?", queryParm["instance_id"])
		}
		if _, ok := queryParm["order_no"]; ok {
			tx.Where("order_no=?", queryParm["order_no"])
		}
		if _, ok := queryParm["order_type"]; ok {
			tx.Where("order_type=?", queryParm["order_type"])
		}
		if _, ok := queryParm["kw"]; ok {
			tx.Where("remark like ?", "%"+queryParm["kw"].(string)+"%")
		}

		if page == 1 {
			if err := tx.Count(&total).Error; err != nil {
				return 0, err
			}
		}
		tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	}
	if dest != nil {
		tx.Scan(dest)
	}
	return total, tx.Error
}

func (o *AmountCalculate) ListForCheckBalance(dest interface{}, userId uint, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o).Where("user_id=?", userId)
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id asc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *AmountCalculate) GetByOrderNo(orderNo string) error {
	return DB.First(o, "order_no=?", orderNo).Error
}

func (o *AmountCalculate) ExistsOrderNo(orderNo string) (bool, error) {
	err := DB.First(o, "order_no=?", orderNo).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return false, nil // 记录不存在
	}
	if err != nil {
		return false, err // 出现其他错误
	}
	return true, nil // 记录存在
}

func (balance *AmountCalculate) New(tx *gorm.DB) error {

	if err := balance.checkBalance(); err != nil {
		logger.Error(err)
		return err
	}

	if balance.CardId > 0 {
		var card Card
		if err := tx.First(&card, balance.CardId).Error; err != nil || card.ID == 0 {
			logger.Error(err, " orderNo:", balance.OrderNo, " balance.OccurredAmount:", balance.OccurredAmount, " cardID:", card.ID)
			return err
		}

		after := card.LeaveAmount.Add(balance.OccurredAmount)
		balance.BeforeOccurred = card.LeaveAmount
		balance.AfterOccurred = after

		if err := tx.Save(balance).Error; err != nil {
			logger.Error(err)
			return err
		}
		if balance.ID == 0 {
			logger.Error("生成流水记录失败")
			return errors.New("生成流水记录失败")
		}

		result := tx.Model(&card).Updates(map[string]interface{}{"leave_amount": after})
		if err := result.Error; err != nil {
			logger.Error(err)
			return err
		}

		if result.RowsAffected != 1 {
			err := errors.New(fmt.Sprintf("更新卡leaveAmount失败 cardId:%d", card.ID))
			logger.Error(err)
			return err
		}
	} else {
		var user User
		if err := tx.First(&user, balance.UserId).Error; err != nil || user.ID == 0 {
			logger.Error(err, " orderNo:", balance.OrderNo, " balance.OccurredAmount:", balance.OccurredAmount, " userID:", user.ID)
			return err
		}

		after := user.Amount.Add(balance.OccurredAmount)
		balance.BeforeOccurred = user.Amount
		balance.AfterOccurred = after

		if err := tx.Save(balance).Error; err != nil {
			logger.Error(err)
			return err
		}

		result := tx.Model(&user).Updates(map[string]interface{}{"amount": after})
		if err := result.Error; err != nil {
			logger.Error(err)
			return err
		}

		if result.RowsAffected != 1 {
			err := errors.New(fmt.Sprintf("更新用户amount失败 userId:%d", user.ID))
			logger.Error(err)
			return err
		}
	}

	return nil
}

func (balance *AmountCalculate) GetBalanceObject(orderNo string, cardId uint, userId uint, orderType int, occurred decimal.Decimal, show string, remark string, operatorId uint, operator string) error {
	balance.OrderNo = orderNo
	balance.CardId = cardId
	balance.UserId = userId
	balance.OrderType = orderType
	balance.Show = show
	balance.Remark = remark
	balance.Operator = operator
	balance.OperatorId = operatorId
	balance.OccurredAmount = occurred
	err := balance.checkBalance()
	if err != nil {
		return err
	}
	return nil
}

func (balance *AmountCalculate) checkBalance() error {
	if len(balance.OrderNo) == 0 {
		return errors.New("单号不正确")
	}

	if balance.UserId == 0 {
		return errors.New("未找到用户")
	}
	if len(balance.OrderNo) == 0 {
		return errors.New("业务单号为空")
	}
	if balance.OccurredAmount == decimal.NewFromInt(0) {
		return errors.New("没有发生金额")
	}
	if balance.OrderType == 0 {
		return errors.New("没有业务类型")
	}

	if balance.CardId > 0 {
		if balance.OccurredAmount.GreaterThanOrEqual(decimal.Zero) {
			return errors.New("算力卡只支持扣款")
		}
	}
	if balance.OrderType == enums.OrderTypeEnum.ManagerAdd || balance.OrderType == enums.OrderTypeEnum.RechargeBuy || balance.OrderType == enums.OrderTypeEnum.UserGift || balance.OrderType == enums.OrderTypeEnum.Subscribe || balance.OrderType == enums.OrderTypeEnum.Refund {
		//if balance.OccurredAmount.Cmp(decimal.NewFromInt(0)) == -1 {
		//	return errors.New("金额与业务类型不匹配")
		//}
		if balance.OccurredAmount.LessThanOrEqual(decimal.Zero) {
			return errors.New("金额与业务类型不匹配")
		}
	}
	if balance.OrderType == enums.OrderTypeEnum.Cost || balance.OrderType == enums.OrderTypeEnum.CostLLM || balance.OrderType == enums.OrderTypeEnum.CostINST || balance.OrderType == enums.OrderTypeEnum.ManagerCost {
		//if balance.OccurredAmount.Cmp(decimal.NewFromInt(0)) == 1 {
		//	return errors.New("金额与业务类型不匹配")
		//}
		if balance.OccurredAmount.GreaterThanOrEqual(decimal.Zero) {
			return errors.New("金额与业务类型不匹配")
		}
		if balance.OccurredAmount.LessThanOrEqual(decimal.NewFromInt(-1000)) {
			return errors.New("扣款金额大于1000")
		}
	}
	return nil
}

func (o *AmountCalculate) StatsUserAmountCost(userId uint, rewardMonth time.Time) (decimal.Decimal, error) {
	var totalAmount decimal.Decimal

	firstOfMonth := time.Date(rewardMonth.Year(), rewardMonth.Month(), 1, 0, 0, 0, 0, rewardMonth.Location())
	lastOfMonth := firstOfMonth.AddDate(0, 1, 0)

	tx := DB.Debug().Model(o).
		Select("IFNULL(sum(occurred_amount), 0) as total_occurred").
		//Select("sum(occurred_amount) as total_occurred").
		Where("user_id = ?", userId).
		Where("card_id=0").
		Where("occurred_amount<0").
		Where("created_at >=? and created_at <?", firstOfMonth, lastOfMonth).
		Scan(&totalAmount)
	if tx.Error != nil {
		return decimal.Zero, tx.Error
	}

	return totalAmount, tx.Error
}

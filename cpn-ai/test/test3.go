package main

import (
	"context"
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/model"
	"cpn-ai/service"
	"cpn-ai/service/guide"
	"cpn-ai/service/tasklog"
	"cpn-ai/structs"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	gonanoid "github.com/matoous/go-nanoid/v2"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"reflect"
	"regexp"
	"strings"
	"time"
)

func uuidToNumber(uuid string) uint64 {
	// 计算UUID的MD5摘要
	hash := md5.New()
	hash.Write([]byte(uuid))
	hashBytes := hash.Sum(nil)

	// 将MD5摘要转换为uint64
	var result uint64
	for i := 0; i < 8; i++ {
		result |= uint64(hashBytes[i]) << (8 * uint(i))
	}

	return result
}

func asetest() {

	//key := []byte("myverystrongpasswordo32bitlength") // 32 bytes key for AES-256
	aaa := config.EncryptAes("Hello, World!我们的大大")
	fmt.Println("aaa:", aaa)
	fmt.Println(config.DecryptAes(aaa))

	plaintext := []byte("Hello, World!")
	//
	//block, err := aes.NewCipher(key)
	//if err != nil {
	//	panic(err)
	//}
	block := config.AesBlock

	ciphertext := make([]byte, len(plaintext))
	stream := cipher.NewCTR(block, make([]byte, block.BlockSize()))
	stream.XORKeyStream(ciphertext, plaintext)

	res := string(ciphertext)
	fmt.Printf("Encrypted: %s\n", base64.StdEncoding.EncodeToString(ciphertext))
	fmt.Printf("Decrypted1213: %s\n", des(res))

	text := string(ciphertext)
	ciphertext = []byte(text)
	decrypted := make([]byte, len(ciphertext))
	stream1 := cipher.NewCTR(block, make([]byte, block.BlockSize()))
	stream1.XORKeyStream(decrypted, ciphertext)

	fmt.Printf("Decrypted: %s\n", decrypted)

}

func des(text string) string {
	block := config.AesBlock
	//text := string(ciphertext)
	ciphertext := []byte(text)
	decrypted := make([]byte, len(ciphertext))
	stream := cipher.NewCTR(block, make([]byte, block.BlockSize()))
	stream.XORKeyStream(decrypted, ciphertext)

	fmt.Printf("Decrypted: %s\n", decrypted)
	return string(decrypted)
}

func aaa() { // 测试函数名必须以Test开头，必须接收一个*testing.T类型参数
	ary := strings.Split("2|10|8", "|")
	labelIds := "|"
	for _, tmp := range ary {
		if tmp == "|" {
			continue
		}
		tmpLabelId := utils.String2Uint(tmp)
		if aryLabelId, err := service.GetParentLabels(tmpLabelId); err != nil {
			logger.Error(err, "oReq.LabelIds:")
		} else {
			for _, labelId := range aryLabelId {
				key := fmt.Sprintf("|%d|", labelId)
				if !strings.Contains(labelIds, key) {
					labelIds += fmt.Sprintf("%d|", labelId)
				}
			}
		}
	}
	fmt.Println("labelIds" + labelIds)
	if labelIds == "|" {
		labelIds = ""
	}
}

func GenSaveImageTag(tag string) string {

	// 正则表达式：匹配时间部分（yyyyMMddHHmmss 格式）
	re := regexp.MustCompile(`_\d{14}$`)

	// 获取当前时间，格式化为 yyyyMMddHHmmss
	currentTime := time.Now().Format("20060102150405")

	// 检查是否匹配到符合条件的时间部分
	if re.MatchString(tag) {
		// 如果匹配到时间部分，则替换
		tag = re.ReplaceAllString(tag, "_"+currentTime)
	} else {
		// 如果没有匹配到，则在末尾加上 _currentTime
		tag = tag + "_" + currentTime
	}
	return tag
}

func testRedisQueue() {
	/*
		if err := common.InitRedisClient(); err != nil {
			common.FatalLog("failed to initialize Redis: " + err.Error())
		}
		queueName := "TestTask"

		redisqueue.LastPopAt.Store(queueName, time.Now().UnixMilli())
		if pushAt, ok := redisqueue.LastPopAt.Load(queueName); !ok {

		} else {
			lastPushAt := pushAt.(int64)
			fmt.Println(lastPushAt)
		}

		for i := 0; i < 0; i++ {
			m := utils.MakeInterfaceMap()
			qTaskId := "aabbccdd"
			m["q_task_id"] = qTaskId
			m["aa"] = 1
			m["bb"] = 2
			//m["at"] = time.Now().UnixMilli()
			taskJson := utils.GetJsonFromStruct(m)
			if _, err := redisqueue.Push(queueName, taskJson); err != nil {
				logger.Error(err)
			}
			//logger.Info(qTaskId, " Rank:", redisqueue.Rank(queueName, qTaskId))
		}

		if rank, err := redisqueue.RemoveHash(queueName, "aabbccdd"); err != nil {
			logger.Error(err)
		} else {
			fmt.Println("aabbccdd remove: ", rank)
		}*/

	//if rank, err := redisqueue.Rank(queueName, "aabbccdd"); err != nil {
	//	logger.Error(err)
	//} else {
	//	fmt.Println("aabbccdd rank: ", rank)
	//}
	//
	//if rank, err := redisqueue.Rank(queueName, "e8e777cac4b64839bfaaf4ca61c75550"); err != nil {
	//	logger.Error(err)
	//} else {
	//	fmt.Println("aabbccddee rank: ", rank)
	//}

	//if taskJson, err := redisqueue.Pop(queueName); err != nil {
	//	logger.Error(err)
	//} else {
	//	logger.Info(taskJson)
	//}
}

func main() {
	//{
	//	ii := int64()
	//	shortid.DecimalToBase36()
	//}
	if 1 == 2 {
		testRedisQueue()
	}
	if 1 == 2 {

		json := `{"code":0,"msg":"\u64cd\u4f5c\u6210\u529f"}`
		m := utils.GetMapFromJson(json)
		if v, ok := m["code"]; ok {
			if v.(float64) == 0 {
				fmt.Println(1)
			}
		}
	}
	if 1 == 2 {

		userBasePath := "/mnt/user-data/store0/c503abeeefb74af4ab4cb0e5948d4c56/"

		if service.UserCatalogue.CheckBasePath(userBasePath) == false {
			msg := "根目录不正确"
			logger.Error(msg, "  userBasePath:", userBasePath)
			return
		}

		fmt.Println(utils.Generate12NanoId())

		durationStart := time.Now()
		durationEnd := durationStart.AddDate(0, 0, 1)
		duration := durationEnd.Sub(durationStart)
		seconds := uint(duration.Seconds())

		fmt.Println(seconds)

		fmt.Println(GenSaveImageTag("20241115-20241115-zh_2025011520160_20250116112506"))

		if err := model.InitDB(); err != nil {
			common.FatalLog("failed to initialize database: " + err.Error())
		}
		var instImage model.PodImage
		if err := instImage.GetPrivatePodImage(2, 1, "instance.ImageTag"); err != nil {
			if err == gorm.ErrRecordNotFound {
				msg := "实例个人镜像已不存在"
				logger.Error(msg, err, " instanceUuid:")
			} else {
				msg := "获取实例个人镜像信息失败"
				logger.Error(msg, err, " instanceUuid:")
			}
		}

		var item model.Instance
		if err := item.GetByUuid("e6a1f8d149b344baa56f71ed0a80ccb2"); err != nil {
			logger.Error(err)
		}
		fmt.Println(service.IsOverDayInstance(item))
	}
	if 1 == 1 {

		//if ary, err := utils.ReadLinesReversed("/Users/<USER>/fsdownload/train.log", 10); err != nil {
		//	fmt.Println(err)
		//} else {
		//	fmt.Println(ary)
		//}

		if err := model.InitDB(); err != nil {
			common.FatalLog("failed to initialize database: " + err.Error())
		}
		if err := common.InitRedisClient(); err != nil {
			common.FatalLog("failed to initialize Redis: " + err.Error())
		}

		service.TrainService.Run()
		return
	}
	if 1 == 2 {

		service.TrainService.Run()

		fmt.Println(utils.GetLocalIP())
		binary := "100000000"
		dec, _ := guide.BinaryToDecimal(binary)
		fmt.Println(binary, "   ", dec)
		fmt.Println(guide.SetEventCompleted(2, 2))
		fmt.Println(guide.IsEventCompleted(2, 2))
		fmt.Println(guide.IsEventId(34), "  ", guide.DecimalToBinary(34))

		fmt.Println(guide.DecimalToBinary(int64(33)))
		fmt.Println(guide.DecimalToBinary(int64(8)))
		fmt.Println(guide.IsEventCompleted(33, 8))

		guideId := int64(32)
		fmt.Println("guideId:", guideId, "  ", guide.DecimalToBinary(guideId))
		guideStatus := int64(4)
		fmt.Println("guideStatus:", guideStatus, "  ", guide.DecimalToBinary(guideStatus))

		fmt.Println("IsGuideCompleted:", guide.IsGuideCompleted(guideId, guideStatus))

		guideStatus = guide.SetGuideCompleted(guideId, guideStatus)
		fmt.Println("SetGuideCompleted:", guideStatus, " ", guide.DecimalToBinary(guideStatus))
		fmt.Println("IsGuideCompleted:", guide.IsGuideCompleted(guideId, guideStatus))

		fmt.Println("IsGuideCompleted:", guide.IsGuideCompleted(1, guideStatus))
		guideStatus = guide.SetGuideCompleted(1, guideStatus)
		fmt.Println("IsGuideCompleted:", guide.IsGuideCompleted(1, guideStatus))

		fmt.Println(guide.IsEventCompleted(2, 1))

		//service.EmailService.Test()
		if err := model.InitDB(); err != nil {
			common.FatalLog("failed to initialize database: " + err.Error())
		}

		{

			now := time.Now()
			statAt := time.Date(now.Year(), now.Month(), 17, 0, 0, 0, 0, now.Location())
			var statDay model.StatDay
			statDayNum := int64(0)
			if err := statDay.GetByStatDay(statDayNum); err != nil {
				if err != gorm.ErrRecordNotFound {
					msg := "查询记录失败"
					logger.Error(msg, err)

				}
			} else {
				if statDay.Status == 1 {
					msg := "记录已统计完成，不做统计 "
					logger.Error(msg, err)

				}
			}
			if statDay.ID == 0 {
				statDay.StatDay = statDayNum
			}
			statDay.StatAt = now

			type detailCostAmount struct {
				OrderType   int             `json:"order_type"`
				TotalAmount decimal.Decimal `json:"total_amount"`
			}
			detailCostAry := make([]detailCostAmount, 0)
			if err := statDay.StatDayCostDetailAmount(&detailCostAry, statAt); err != nil {
				logger.Error(err)

			} else {
				for _, item := range detailCostAry {
					if item.OrderType == enums.OrderTypeEnum.CostINST {
						statDay.DayCostInstAmount = item.TotalAmount
					} else if item.OrderType == enums.OrderTypeEnum.CostPod {
						statDay.DayCostPodAmount = item.TotalAmount
					} else if item.OrderType == enums.OrderTypeEnum.ImageStore {
						statDay.DayCostImageStore = item.TotalAmount
					} else if item.OrderType == enums.OrderTypeEnum.CloudStore {
						statDay.DayCostCloudStore = item.TotalAmount
					}
				}
			}

		}

		//if err := service.ChargingService.RunCloudStoreSettle(); err != nil {
		//	logger.Error(err)
		//} else {
		//	logger.Info("aaaaa")
		//}

		var card model.Card
		if validAmount, validCount, err := card.CardValid(2, 0); err != nil {
			logger.Error(err)
		} else {
			logger.Info(validCount, validAmount)
		}

		//var instRecord model.InstRecord
		//if err := instRecord.GetForContinueSaveImage(22); err != nil {
		//	msg := "查询启动记录失败" + err.Error()
		//	logger.Error(msg, err)
		//	return
		//}

		now := time.Date(2024, 11, 1, 0, 0, 0, 0, time.Now().Location())
		rewardMonth := now
		var check model.AmountBalance
		//if err := check.GetLastCalcPodReward(); err != nil {
		//	logger.Error(err)
		//}

		if err := check.CheckPodReward(rewardMonth); err != nil {
			if err != gorm.ErrRecordNotFound {
				logger.Error("rewardMonth:", rewardMonth.String(), " 检查pod流水记录失败 err:", err)

			}
		} else {
			err = errors.New("还有未计算完的pod流水记录")
			logger.Error("rewardMonth:", rewardMonth.String(), "  err:", err)

		}
		if err := service.RewardPodUsageService.RunEveryday(now); err != nil {
			logger.Error("统计Pod每月佣金出错 err:", err)
			//logKey := "RewardPodUsageService.RunEveryday"
			//EmailService.AddNeedSend(logKey, "统计Pod每月佣金出错 err:"+err.Error()+" 检查时间:"+jsontime.Now().String())
		}

		//service.RewardPodUsageService.DataTransfer()

		//manage.GpuModelApi.Test()

		return
		rewardMonthStr := "202503"
		rewardMonthTime := time.Date(utils.String2Int(rewardMonthStr[:4]), time.Month(utils.String2Int(rewardMonthStr[4:])), 1, 0, 0, 0, 0, time.Now().Location())

		if err := service.InvitationService.GenRewardRecord(14210, rewardMonthTime); err != nil {
			logger.Error(err)
			return
		}

		service.DirMakeSureByUser(2, 653)

		service.WarnService.PrivateCloudStore(2, 23, 23452345345345345)

		var instance model.Instance
		lastAutoId := uint(0)
		pageSize := 50
		for {
			var ary = make([]model.Instance, 0)
			if err := instance.ListForSettle(&ary, lastAutoId, pageSize); err != nil {
				logger.Error(err)
				//return err
			} else {
				total := len(ary)
				logger.Info(fmt.Sprintf("找到%d条需要处理的扣费实例", total))
				if total == 0 {
					logger.Info(fmt.Sprintf("找到%d条需要处理的扣费实例,退出循环,break", total))
					break
				}
			}
		}

		var item model.Instance
		if err := item.GetByUuid("80de1a1f68ff4e5cb9c629967dab2a85"); err != nil {
			logger.Error(err)
		}

		var user model.User
		if err := user.GetById(item.UserId); err != nil {
			logger.Error(err)
		}

		reminderAt := item.ReminderAt
		diff := time.Now().Sub(item.StartupTime)
		diffHours := diff.Hours()
		needReminder := false
		if diffHours >= 1 && diffHours < 3 {
			if reminderAt.Before(item.StartupTime.Add(time.Hour * 1)) { //提醒
				needReminder = true
			}
		} else if diffHours >= 3 && diffHours < 8 {
			if reminderAt.Before(item.StartupTime.Add(time.Hour * 3)) {
				needReminder = true
			}
		} else if diffHours >= 8 && diffHours < 24 {
			if reminderAt.Before(item.StartupTime.Add(time.Hour * 8)) {
				needReminder = true
			}
		} else if diffHours >= 24 && diffHours < 168 {
			if reminderAt.Before(item.StartupTime.Add(time.Hour * 24)) {
				needReminder = true
			}
		} else if diffHours > 168 {
			if reminderAt.Before(reminderAt.Add(time.Hour * 168)) {
				needReminder = true
			}
		}

		if needReminder {
			if err := item.SetReminderAt(); err != nil {
				logger.Error("设置提醒时间失败 err:", err, " instanceUuid:", item.Uuid)
			} else {
				// 获取天数、小时和分钟
				days := int(diff.Hours()) / 24      // 整数天数
				hours := int(diff.Hours()) % 24     // 剩余小时数
				minutes := int(diff.Minutes()) % 60 // 剩余分钟数

				reason := "已连续运行"
				if days > 0 {
					reason += fmt.Sprintf("%d天", days)
				}
				if hours > 0 {
					reason += fmt.Sprintf("%d小时", hours)
				}
				if minutes > 0 {
					reason += fmt.Sprintf("%d分钟", minutes)
				}
				//go func() {
				if msg, err := service.WarnService.InstanceRunning(item.UserId, item.Uuid, reason); err != nil {
					logger.Error("发送请求(实例运行提醒)失败 userId:", user.ID, "  uuid:", item.Uuid, "  reason:", reason, "  msg:", msg, " err:", err)
				} else {
					logger.Info("发送请求(实例运行提醒)成功 userId:", user.ID, "  uuid:", item.Uuid, "  reason:", reason)
				}
				//}()
			}
		}
	}
	{

		if hubStat, err := service.StatisticsHub(); err != nil {
			logger.Error("镜像仓库容量获取失败 err:", err)
		} else {
			logger.Info(hubStat)
		}
	}
	{
		if hubStat, err := service.StatisticsHub(); err != nil {
			logger.Error("镜像仓库容量获取失败 err:", err)
		} else {
			fmt.Println(hubStat)
			if hubStat.TotalStorageConsumption == 0 {
				logger.Error("镜像仓库容量获取失败 ", utils.GetJsonFromStruct(hubStat))
			} else {
				if hubStat.TotalStorageConsumption > 10995116277760 { //10T

				}
				logger.Info("镜像仓库存储容量检查完成,当前使用容量为", hubStat.TotalStorageConsumption/1024/1024/1024/1024, "T")
			}
		}
		return
	}

	{
		// Initialize Redis
		if err := common.InitRedisClient(); err != nil {
			common.FatalLog("failed to initialize Redis: " + err.Error())
		}
		redisKey := "sdfasdf"
		if val, err := common.RedisGet(redisKey); err != redis.Nil {
			logger.Error(err, redisKey)
		} else {
			if val != "" {

			}
		}

		listKey := "aabbcc"
		length, err := common.RedisLLen(listKey)
		if err != nil {
			logger.Error(err)

		} else {
			logger.Error(length)
		}
	}
	{

		fmt.Println(utils.FormatAccountStar("伊雄军"))
		fmt.Println(utils.FormatAccountStar("1301"))
		fmt.Println(utils.FormatAccountStar("13014"))
		fmt.Println(utils.FormatAccountStar("130146"))
		fmt.Println(utils.FormatAccountStar("*********"))
		fmt.Println(utils.FormatAccountStar("*********11"))
	}
	{

		service.StatisticsHub()

	}
	{
		const customAlphabet = "0123456789abcdefghijklmnopqrstuvwxyz"
		id1, _ := gonanoid.Generate(customAlphabet, 12)
		id, _ := gonanoid.New()
		fmt.Println(id, id1)

	}

	{
		if err := model.InitDB(); err != nil {
			common.FatalLog("failed to initialize database: " + err.Error())
		}
		var pod model.Pod
		if err := pod.GetByIdFromCache(30); err != nil {
			logger.Error(err)
		} else {
			fmt.Println(utils.GetJsonFromStruct(pod))
		}
		if err := pod.GetByIdFromCache(30); err != nil {
			logger.Error(err)
		} else {
			fmt.Println(utils.GetJsonFromStruct(pod))
		}
	}
	{
		encodeCertName := config.EncryptAes("麦麦提阿仆杜拉江.麦麦提江")
		fmt.Println(encodeCertName)

	}

	{

		var FavoritedAt jsontime.JsonTime
		fmt.Println(FavoritedAt)
		// Initialize Redis
		if err := common.InitRedisClient(); err != nil {
			common.FatalLog("failed to initialize Redis: " + err.Error())
		}

		current := int64(0)
		total := int64(100)
		percent := float64(0)
		if total > 0 {
			percent = float64(current) / float64(total)
		}
		taskProgress := tasklog.TaskProgress{
			Status:      "DockerRun",
			CoverStatus: "DockerRun",
			Current:     current,
			Total:       total,
			Percent:     percent,
		}
		//txt := fmt.Sprintf("Progress:%.2f%%  %d/%d", percent*100, current, total)

		ctx := context.Background()
		if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, "fb00211ab060459f9d601edb171c3392"); err != nil {
			logger.Error(err)
		} else {
			ctx = context.WithValue(ctx, "logkey", logKey)
			ctx = context.WithValue(ctx, "task", tasklog.TaskEnum.StartupMark)
		}
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动中", "3秒输出一次日志", taskProgress)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动中", "3秒输出一次日志", taskProgress)
		list, _ := tasklog.List(tasklog.GetLogKey(ctx))
		fmt.Println(list)

	}
	{
		if err := model.InitDB(); err != nil {
			common.FatalLog("failed to initialize database: " + err.Error())
		}
		//var podImage model.PodImage
		//err11 := podImage.Transfer(1, 12)
		//logger.Info(err11)
		return
	}

	{
		regularTime := "2024-07-22 12:53:44"

		var defaultTime = time.Date(2024, time.January, 22, 12, 53, 44, 0, time.Local)
		fmt.Println(defaultTime)

		if tmpTime, err := time.ParseInLocation(jsontime.TimeFormat, regularTime, time.Local); err != nil {

			return
		} else {
			fmt.Println(tmpTime)
		}
		// 将时间转换为标准时区
		ShanghaiLocal, err := time.LoadLocation("Asia/Shanghai")
		if err != nil {
			fmt.Println("加载上海时区时发生错误:", err)
		}

		if tmpTime, err := time.ParseInLocation(jsontime.TimeFormat, "1901-01-01 00:00:00", ShanghaiLocal); err != nil {

			return
		} else {
			fmt.Println(tmpTime)
		}

		if tmpTime, err := time.ParseInLocation(jsontime.TimeFormat, regularTime, ShanghaiLocal); err != nil {

			return
		} else {
			fmt.Println(tmpTime)
		}

		if tmpTime, err := time.ParseInLocation(jsontime.TimeFormat, regularTime, time.Local); err != nil {

			return
		} else {
			fmt.Println(tmpTime)
		}

	}

	fmt.Println(common.DefaultTime)
	fmt.Println(common.NationalDay)
	fmt.Println(jsontime.JsonTime(common.DefaultTime))

	//public/4fe2ef0462a043a6ada19f0389190088
	var hub structs.HubRepositorie
	if err := service.GetHubImage(enums.ImageTypeEnum.Public, "4fe2ef0462a043a6ada19f0389190088", "v1.1", &hub); err != nil {
		logger.Error(err)
	}
	fmt.Println(hub)
	arr := make([]structs.HubRepositorie, 0)
	//if err := utils.GetStructAryFromJson(&arr, artifacts); err != nil {
	//	logger.Error(err)
	//}
	fmt.Println(arr)

	value := decimal.NewFromFloat(123.456789)
	msg := fmt.Sprintf("您当前的余额为%s，Pod启动需要至少预存一个小时的金额，用于支付超出的Pod服务费、镜像存储费、算力等", value.Truncate(2).String())
	logger.Error(msg)

	service.SkService.Delete("testsk")

	gups := make([]int, 0)
	ary1 := strings.Split("", "_")
	for _, v := range ary1 {
		gups = append(gups, utils.String2Int(v))
	}

	queryParm := make(map[string]interface{})

	queryParm["id"] = uint(23)

	fmt.Println(fmt.Sprintf("%%|%d|%%", queryParm["id"].(uint)))

	if err := model.InitDB(); err != nil {
		common.FatalLog("failed to initialize database: " + err.Error())
	}
	aaa()
	asetest()

	if utils.ValidCouponString("WEIW66", 6) == false {
		logger.Error("ss")
		return
	}

	var taskLog tasklog.TaskLogItem
	tt := `{"msg":"镜像提交中","state":"progress","done":false,"log":"接收到提交镜像指令 imageID:206","time":"2024-05-27 18:12:51"}`
	if err := utils.GetStructFromJson(&taskLog, tt); err != nil {
		logger.Error(err)
	}
	fmt.Println(taskLog)
	str := `{"code":0,"msg":"重启成功","result":{}}`

	var ginH gin.H
	err := json.Unmarshal([]byte(str), &ginH)
	if err != nil {
		logger.Error(err, str)
	}

	fmt.Println(utils.IsVersion("sdfdf-"))
	fmt.Println(utils.IsVersion(".sdfdf"))
	fmt.Println(utils.IsVersion("-sdfdf"))
	fmt.Println(utils.IsVersion("sdfdf"))
	fmt.Println(utils.IsVersion("V1.3"))
	artifacts := `[{
	"accessories": null,
	"addition_links": {
		"build_history": {
			"absolute": false,
			"href": "/api/v2.0/projects/private/repositories/02f02c53c2db4f74bfb1b80305c44efc/artifacts/sha256:2ffb0b30212a7bb6156074274e4a418d9a03a3fa94d7dd7d9808158ee5b7929e/additions/build_history"
		}
	},
	"digest": "sha256:2ffb0b30212a7bb6156074274e4a418d9a03a3fa94d7dd7d9808158ee5b7929e",
	"extra_attrs": {
		"architecture": "amd64",
		"author": "NVIDIA CORPORATION <<EMAIL>>",
		"config": {
			"Cmd": ["/root/start.sh"],
			"Entrypoint": ["/opt/nvidia/nvidia_entrypoint.sh"],
			"Env": ["gpu_memory_utilization=0.8", "max_model_len=1024", "MODEL=Qwen/Qwen1.5-7B-Chat", "PASSWORD=123456", "pd=123456", "PATH=/usr/local/nvidia/bin:/usr/local/cuda/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin", "NVARCH=x86_64", "NVIDIA_REQUIRE_CUDA=cuda>=12.1 brand=tesla,driver>=470,driver<471 brand=unknown,driver>=470,driver<471 brand=nvidia,driver>=470,driver<471 brand=nvidiartx,driver>=470,driver<471 brand=geforce,driver>=470,driver<471 brand=geforcertx,driver>=470,driver<471 brand=quadro,driver>=470,driver<471 brand=quadrortx,driver>=470,driver<471 brand=titan,driver>=470,driver<471 brand=titanrtx,driver>=470,driver<471 brand=tesla,driver>=525,driver<526 brand=unknown,driver>=525,driver<526 brand=nvidia,driver>=525,driver<526 brand=nvidiartx,driver>=525,driver<526 brand=geforce,driver>=525,driver<526 brand=geforcertx,driver>=525,driver<526 brand=quadro,driver>=525,driver<526 brand=quadrortx,driver>=525,driver<526 brand=titan,driver>=525,driver<526 brand=titanrtx,driver>=525,driver<526", "NV_CUDA_CUDART_VERSION=12.1.55-1", "NV_CUDA_COMPAT_PACKAGE=cuda-compat-12-1", "CUDA_VERSION=12.1.0", "LD_LIBRARY_PATH=/usr/local/nvidia/lib:/usr/local/nvidia/lib64", "NVIDIA_VISIBLE_DEVICES=all", "NVIDIA_DRIVER_CAPABILITIES=compute,utility", "NV_CUDA_LIB_VERSION=12.1.0-1", "NV_NVTX_VERSION=12.1.66-1", "NV_LIBNPP_VERSION=12.0.2.50-1", "NV_LIBNPP_PACKAGE=libnpp-12-1=12.0.2.50-1", "NV_LIBCUSPARSE_VERSION=12.0.2.55-1", "NV_LIBCUBLAS_PACKAGE_NAME=libcublas-12-1", "NV_LIBCUBLAS_VERSION=12.1.0.26-1", "NV_LIBCUBLAS_PACKAGE=libcublas-12-1=12.1.0.26-1", "NV_LIBNCCL_PACKAGE_NAME=libnccl2", "NV_LIBNCCL_PACKAGE_VERSION=2.17.1-1", "NCCL_VERSION=2.17.1-1", "NV_LIBNCCL_PACKAGE=libnccl2=2.17.1-1+cuda12.1", "NVIDIA_PRODUCT_NAME=CUDA", "NVIDIA_CUDA_END_OF_LIFE=1", "NV_CUDA_CUDART_DEV_VERSION=12.1.55-1", "NV_NVML_DEV_VERSION=12.1.55-1", "NV_LIBCUSPARSE_DEV_VERSION=12.0.2.55-1", "NV_LIBNPP_DEV_VERSION=12.0.2.50-1", "NV_LIBNPP_DEV_PACKAGE=libnpp-dev-12-1=12.0.2.50-1", "NV_LIBCUBLAS_DEV_VERSION=12.1.0.26-1", "NV_LIBCUBLAS_DEV_PACKAGE_NAME=libcublas-dev-12-1", "NV_LIBCUBLAS_DEV_PACKAGE=libcublas-dev-12-1=12.1.0.26-1", "NV_CUDA_NSIGHT_COMPUTE_VERSION=12.1.0-1", "NV_CUDA_NSIGHT_COMPUTE_DEV_PACKAGE=cuda-nsight-compute-12-1=12.1.0-1", "NV_NVPROF_VERSION=12.1.55-1", "NV_NVPROF_DEV_PACKAGE=cuda-nvprof-12-1=12.1.55-1", "NV_LIBNCCL_DEV_PACKAGE_NAME=libnccl-dev", "NV_LIBNCCL_DEV_PACKAGE_VERSION=2.17.1-1", "NV_LIBNCCL_DEV_PACKAGE=libnccl-dev=2.17.1-1+cuda12.1", "LIBRARY_PATH=/usr/local/cuda/lib64/stubs", "NV_CUDNN_VERSION=*********", "NV_CUDNN_PACKAGE_NAME=libcudnn8", "NV_CUDNN_PACKAGE=libcudnn8=*********-1+cuda12.1", "NV_CUDNN_PACKAGE_DEV=libcudnn8-dev=*********-1+cuda12.1"],
			"ExposedPorts": {
				"22/tcp": {},
				"3000/tcp": {},
				"8000/tcp": {}
			},
			"Labels": {
				"com.nvidia.cudnn.version": "*********",
				"gpus": "0",
				"host": "**************",
				"host_port": "**************:22",
				"instance_uuid": "02f02c53c2db4f74bfb1b80305c44efc",
				"maintainer": "NVIDIA CORPORATION <<EMAIL>>",
				"map_ports": "88_89",
				"org.opencontainers.image.ref.name": "ubuntu",
				"org.opencontainers.image.version": "22.04",
				"pod_category": "2",
				"pod_id": "30",
				"ssh_port": "10022",
				"startup_mark": "5d25659397254410a656fe3da74995fb",
				"startup_parm": "{\"user_id\":2}"
			},
			"WorkingDir": "/app/backend"
		},
		"created": "2024-05-08T13:23:26.689005246Z",
		"os": "linux"
	},
	"icon": "sha256:0048162a053eef4d4ce3fe7518615bef084403614f8bca43b40ae2e762e11e06",
	"id": 189,
	"labels": null,
	"manifest_media_type": "application/vnd.docker.distribution.manifest.v2+json",
	"media_type": "application/vnd.docker.container.image.v1+json",
	"project_id": 5,
	"pull_time": "0001-01-01T00:00:00.000Z",
	"push_time": "2024-05-08T13:23:27.968Z",
	"references": null,
	"repository_id": 51,
	"size": 13025230928,
	"tags": null,
	"type": "IMAGE"
}]`
	if artifacts != "" {

	}

	mapName := make(map[string]interface{})
	mapName["aa"] = 5
	//val, ok := mapName["aa"]
	fmt.Println(reflect.TypeOf(mapName["aa"]))

	// Initialize Redis
	if err := common.InitRedisClient(); err != nil {
		common.FatalLog("failed to initialize Redis: " + err.Error())
	}
	service.StartupLog.Save("asdfasfdasdfasfasfd", "sdfsdfsd", "sadfsfasfd")
	service.StartupLog.Save("asdfasfdasdfasfasfd", "sdfsdfsd", "sadfsfasfd")
	service.StartupLog.Save("asdfasfdasdfasfasfd", "sdfsdfsd", "sadfsfasfd")

	service.StartupLog.ScanAndHandleKeys()

	startupMaps := `[{"title":"WebUI","url":"https://dcb6da03381c4746ade7fc715dcfc23688.hz01.suanyun.cn"},{"title":"Juypter","url":"https://dcb6da03381c4746ade7fc715dcfc23689.hz01.suanyun.cn/lab?token=Zeyun1234"}]`
	if startupMaps != "" {

		arrKey := make([]string, 0)
		arr := utils.GetMapAryFromJson(startupMaps)
		if arr != nil {
			for _, mm := range arr {
				if _, ok := mm["url"]; ok {
					//存在
					url := mm["url"].(string)
					url = strings.Replace(url, "https://", "", -1)
					tmpAry := strings.Split(url, ".")
					if len(tmpAry) > 0 && len(tmpAry[0]) > 16 {
						arrKey = append(arrKey, tmpAry[0])
					}
				}
			}
		}
		println(arrKey)
	}
}

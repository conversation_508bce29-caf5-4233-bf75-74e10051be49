package structs

type PodAuditContent struct {
	Title    string `json:"title,omitempty"`
	Desc     string `json:"desc,omitempty"`
	Markdown string `json:"markdown,omitempty"`
	//NeedMemory    int    `json:"need_memory"`
	//NeedGpus      int    `json:"need_gpus"`
	//StartupElapse int    `json:"startup_elapse"`
	//PortMaps      string `json:"port_maps"`
	Cover        string `json:"cover,omitempty"`
	Logo         string `json:"logo,omitempty"`
	RejectReason string `json:"reject_reason,omitempty"`
}

//
//func (o PodAuditContent) Load(pod *model.Pod) {
//	pod.Title = o.Title
//	pod.Desc = o.Desc
//	pod.Markdown = o.Markdown
//	//pod.NeedMemory = o.NeedMemory
//	//pod.NeedGpus = o.NeedGpus
//	//pod.StartupElapse = o.StartupElapse
//	//pod.PortMaps = o.PortMaps
//	pod.Cover = o.Cover
//	pod.Logo = o.Logo
//}

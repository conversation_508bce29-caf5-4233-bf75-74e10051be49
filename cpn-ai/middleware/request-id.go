package middleware

import (
	"context"
	"cpn-ai/common"
	"cpn-ai/common/utils"
	"github.com/gin-gonic/gin"
)

func RequestId() func(c *gin.Context) {
	return func(c *gin.Context) {
		id := utils.GetTimeString() + utils.GetRandomString(8)
		c.Set(common.RequestIdKey, id)
		ctx := context.WithValue(c.Request.Context(), common.RequestIdKey, id)
		c.Request = c.Request.WithContext(ctx)
		c.<PERSON>(common.RequestIdKey, id)
		c.Next()
	}
}

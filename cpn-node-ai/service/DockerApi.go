package service

import (
	"context"
	"fmt"

	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/client"
)

func TestPull() {
	// 创建 Docker API 客户端
	cli, err := client.NewClientWithOpts(client.FromEnv, client.WithAPIVersionNegotiation())
	if err != nil {
		panic(err)
	}

	// 设置 Docker 上下文
	ctx := context.Background()

	// 拉取镜像
	imageName := "hub.suanyun.cn/suanyun-dev/cuda:11.8.0-cudnn8-devel-ubuntu22.04-02-jupyter"
	fmt.Printf("Pulling image %s...\n", imageName)
	reader, err := cli.ImagePull(ctx, imageName, image.PullOptions{})
	if err != nil {
		panic(err)
	}
	defer reader.Close()

	//// 获取终端信息
	//termFd, isTerm := term.GetFdInfo(os.Stdout)
	//jsonmessage.DisplayJSONMessagesStream(reader, os.<PERSON>do<PERSON>, termFd, isTerm, nil)
}

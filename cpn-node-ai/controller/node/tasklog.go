package node

import (
	"cpn-ai/common/logger"
	"cpn-ai/service/tasklog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
)

type taskLogApi_ struct {
}

var TaskLogApi taskLogApi_

type taskLogReq struct {
	LogKey string `json:"log_key"`
}

func (obj taskLogApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq taskLogReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Log<PERSON>ey == "" {
		msg = "参数错误"
		return
	}

	if ary, err := tasklog.List(oReq.LogKey); err != nil {
		msg = "获取失败"
		logger.Error(msg, err)
		return
	} else {
		result["tasklog"] = ary
		code = 0
	}
}

func (obj taskLogApi_) Last(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq taskLogReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.LogKey == "" {
		msg = "参数错误"
		return
	}
	ary := make([]tasklog.TaskLogItem, 0)
	if item, l, err := tasklog.Last(oReq.LogKey); err != nil {
		if err == gorm.ErrEmptySlice {
			result["tasklog"] = ary
			result["len"] = l
			code = 0
			return
		}
		msg = "获取失败"
		logger.Error(msg, err)
		return
	} else {
		ary = append(ary, item)
		result["tasklog"] = ary
		result["len"] = l
		code = 0
	}
}

func (obj taskLogApi_) BootIn(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq taskLogReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.LogKey == "" {
		msg = "参数错误"
		return
	}
	if booting, err := tasklog.BootIn(oReq.LogKey); err != nil {
		msg = err.Error()
		return
	} else {
		result["boot_in"] = booting
		result["log_key"] = oReq.LogKey
		code = 0
	}
}

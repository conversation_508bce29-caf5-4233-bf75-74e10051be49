package node

import (
	"bytes"
	"context"
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/service"
	"cpn-ai/service/tasklog"
	"cpn-ai/structs"
	"fmt"
	"io"
	"net/http"
	"runtime"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type dockerApi_ struct {
	//CancelFuncs map[string]context.CancelFunc
	CancelFuncs sync.Map
}

var DockerApi dockerApi_

type startupReq struct {
	FirstVirtualId uint                `json:"first_virtual_id"` //指定虚拟机ID
	VirtualId      uint                `json:"virtual_id"`       //指定虚拟机ID
	PodId          uint                `json:"pod_id"`
	ImageId        uint                `json:"image_id"`
	InstanceUuid   string              `json:"instance_uuid"`
	GpuModelId     uint                `json:"gpu_model_id"`
	NeedGpus       int                 `json:"need_gpus"`
	UserPath       string              `json:"user_path"`
	StartupParm    structs.StartupParm `json:"startup_parm"`
}

type startupAbortReq struct {
	VirtualId   uint   `json:"virtual_id"`
	StartupMark string `json:"startup_mark"`
}

type loopStartupReq struct {
	VirtualId    uint   `json:"virtual_id"` //指定虚拟机ID
	InstanceUuid string `json:"instance_uuid"`
	StartupMark  string `json:"startup_mark"`
}

type shutdownReq struct {
	VirtualId    uint     `json:"virtual_id"` //指定虚拟机ID
	StartupMark  string   `json:"startup_mark"`
	InstanceUuid string   `json:"instance_uuid"`
	Task         string   `json:"task"`
	RemoveKeys   []string `json:"remove_keys"`
	Force        bool     `json:"force"`
	Destroy      bool     `json:"destroy"`
	SaveImageId  uint     `json:"save_image_id"`
	StorageMode  int      `json:"storage_mode"`
}

type saveImageReq struct {
	VirtualId   uint   `json:"virtual_id"` //指定虚拟机ID
	StartupMark string `json:"startup_mark"`
	NewImageId  uint   `json:"new_image_id"`
	StorageMode int    `json:"storage_mode"`
	Task        string `json:"task"`
	Shutdown    bool   `json:"shutdown"`
	UserPath    string `json:"user_path"`
	//SaveImagePath string `json:"save_image_path"`
}

type commitImageReq struct {
	VirtualId   uint   `json:"virtual_id"` //指定虚拟机ID
	StartupMark string `json:"startup_mark"`
	NewImageId  uint   `json:"new_image_id"`
	Shutdown    bool   `json:"shutdown"`
	Restart     bool   `json:"restart"`
	UserPath    string `json:"user_path"`
}

type pullImageReq struct {
	VirtualId uint `json:"virtual_id"` //指定虚拟机ID
	ImageId   uint `json:"image_id"`
}

type runCommandReq struct {
	PodId        uint                `json:"pod_id"`
	ImageId      uint                `json:"image_id"`
	NeedGpus     int                 `json:"need_gpus"`
	UserDataPath string              `json:"user_data_path"`
	StartupParm  structs.StartupParm `json:"startup_parm"`
}

type dockerStateReq struct {
	StartupMark string `json:"startup_mark"`
	All         bool   `json:"all"`
}

type setNginxReq struct {
	StartupMarkKey string `json:"startup_mark_key"`
}

type dockerDetailReq struct {
	VirtualId   uint   `json:"virtual_id"`
	StartupMark string `json:"startup_mark"`
}

type dockerApiTestReq struct {
	ApiName     string `json:"api_name"`
	VirtualId   uint   `json:"virtual_id"`
	StartupMark string `json:"startup_mark"`
	DockerId    string `json:"docker_id"`
}

func (obj *dockerApi_) SpellRunCommand(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq runCommandReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.NeedGpus <= 0 {
		oReq.NeedGpus = 1
	}

	if err := service.NodeService.LoadPods(oReq.PodId); err != nil {
		logger.Error(err, oReq)
	}

	var pod service.PodItem
	if podValue, ok := service.NodeService.Pods.Load(oReq.PodId); !ok {
		//if pod, ok := service.NodeService.Pods[oReq.PodId]; !ok {
		msg = "Pod不存在"
		logger.Error(msg, oReq)
		return
	} else {
		pod = podValue.(service.PodItem)
		if oReq.NeedGpus < pod.NeedGpus {
			oReq.NeedGpus = pod.NeedGpus
		}
	}
	//pod := service.NodeService.Pods[oReq.PodId]

	if oReq.ImageId > 0 {
		if imageValue, ok := service.NodeService.PodImages.Load(oReq.ImageId); !ok {
			//if podImage, ok := service.NodeService.PodImages[oReq.ImageId]; !ok {
			msg = "PodImage不存在"
			logger.Error(msg, oReq)
			return
		} else {
			pod.PodImage = imageValue.(service.PodImageItem)
		}
	}

	instanceUuid := utils.GetUUID()
	startupMark := utils.GetUUID()

	var virtual service.Virtual

	for _, val := range service.NodeService.GetVirtuals() {
		if val.ID == 23 {
			virtual = val
			break
		}
	}
	if virtual.ID == 0 {
		msg = "没有找到虚拟机"
		logger.Error(msg, oReq)
		return
	}

	//gpus := virtual.Gpus
	//for i := 0; i < len(gpus) && i < oReq.NeedGpus; i++ {
	//	gpus[i].Status = enums.GpuStatusEnum.Locked
	//	gpus[i].StartupMark = startupMark
	//}
	//virtual.Gpus = gpus
	if err := virtual.LockGpus(nil, startupMark, oReq.NeedGpus); err != nil {
		msg = "显卡锁定失败"
		logger.Error(err)
		return
	}
	logger.Info(utils.GetJsonFromStruct(virtual))
	if pod.Command != "" {
		if command, err := virtual.SpellRunDockerCommand(pod, oReq.NeedGpus, instanceUuid, startupMark, oReq.UserDataPath, oReq.StartupParm); err != nil {
			msg = "拼装启动命令失败"
			logger.Error(msg, err)
			result["command_err"] = err.Error()
		} else {
			code = 0
			result["command"] = command
		}
	}

	if command, err := virtual.SpellRunDockerProCommand(nil, instanceUuid, startupMark, pod, oReq.NeedGpus, oReq.UserDataPath, oReq.StartupParm); err != nil {
		msg = "拼装启动pro命令失败"
		logger.Error(msg, err)
		result["command_pro_err"] = err.Error()
	} else {
		code = 0
		result["command_pro"] = command
	}

}

func (obj *dockerApi_) Detail(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq dockerDetailReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	//code 不为0不知道什么情况  code为0
	if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
		if !virtual.Inited() {
			result["state"] = "virtual_not_inited"
			msg = "虚拟机未初始化，请稍后再试"
			return
		}
		for _, docker := range virtual.GetDockers() {
			if docker.StartupMark == oReq.StartupMark {
				//var tmp dockerDetailReq
				//if err := utils.Scan(docker, &tmp); err != nil {
				//	msg = "Docker数据转换失败"
				//	logger.Error(err)
				//} else {
				//	result["docker"] = tmp
				//	code = 0
				//}
				//logger.Info("docker信息：", docker)
				tmp := structs.DockerDetailResp{
					ID:           docker.ID,
					PodId:        docker.PodId,
					VirtualId:    docker.VirtualId,
					InstanceUuid: docker.InstanceUuid,
					StartupMark:  docker.StartupMark,
					Gpus:         docker.Gpus,
					MapPorts:     docker.MapPorts,
					WebUrl:       docker.WebUrl,
					ApiBase:      docker.ApiBase,
					CreatedAt:    docker.CreatedAt,
					State:        docker.State,
				}
				result["docker"] = tmp
				result["state"] = docker.State
				code = 0
				return
			}
		}
		code = 0
		result["state"] = enums.DockerStatusEnum.NotExist
		msg = "Docker不存在"
		logger.Error(msg, oReq)
		return
	} else {
		code = 0
		result["state"] = "virtual_not_exist"
		msg = "虚拟机不存在"
		logger.Error(msg, oReq)
		return
	}
}

func (obj *dockerApi_) ApiTest(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq dockerApiTestReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	ctx, _ := context.WithCancel(context.Background())

	//code 不为0不知道什么情况  code为0
	if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
		if !virtual.Inited() {
			result["state"] = "virtual_not_inited"
			msg = "虚拟机未初始化，请稍后再试"
			return
		}

		if _, err := virtual.SshDial(); err != nil {
			msg = "Ssh连接失败"
			return
		}
		defer virtual.CloseSsh("DockerApiTest")

		if _, err := virtual.NewDockerClient(); err != nil {
			msg = "Tcp连接失败"
			return
		}
		result["oreq"] = utils.GetJsonFromStruct(oReq)
		if oReq.ApiName == "ContainerListUseApi" {
			if containers, err := virtual.ContainerListUseApi(ctx, oReq.DockerId, oReq.StartupMark); err != nil {
				//tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Fail, "容器停止失败", fmt.Sprintf("dockerId:%s", dockerId), nil)
				result["err"] = err.Error()
				logger.Error(err)
			} else {
				result["containers"] = containers
				result["success"] = true
				//tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器已停止，开始提交", fmt.Sprintf("dockerId:%s", dockerId), nil)
			}
		} else {
			msg = "没有匹配到测试函数"
		}

		code = 0
		return
	} else {
		result["state"] = "virtual_not_exist"
		msg = "虚拟机不存在"
		logger.Error(msg, oReq)
		return
	}
}

func (obj *dockerApi_) Status(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq dockerStateReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if len(oReq.StartupMark) < 10 {
		msg = "参数错误"
		logger.Error(msg, oReq)
		return
	}
	if oReq.All {
		if log, err := service.StartupLog.List(oReq.StartupMark); err != nil {
			msg = "获取启动日志失败"
			logger.Error(msg, oReq, err)
			return
		} else {
			result["startup_log"] = log
		}
	} else {
		if log, err := service.StartupLog.Last(oReq.StartupMark); err != nil {
			msg = "获取启动日志失败"
			logger.Error(msg, oReq, err)
			return
		} else {
			result["startup_log"] = log
		}
	}
	msg = ""
	code = 0
}

func (obj *dockerApi_) StartupLog(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq dockerStateReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if len(oReq.StartupMark) < 10 {
		msg = "参数错误"
		logger.Error(msg, oReq)
		return
	}
	if oReq.All {
		if log, err := service.StartupLog.List(oReq.StartupMark); err != nil {
			msg = "获取启动日志失败"
			logger.Error(msg, oReq, err)
			return
		} else {
			result["startup_log"] = log
		}
	} else {
		if log, err := service.StartupLog.Last(oReq.StartupMark); err != nil {
			msg = "获取启动日志失败"
			logger.Error(msg, oReq, err)
			return
		} else {
			result["startup_log"] = log
		}
	}
	msg = ""
	code = 0
}

func (obj *dockerApi_) Startup(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq startupReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.InstanceUuid == "" {
		msg = "实例为空"
		return
	}

	ctx := context.Background()
	if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupInstance, oReq.InstanceUuid); err != nil {
		logger.Error(err)
	} else {
		ctx = context.WithValue(ctx, "logkey", logKey)
		ctx = context.WithValue(ctx, "task", tasklog.TaskEnum.StartupInstance)
		//tasklog.Delete(logKey)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "接收到启动指令", "节点实例日志开始", nil)
	}
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "接收到启动指令", utils.GetJsonFromStruct(oReq), nil)

	//if _, ok := service.NodeService.Pods[oReq.PodId]; !ok {
	if err := service.NodeService.LoadPods(oReq.PodId); err != nil {
		logger.Error(err, oReq)
	}
	//}

	if oReq.ImageId > 0 {
		if err := service.NodeService.LoadPodImages(oReq.ImageId); err != nil {
			msg = "载入镜像数据失败"
			logger.Error(msg, err, utils.GetJsonFromStruct(oReq))
			return
		}
	}

	//if pod, ok := service.NodeService.Pods[oReq.PodId]; !ok {
	//	msg = "Pod不存在"
	//	logger.Error(msg, oReq)
	//	return
	//} else {
	//	//if oReq.NeedGpus < pod.NeedGpus {
	//	//	oReq.NeedGpus = pod.NeedGpus
	//	//}
	//}

	//startupKey := "inst:" + oReq.InstanceUuid
	needGpus := oReq.NeedGpus
	if oReq.FirstVirtualId > 0 {
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("尝试启动FirstVirtualId:%d needGpus:%d", oReq.FirstVirtualId, needGpus), nil)
		if virtualId, startupMark, err := service.NodeService.Startup(ctx, oReq.InstanceUuid, oReq.PodId, oReq.ImageId, oReq.FirstVirtualId, oReq.GpuModelId, needGpus, oReq.UserPath, oReq.StartupParm); err != nil {
			msg = err.Error()
			logger.Error(err, oReq)
			if len(startupMark) == 32 {
				result["startup_mark"] = startupMark
			}
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("尝试启动FirstVirtualId:%d失败 err:%s", oReq.FirstVirtualId, msg), nil)
		} else {
			if virtual, ok := service.NodeService.GetVirtual(virtualId); ok {
				if lockGpus, err := virtual.GetLockGpuItem(startupMark); err != nil {
					logger.Error(err)
				} else {
					gpus := make([]int, 0)
					for _, gpu := range lockGpus {
						gpus = append(gpus, gpu.Index)
					}
					result["startup_gpus"] = gpus
				}
			}

			result["virtual_id"] = virtualId
			result["startup_mark"] = startupMark
			msg = "启动中"
			code = 0
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("尝试启动FirstVirtualId:%d成功 startupMark:%s", oReq.FirstVirtualId, startupMark), nil)
			if config.Env == enums.EnvEnum.DEV {
				go func() {
					time.Sleep(time.Second * 3)
					ginH, err := service.MasterService.ReportStartupSuccess(virtualId, startupMark)
					logger.Info("直接启动成功上报结果:", utils.GetJsonFromStruct(ginH), "   ", err)
				}()
			}
			return
		}
	}

	if virtualId, startupMark, err := service.NodeService.Startup(ctx, oReq.InstanceUuid, oReq.PodId, oReq.ImageId, oReq.VirtualId, oReq.GpuModelId, needGpus, oReq.UserPath, oReq.StartupParm); err != nil {
		msg = err.Error()
		logger.Error(err, oReq)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("启动失败:%s", msg), nil)
		return
	} else {
		if virtual, ok := service.NodeService.GetVirtual(virtualId); ok {
			if lockGpus, err := virtual.GetLockGpuItem(startupMark); err != nil {
				logger.Error(err)
			} else {
				gpus := make([]int, 0)
				for _, gpu := range lockGpus {
					gpus = append(gpus, gpu.Index)
				}
				result["startup_gpus"] = gpus
			}
		}
		result["virtual_id"] = virtualId
		result["startup_mark"] = startupMark
		msg = "启动中"
		code = 0
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("启动中 virtualId：%d startupMark:%s", virtualId, startupMark), nil)

		if config.Env == enums.EnvEnum.DEV {
			go func() {
				time.Sleep(time.Second * 3)

				ginH, err := service.MasterService.ReportStartupSuccess(virtualId, startupMark)
				logger.Info("直接启动成功上报结果:", utils.GetJsonFromStruct(ginH), "   ", err)

			}()
		}
	}

}

func (obj *dockerApi_) StartupAbort(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	lockKey := ""
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		if lockKey != "" {
			common.RedisUnLock(lockKey)
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq startupAbortReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	trace := "取消实例开机 oReq:" + utils.GetJsonFromStruct(oReq)
	logger.Info("接收到取消实例开机指令", trace)

	if oReq.StartupMark == "" {
		msg = "需要启动标记"
		logger.Error(msg, oReq)
		return
	}

	lockKey = enums.RedisKeyEnum.LockKey + "StartupAbort_" + oReq.StartupMark
	if common.RedisLock(lockKey, 1, 1000*10) {

	} else {
		lockKey = ""
		msg = "请勿频繁操作"
		return
	}
	ctx := context.Background()
	cancelKey := ""
	if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, oReq.StartupMark); err != nil {
		logger.Error(err)
		msg = err.Error()
		return
	} else {
		cancelKey = logKey
		ctx = context.WithValue(ctx, "logkey", logKey)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在取消本次开机", fmt.Sprintf("接收到取消Pod实例开机指令 startupMark:%s", oReq.StartupMark), nil)

		abortRedisKey := enums.RedisKeyEnum.LockKey + "StartupAbort:" + oReq.StartupMark
		if abortTime, _ := common.RedisGet(abortRedisKey); abortTime != "" {
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在取消本次开机", fmt.Sprintf("取消标记已经设置，本次不设置， startupMark:%s", oReq.StartupMark)+" abortTime:"+abortTime, nil)
			msg = "尝试取消中，请稍后"
			code = 0
			return
		}

		if err := common.RedisSet(abortRedisKey, time.Now().Format(common.TimeFormat), time.Hour*24); err != nil {
			msg = "设置取消标记失败，请重试"
			logger.Error(msg, err)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在取消本次开机", fmt.Sprintf("取消标记设置失败 startupMark:%s", oReq.StartupMark), nil)
			return
		} else {
			msg = "尝试取消中，请稍后"
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在取消本次开机", fmt.Sprintf("取消标记设置完成 startupMark:%s", oReq.StartupMark), nil)
			code = 0
		}

		if cancelFun, ok := service.NodeService.CancelFuncs.Load(cancelKey); ok {
			cancelFun.(context.CancelFunc)()
			//tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "已取消Pod实例开机", fmt.Sprintf("已取消Pod实例开机 startupMark:%s", oReq.StartupMark), nil)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("获取到取消句柄，并且已执行 startupMark:%s", oReq.StartupMark), nil)

			//msg = "已取消"

			/*
				if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
					if _, err = virtual.NewDockerClient(); err != nil {
						msg = "Tcp连接失败"
						logger.Error(trace, msg, err)
					}
					defer virtual.CloseSsh("StartupAbort")

					unLock := false
					if container, err := virtual.ContainerByStartupMarkUseApi(ctx, oReq.StartupMark); err != nil {
						if err != common.ErrRecordNotFound {
							tmpMsg := "确认容器是否已经停止 获取容器信息失败，不移除锁卡标记"
							logger.Error(tmpMsg, oReq.StartupMark)
							tasklog.Save(ctx, "", "", "取消Pod实例开机 "+tmpMsg+" err:"+err.Error(), nil)
						} else {
							tmpMsg := "确认容器是否已经停止 容器不存在，移除锁卡标记"
							logger.Info(tmpMsg, oReq.StartupMark)
							tasklog.Save(ctx, "", "", "取消Pod实例开机 "+tmpMsg, nil)
							unLock = true
						}
					} else {
						if container.State == enums.DockerStatusEnum.Running {
							tmpMsg := "确认容器是否已经停止 未停止,不移除锁卡标记"
							tasklog.Save(ctx, "", "", "取消Pod实例开机 "+tmpMsg, nil)
							logger.Info(tmpMsg, oReq.StartupMark)
						} else {
							tmpMsg := "确认容器是否已经停止 不在运行中,移除锁卡标记，当前状态：" + container.State
							tasklog.Save(ctx, "", "", "取消Pod实例开机 "+tmpMsg, nil)
							logger.Info(tmpMsg, oReq.StartupMark, "  当前容器状态：", container.State)
							unLock = true
						}
					}
					if unLock {
						if err := virtual.UnLockGpus(oReq.StartupMark, "StartupAbort"); err != nil {
							logger.Error(err, " virtualId:", oReq.VirtualId, "  startupMark:", oReq.StartupMark, "   dockerId:")
							tasklog.Save(ctx, "", "", "取消Pod实例开机 UnLockGpus失败 err:"+err.Error(), nil)
						} else {
							tasklog.Save(ctx, "", "", "取消Pod实例开机 UnLockGpus完成", nil)
						}
					}
				}*/
			code = 0
			return
		} else {
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("未获取到取消句柄startupMark:%s", oReq.StartupMark), nil)
			//if docker, err := service.NodeService.FindDocker(oReq.VirtualId, oReq.StartupMark); err != nil {
			//	msg = "查询容器出错，请重试"
			//	logger.Error(msg, oReq, err)
			//	return
			//} else if docker != nil {
			//	msg = "当前进度不支持取消"
			//	abortRedisKey := enums.RedisKeyEnum.LockKey + "StartupAbort:" + oReq.StartupMark
			//	if err := common.RedisSet(abortRedisKey, time.Now().Format(common.TimeFormat), time.Second*10); err != nil {
			//		msg = "设置取消标记失败，请重试"
			//		logger.Error(msg, err)
			//	} else {
			//		msg = "尝试取消中，请稍后"
			//		code = 0
			//	}
			//	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "Pod实例开机"+msg, fmt.Sprintf("Pod实例开机句柄不存在 startupMark:%s", oReq.StartupMark), nil)
			//	return
			//}
			//msg = "取消句柄不存在"
			return
		}
	}

}

func (obj *dockerApi_) LoopStartup(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq loopStartupReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.InstanceUuid == "" {
		msg = "实例为空"
		return
	}

	ctx := context.Background()
	if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupInstance, oReq.InstanceUuid); err != nil {
		logger.Error(err)
	} else {
		ctx = context.WithValue(ctx, "logkey", logKey)
		ctx = context.WithValue(ctx, "task", tasklog.TaskEnum.StartupInstance)
		tasklog.Delete(logKey)
	}
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "接收到LoopStartup指令", utils.GetJsonFromStruct(oReq), nil)

	//var appStartLoopItem service.AppStartLoopItem
	//elapse := 0
	//if err := appStartLoopItem.GetAppStartLoop(&appStartLoopItem, oReq.StartupMark); err == nil {
	//	elapse = appStartLoopItem.LeaveCount
	//} else {
	//	appStartLoopItem.SaveAppStartLoop(oReq.VirtualId, oReq.StartupMark, 1)
	//	service.NodeService.LoopAppStart(ctx, oReq.StartupMark, oReq.VirtualId)
	//}
}

func (obj *dockerApi_) Shutdown(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code >= 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	var oReq shutdownReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	logger.Info("shutdown参数：", utils.GetJsonFromStruct(oReq))
	if oReq.StartupMark == "" {
		msg = "参数错误"
		return
	}

	ctx := context.Background()
	if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, oReq.StartupMark); err != nil {
		logger.Error(err)
	} else {
		ctx = context.WithValue(ctx, "logkey", logKey)
		ctx = context.WithValue(ctx, "task", tasklog.TaskEnum.StartupMark)
		tasklog.Save(ctx, "", "", fmt.Sprintf("接收到关机指令 startupMark:%s", oReq.StartupMark), nil)
	}

	logger.Info("开始移除Docker VirtualId:", oReq.VirtualId, "   StartupMark:", oReq.StartupMark)

	docker, err := service.NodeService.FindDocker(oReq.VirtualId, oReq.StartupMark)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			msg = "记录不存在，做已移除处理"
			code = 0
			tasklog.Save(ctx, "", "", fmt.Sprintf("%s startupMark:%s", msg, oReq.StartupMark), nil)
			return
		}
		if _, ok := service.NodeService.GetVirtual(oReq.VirtualId); !ok {
			if oReq.Force {
				logger.Info("开始强制移除Docker VirtualId:", oReq.VirtualId, "   StartupMark:", oReq.StartupMark)
				msg = "虚拟机不存在，做成功移除处理"
				code = 0
				tasklog.Save(ctx, "", "", fmt.Sprintf("%s startupMark:%s", msg, oReq.StartupMark), nil)
				return
			}
		}
		msg = err.Error()
		logger.Error(msg, utils.GetJsonFromStruct(oReq), err)
		tasklog.Save(ctx, "", "", fmt.Sprintf("%s startupMark:%s", msg, oReq.StartupMark), nil)
		return
	} else {
		if docker == nil {
			msg = "已移除"
			code = 0
			tasklog.Save(ctx, "", "", fmt.Sprintf("容器%s startupMark:%s", msg, oReq.StartupMark), nil)
			return
		} else {
			tasklog.Save(ctx, "", "", fmt.Sprintf("%s startupMark:%s", "开始移除docker", oReq.StartupMark), nil)
			if b, err := service.NodeService.RmStopDockerTest(ctx, oReq.VirtualId, oReq.StartupMark, docker.ID, 1); err != nil {
				msg = "移除失败"
				tasklog.Save(ctx, "", "", fmt.Sprintf("容器停止%s startupMark:%s err:%s", msg, oReq.StartupMark, err.Error()), nil)
				logger.Error(msg, err, oReq)
				return
			} else {
				if oReq.SaveImageId > 0 {
					task := oReq.Task
					if task == "" {
						task = tasklog.TaskEnum.SaveInstanceImage
					}
					{
						lockKey := enums.RedisKeyEnum.LockKey + "ShutdownSaveImage_" + utils.Uint2String(oReq.SaveImageId)
						if common.RedisLock(lockKey, 1, 1000*20) {
							defer common.RedisUnLock(lockKey)

							if task == tasklog.TaskEnum.SaveInstanceImage {
								if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
									if err1 := virtual.SetCacheShutdownKeep(ctx, oReq.StartupMark, common.CacheShutdownKeepSeconds7Day); err1 != nil {
										logger.Error(err1, " ", oReq.StartupMark)
										tasklog.Save(ctx, "", "", fmt.Sprintf("%s startupMark:%s err:%s", "设置实例容器为缓存7天", oReq.StartupMark, err1.Error()), nil)
									} else {
										tasklog.Save(ctx, "", "", fmt.Sprintf("%s startupMark:%s ", "设置实例容器为缓存7天", oReq.StartupMark), nil)
									}
								}
							}
							logger.Info("调用SaveImage")
							tasklog.Save(ctx, "", "", fmt.Sprintf("%s startupMark:%s ", "调用SaveImage", oReq.StartupMark), nil)
							nReq := saveImageReq{
								VirtualId:   oReq.VirtualId,
								StartupMark: oReq.StartupMark,
								NewImageId:  oReq.SaveImageId,
								StorageMode: enums.ImageStorageModeEnum.Registry,
								Task:        task,
							}

							//postData["virtual_id"] = virtualId
							//postData["startup_mark"] = startupMark
							//postData["new_image_id"] = newImageId
							//postData["storage_mode"] = storageMode
							//postData["task"] = task
							//postData["user_path"] = userPath
							//postData["shutdown"] = shutdown

							jsonStr := utils.GetJsonFromStruct(nReq)
							c.Request.Body = io.NopCloser(bytes.NewBufferString(jsonStr))
							obj.SaveImage(c)
							code = -1
							return
						} else {
							msg = "实例保存中，请勿重复提交"
							logger.Error("实例保存中，请勿重复提交 ", fmt.Sprintf("oReq:%s", utils.GetJsonFromStruct(oReq)))
							tasklog.Save(ctx, "", "", fmt.Sprintf("%s startupMark:%s", msg, oReq.StartupMark), nil)
						}
					}
				}
				if b {
					code = 0
					msg += "移除成功"
					return
				} else {
					msg += "移除失败"
					return
				}
			}
		}

	}
}

func (obj *dockerApi_) Stop(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq shutdownReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	logger.Info("开始停止Docker VirtualId:", oReq.VirtualId, "   StartupMark:", oReq.StartupMark)
	docker, err := service.NodeService.FindDocker(oReq.VirtualId, oReq.StartupMark)
	if err != nil {
		msg = "查询出错"
		msg = "停止容器时获取容器信息失败"
		logger.Error(msg, oReq, err)
		return
	} else {
		if docker == nil {
			msg = "Docker不存在"
			return
		} else {
			if b, m, err := service.NodeService.StopDocker(oReq.VirtualId, docker.ID); err != nil {
				logger.Error(m, err, oReq)
				return
			} else {
				if b {
					code = 0
					msg = "停止成功"
					return
				} else {
					msg = m
					return
				}
			}
		}

	}
}

func (obj *dockerApi_) Start(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq shutdownReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	logger.Info("开始开启Docker VirtualId:", oReq.VirtualId, "   StartupMark:", oReq.StartupMark)
	docker, err := service.NodeService.FindDocker(oReq.VirtualId, oReq.StartupMark)
	if err != nil {
		msg = "查询出错"
		msg = "启动容器时获取容器信息失败"
		logger.Error(msg, oReq, err)
		return
	} else {
		if docker == nil {
			msg = "Docker不存在"
			return
		} else {
			if b, m, err := service.NodeService.StartDocker(oReq.VirtualId, docker.ID); err != nil {
				logger.Error(m, err, oReq)
				msg = m
				return
			} else {
				if b {
					code = 0
					msg = "开启成功"
					return
				} else {
					msg = m
					return
				}
			}
		}
	}
}

func (obj *dockerApi_) ReStart(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq shutdownReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	ctx := context.Background()
	if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, oReq.StartupMark); err != nil {
		logger.Error(err)
	} else {
		ctx = context.WithValue(ctx, "logkey", logKey)
		tasklog.Save(ctx, "", "正在重启容器", fmt.Sprintf("接收到重启指令 startupMark:%s", oReq.StartupMark), nil)
	}

	logger.Info("开始重启Docker VirtualId:", oReq.VirtualId, "   StartupMark:", oReq.StartupMark)
	//service.StartupLog.Save(oReq.StartupMark, fmt.Sprintf("正在重启容器"), fmt.Sprintf("virtualId: %d ", oReq.VirtualId))
	tasklog.Save(ctx, "", "正在重启容器", fmt.Sprintf("virtualId: %d ", oReq.VirtualId), nil)
	docker, err := service.NodeService.FindDocker(oReq.VirtualId, oReq.StartupMark)
	if err != nil {
		msg = "重启容器时获取容器信息失败"
		if err == gorm.ErrRecordNotFound {
			msg = "容器未在操作队列"
		}
		logger.Error(msg, oReq, err)
		//service.StartupLog.Save(oReq.StartupMark, fmt.Sprintf("正在重启容器"), fmt.Sprintf("查询Docker失败 msg:%s err:%s", msg, err.Error()))
		tasklog.Save(ctx, "", "正在重启容器", fmt.Sprintf("查询Docker失败 msg:%s err:%s", msg, err.Error()), nil)
		return
	} else {
		if docker == nil {
			msg = "Docker不存在"
			//service.StartupLog.Save(oReq.StartupMark, fmt.Sprintf("正在重启容器"), fmt.Sprintf("Docker不存在"))
			tasklog.Save(ctx, "", "正在重启容器", fmt.Sprintf("Docker不存在"), nil)
			return
		} else {
			if b, m, err := service.NodeService.ReStartDocker(oReq.VirtualId, docker.ID); err != nil {
				logger.Error(m, err, oReq)
				msg = m
				//service.StartupLog.Save(oReq.StartupMark, fmt.Sprintf("正在重启容器"), fmt.Sprintf("容器重启失败 msg:%s err:%s", msg, err.Error()))
				tasklog.Save(ctx, "", "正在重启容器", fmt.Sprintf("容器重启失败 msg:%s err:%s", msg, err.Error()), nil)
				return
			} else {
				if b {
					//service.StartupLog.Save(docker.StartupMark, fmt.Sprintf("容器重启成功"), "")
					tasklog.Save(ctx, "", "容器重启成功", "", nil)
					for i := 0; i < 3; i++ {
						if str, err := service.NginxAddInstance(docker.VirtualId, docker.InstanceUuid); err != nil {
							logger.Error(str, err)
							//service.StartupLog.Save(docker.StartupMark, fmt.Sprintf("正在重启应用"), fmt.Sprintf("第%d次映射地址失败 %s %s", i, str, err))
							tasklog.Save(ctx, "", "正在重启应用", fmt.Sprintf("第%d次映射地址失败 %s %s", i, str, err), nil)
						} else {
							//service.StartupLog.Save(docker.StartupMark, fmt.Sprintf("正在重启应用"), fmt.Sprintf("第%d次映射地址完成 %s", i, str))
							tasklog.Save(ctx, "", "正在重启应用", fmt.Sprintf("第%d次映射地址完成 %s", i, str), nil)
							break
						}
						time.Sleep(time.Second * 2)
					}
					code = 0
					msg = "容器重启成功，页面打开需要一点时间，请尝试刷新几次页面"
					return
				} else {
					//service.StartupLog.Save(docker.StartupMark, fmt.Sprintf("容器重启失败"), fmt.Sprintf("msg:%s err:%s", m, err.Error()))
					tasklog.Save(ctx, "", "容器重启失败", fmt.Sprintf("msg:%s err:%s", m, err.Error()), nil)
					msg = m
					return
				}
			}
		}
	}
}

func (obj *dockerApi_) Logs(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq shutdownReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	logger.Info("开始获取Docker日志 VirtualId:", oReq.VirtualId, "   StartupMark:", oReq.StartupMark)
	docker, err := service.NodeService.FindDocker(oReq.VirtualId, oReq.StartupMark)
	if err != nil {
		msg = "查询出错"
		msg = "获取容器日志时获取容器信息失败"
		logger.Error(msg, oReq, err)
		return
	} else {
		if docker == nil {
			msg = "Docker不存在"
			return
		} else {
			if b, m, err := service.NodeService.LogsDocker(oReq.VirtualId, docker.ID); err != nil {
				logger.Error(m, err, oReq)
				msg = m
				return
			} else {
				if b {
					code = 0
					result["logs"] = m
					msg = "日志获取成功"
					return
				} else {
					msg = "日志获取失败"
					result["logs"] = m
					return
				}
			}
		}
	}
}

func (obj *dockerApi_) SaveImage(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		if c == nil {
			logger.Error("Received nil context in SaveImage")
			return
		} else {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	showObjName := "镜像"

	var oReq saveImageReq
	if c == nil {
		logger.Error("Received nil context in SaveImage oReq")
		return
	}
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	logger.Info("接收到保存镜像指令 oReq:", utils.GetJsonFromStruct(oReq))

	if oReq.NewImageId == 0 {
		msg = "请先创建要保存的镜像Id"
		logger.Error(msg, oReq)
		return
	}
	if oReq.StorageMode == 0 {
		oReq.StorageMode = enums.ImageStorageModeEnum.Registry
	}
	ctx, cancel := context.WithCancel(context.Background())
	cancelKey := ""
	if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.SaveImage, utils.Uint2String(oReq.NewImageId)); err != nil {
		logger.Error(err)
	} else {
		cancelKey = logKey
		ctx = context.WithValue(ctx, "logkey", logKey)
		if oReq.Task == "" {
			if oReq.Shutdown == true {
				ctx = context.WithValue(ctx, "task", tasklog.TaskEnum.SaveImageAndShutdown)
			} else {
				ctx = context.WithValue(ctx, "task", tasklog.TaskEnum.SaveImage)
			}
		} else {
			ctx = context.WithValue(ctx, "task", oReq.Task)
			if oReq.Task == tasklog.TaskEnum.SaveInstanceImage {
				showObjName = "实例数据"
			}
		}

		if b, err := tasklog.DoneFive(logKey); err != nil {
			tasklog.Save(ctx, "", "检查重复保存"+showObjName+"失败", fmt.Sprintf("获取镜像日志失败 接收到保存镜像指令 oReq:%s", utils.GetJsonFromStruct(oReq)), nil)
			msg = "检查重复保存镜像出错"
			return
		} else {
			if b == false {
				tasklog.Save(ctx, "", "重复保存"+showObjName+"操作", fmt.Sprintf("5秒内有镜像保存日志 接收到保存镜像指令 oReq:%s", utils.GetJsonFromStruct(oReq)), nil)
				msg = "重复保存镜像操作"
				return
			}
		}
		logger.Info("删除logKey:", logKey)
		tasklog.Delete(logKey)
	}
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "开始保存"+showObjName, fmt.Sprintf("接收到保存镜像指令 oReq:%s", utils.GetJsonFromStruct(oReq)), nil)

	var imageMeta string

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			newImageId := oReq.NewImageId
			reportState := enums.ReportStateEnum.Fail
			task := ""
			if ctx != nil {
				if tmpTask, ok := ctx.Value("task").(string); ok {
					task = tmpTask
				}
			}
			if ginH, err := service.MasterService.ReportDockerPush(oReq.StartupMark, oReq.NewImageId, enums.ReportStateEnum.Fail, task, msg, imageMeta); err != nil {
				logger.Error("Push上报失败", err, "   imageId:", newImageId, "  reportState:", reportState)
				tasklog.Save(ctx, "", showObjName+"保存失败("+msg+")", "镜像保存结果上报出错 reportState:"+reportState+"  startupMark:"+oReq.StartupMark, nil)
			} else {
				logger.Info("Push上报完成", ginH, "   imageId:", newImageId, "  reportState:", reportState)
				tasklog.Save(ctx, "", showObjName+"保存失败("+msg+")", " 镜像保存结果上报成功 reportState:"+reportState+"  startupMark:"+oReq.StartupMark+" ginH:"+utils.GetJsonFromStruct(ginH), nil)
			}
		}
	}()

	if err := service.NodeService.LoadPodImages(oReq.NewImageId); err != nil {
		msg = "载入镜像数据失败"
		//service.StartupLog.Save(logKey, "保存镜像失败", fmt.Sprintf("载入镜像数据失败 imageID:%d", oReq.NewImageId))
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Fail, "保存"+showObjName+"失败", fmt.Sprintf("载入镜像数据失败 imageID:%d", oReq.NewImageId), nil)
		logger.Error(msg, err, oReq)
		return
	}

	var newImage service.PodImageItem
	if imageValue, ok := service.NodeService.PodImages.Load(oReq.NewImageId); !ok {
		//if tmpImage, ok := service.NodeService.PodImages[oReq.NewImageId]; !ok {
		msg = "镜像ID不存在"
		//service.StartupLog.Save(logKey, "保存镜像失败", fmt.Sprintf("镜像ID不存在 imageID:%d", oReq.NewImageId))
		tasklog.Save(ctx, tasklog.SaveImageAndShutdownStateEnum.Fail, "保存"+showObjName+"失败", fmt.Sprintf("镜像ID不存在 imageID:%d", oReq.NewImageId), nil)
		logger.Error(msg, oReq)
		return
	} else {
		newImage = imageValue.(service.PodImageItem)
	}

	//if oReq.SaveImagePath == "" {
	//	msg = "镜像路径为空"
	//	logger.Error(msg, utils.GetJsonFromStruct(oReq))
	//	return
	//}

	logger.Info("开始查找相应容器 oReq:", utils.GetJsonFromStruct(oReq))

	//docker, err := service.NodeService.FindDocker(oReq.VirtualId, oReq.StartupMark)
	docker, err := service.NodeService.FindContainer(ctx, oReq.VirtualId, oReq.StartupMark)
	if err != nil {
		msg = "查询出错"
		msg = "保存镜像时获取容器信息失败"
		tasklog.Save(ctx, tasklog.SaveImageAndShutdownStateEnum.Fail, "保存"+showObjName+"失败", fmt.Sprintf("查询Docker失败 imageID:%d  startupMark:%s err:%s", oReq.NewImageId, oReq.StartupMark, err.Error()), nil)
		logger.Error(msg, " oReq:", utils.GetJsonFromStruct(oReq), " err:", err)
		return
	} else {
		if docker == nil {
			tasklog.Save(ctx, tasklog.SaveImageAndShutdownStateEnum.Fail, "保存"+showObjName+"失败", fmt.Sprintf("Docker不存在 imageID:%d  startupMark:%s", oReq.NewImageId, oReq.StartupMark), nil)
			msg = "容器不存在"
			logger.Error(msg, " oReq:", utils.GetJsonFromStruct(oReq))
			return
		} else {
			go func() {
				lockKey := enums.RedisKeyEnum.LockKey + "SaveImage_" + utils.Uint2String(oReq.NewImageId)
				if common.RedisLock(lockKey, 1, 1000*20) {
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("获取到保存镜像锁 lockKey:%s", lockKey), nil)
					defer common.RedisUnLock(lockKey)
					obj.CancelFuncs.Store(cancelKey, cancel)
					defer obj.CancelFuncs.Delete(cancelKey)
					if err := service.NodeService.SaveDockerImage(ctx, *docker, newImage, oReq.StorageMode, oReq.UserPath, &imageMeta); err != nil {
						logger.Error(err, " oReq:", utils.GetJsonFromStruct(oReq))
						reason := tasklog.ScanErrReasonByCtx(ctx)
						if reason != "" {
							reason = "(" + reason + ")"
						}
						tasklog.Save(ctx, tasklog.SaveImageAndShutdownStateEnum.Fail, showObjName+"保存失败"+reason, fmt.Sprintf("oReq:%s", utils.GetJsonFromStruct(oReq)), nil)
						return
					} else {
						tasklog.Save(ctx, tasklog.SaveImageAndShutdownStateEnum.Success, showObjName+"保存成功", fmt.Sprintf("oReq:%s", utils.GetJsonFromStruct(oReq)), nil)
						return
					}
				} else {
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("重复提交，未获取到保存镜像锁 lockKey:%s", lockKey), nil)
					logger.Error("镜像保存中，请勿重复提交 ", fmt.Sprintf("oReq:%s", utils.GetJsonFromStruct(oReq)))
				}
			}()
			msg = showObjName + "保存中"
			code = 0
			return
		}
	}
}

func (obj *dockerApi_) SaveImageAbort(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq saveImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	logger.Info("接收到取消保存镜像指令 oReq:", utils.GetJsonFromStruct(oReq))

	if oReq.NewImageId == 0 {
		msg = "需要取消的镜像Id"
		logger.Error(msg, oReq)
		return
	}
	ctx := context.Background()
	cancelKey := ""
	if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.SaveImage, utils.Uint2String(oReq.NewImageId)); err != nil {
		logger.Error(err)
		msg = err.Error()
		return
	} else {
		cancelKey = logKey
		ctx = context.WithValue(ctx, "logkey", logKey)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "开始取消", fmt.Sprintf("接收到取消保存镜像指令 imageID:%d", oReq.NewImageId), nil)

		if cancelFun, ok := obj.CancelFuncs.Load(cancelKey); ok {
			cancelFun.(context.CancelFunc)()
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "已取消", fmt.Sprintf("已取消保存镜像 imageID:%d", oReq.NewImageId), nil)
			msg = "已取消"
			code = 0
			return
		} else {
			msg = "取消句柄不存在"
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "已取消", fmt.Sprintf("取消保存镜像句柄不存在 imageID:%d", oReq.NewImageId), nil)
			return
		}
	}

}

func (obj *dockerApi_) CommitImage(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq commitImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	//
	//defer func() {
	//	service.StartupLog.Save(fmt.Sprintf("SaveImage_%d", oReq.NewImageId), msg, "Node http接口返回")
	//}()

	ctx := context.Background()
	if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.CommitDocker, utils.Uint2String(oReq.NewImageId)); err != nil {
		logger.Error(err)
	} else {
		ctx = context.WithValue(ctx, "logkey", logKey)
		tasklog.Delete(logKey)
	}

	tasklog.Save(ctx, tasklog.CommitDockerTaskStateEnum.Progress, "镜像提交中", fmt.Sprintf("接收到提交镜像指令 imageID:%d", oReq.NewImageId), nil)
	if err := service.NodeService.LoadPodImages(oReq.NewImageId); err != nil {
		msg = "载入镜像数据失败"
		tasklog.Save(ctx, tasklog.CommitDockerTaskStateEnum.Fail, "镜像提交失败", fmt.Sprintf("载入镜像数据失败 imageID:%d", oReq.NewImageId), nil)
		logger.Error(msg, err, oReq)
		return
	}

	if _, ok := service.NodeService.PodImages.Load(oReq.NewImageId); !ok {
		//if _, ok := service.NodeService.PodImages[oReq.NewImageId]; !ok {
		msg = "镜像ID不存在"
		tasklog.Save(ctx, tasklog.CommitDockerTaskStateEnum.Fail, "镜像提交失败", fmt.Sprintf("镜像ID不存在 imageID:%d", oReq.NewImageId), nil)
		logger.Error(msg, oReq)
		return
	}

	logger.Info("开始重启Docker VirtualId:", oReq.VirtualId, "   StartupMark:", oReq.StartupMark)
	docker, err := service.NodeService.FindDocker(oReq.VirtualId, oReq.StartupMark)
	if err != nil {
		msg = "查询出错"
		msg = "提交容器时获取容器信息失败"
		tasklog.Save(ctx, tasklog.CommitDockerTaskStateEnum.Fail, "镜像提交失败", fmt.Sprintf("查询Docker失败 imageID:%d  startupMark:%s err:%s", oReq.NewImageId, oReq.StartupMark, err.Error()), nil)
		logger.Error(msg, oReq, err)
		return
	} else {
		if docker == nil {
			tasklog.Save(ctx, tasklog.CommitDockerTaskStateEnum.Fail, "镜像提交失败", fmt.Sprintf("Docker不存在 imageID:%d  startupMark:%s", oReq.NewImageId, oReq.StartupMark), nil)
			msg = "Docker不存在"
			return
		} else {
			go func() {
				lockKey := enums.RedisKeyEnum.LockKey + "SaveImage_" + utils.Uint2String(oReq.NewImageId)
				if common.RedisLock(lockKey, 1, 1000*60) {
					defer common.RedisUnLock(lockKey)
					if err := service.NodeService.CommitDockerImage(ctx, *docker, oReq.NewImageId, oReq.UserPath); err != nil {
						logger.Error(err, oReq)
						tasklog.Save(ctx, tasklog.CommitDockerTaskStateEnum.Fail, "镜像提交失败", fmt.Sprintf("err:%s imageID:%d  startupMark:%s", err.Error(), oReq.NewImageId, oReq.StartupMark), nil)
						return
					} else {
						code = 0
						msg = "镜像提交成功"
						tasklog.Save(ctx, tasklog.CommitDockerTaskStateEnum.Success, "镜像提交成功", fmt.Sprintf("sha256:%s imageID:%d  startupMark:%s", "", oReq.NewImageId, oReq.StartupMark), nil)
						return
					}
				} else {
					logger.Error("镜像提交中，请勿重复提交 imageId:", oReq.NewImageId)
					tasklog.Save(ctx, tasklog.CommitDockerTaskStateEnum.Progress, "镜像提交中，请勿重复提交", fmt.Sprintf("镜像提交中，请勿重复提交 imageID:%d  startupMark:%s", oReq.NewImageId, oReq.StartupMark), nil)
				}
			}()
			msg = "镜像提交中"
			code = 0
			return
		}
	}
}

func (obj *dockerApi_) PullImage(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq pullImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	ctx := context.Background()
	if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.PullImage, fmt.Sprintf("%d_%d", oReq.VirtualId, oReq.ImageId)); err != nil {
		logger.Error(err)
	} else {
		ctx = context.WithValue(ctx, "logkey", logKey)
		ctx = context.WithValue(ctx, "task", tasklog.TaskEnum.PullImage)
		tasklog.Delete(logKey)
	}

	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像拉取中", fmt.Sprintf("接收到提交镜像指令 imageID:%d", oReq.ImageId), nil)
	if err := service.NodeService.LoadPodImages(oReq.ImageId); err != nil {
		msg = "载入镜像数据失败"
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Fail, "镜像拉取失败", fmt.Sprintf("载入镜像数据失败 imageID:%d", oReq.ImageId), nil)
		logger.Error(msg, err, oReq)
		return
	}

	if _, ok := service.NodeService.PodImages.Load(oReq.ImageId); !ok {
		//if _, ok := service.NodeService.PodImages[oReq.ImageId]; !ok {
		msg = "镜像ID不存在"
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Fail, "镜像拉取失败", fmt.Sprintf("镜像ID不存在 imageID:%d", oReq.ImageId), nil)
		logger.Error(msg, oReq)
		return
	}

	if _, ok := service.NodeService.GetVirtual(oReq.VirtualId); !ok {
		msg = "虚拟机不存在"
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Fail, "镜像拉取失败", fmt.Sprintf("镜像ID不存在 imageID:%d", oReq.ImageId), nil)
		logger.Error(msg, oReq)
		return
	}

	//logger.Info("开始重启Docker VirtualId:", oReq.VirtualId, "   StartupMark:", oReq.StartupMark)
	go func() {
		if err := service.NodeService.PullImages(ctx, oReq.VirtualId, oReq.ImageId); err != nil {
			msg = "镜像拉取失败"
			tasklog.Save(ctx, tasklog.CommitDockerTaskStateEnum.Fail, "镜像拉取失败", fmt.Sprintf("镜像拉取失败 imageID:%d  virtualId:%d", oReq.ImageId, oReq.VirtualId), nil)
			logger.Error(msg, oReq, err)
		} else {
			tasklog.Save(ctx, tasklog.CommitDockerTaskStateEnum.Fail, "镜像拉取完成", fmt.Sprintf("镜像拉取完成 imageID:%d  virtualId:%d", oReq.ImageId, oReq.VirtualId), nil)
		}
	}()
	code = 0
	msg = "镜像拉取中"

}

func (obj *dockerApi_) SetNginx(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	//var oReq setNginxReq
	//if err := c.ShouldBindJSON(&oReq); err != nil {
	//	msg = "参数解析失败"
	//	logger.Error(msg, err)
	//	return
	//}

	startupMarkKey := c.Query("startup_mark_key")

	startupMarkK := ""
	if len(startupMarkKey) > 32 {
		startupMarkK = startupMarkKey[0:32]
	} else {
		startupMarkK = startupMarkKey
	}

	if startupMarkK == "" {
		msg = "参数错误"
		logger.Error(msg, startupMarkKey)
		return
	}

	var findDocker service.DockerItem
	for _, virtual := range service.NodeService.GetVirtuals() {
		for _, docker := range virtual.GetDockers() {

			if docker.StartupMark == startupMarkK {
				findDocker = docker
				break
			}
			if docker.InstanceUuid == startupMarkK {
				findDocker = docker
				break
			}
		}
	}

	if findDocker.ID == "" {
		msg = "未找到相应Docker"
		logger.Error(msg, " ", startupMarkKey)
		return
	}

	webUrl := ""
	for _, port := range findDocker.MapPorts { //这个地方有问题
		key := startupMarkK + port
		if key == startupMarkKey {
			webUrl = service.GetMapUrl(findDocker, port)
			break
		}
	}

	if webUrl == "" {
		result["no_web_url"] = "没有设置88端口"
		for _, port := range findDocker.MapPorts { //这个地方有问题
			webUrl = service.GetMapUrl(findDocker, port)
			break
		}
	}

	if webUrl != "" {
		go func() {

			for i := 0; i < 3; i++ {
				if str, err := service.NginxAddInstance(findDocker.VirtualId, startupMarkK); err != nil {
					logger.Error(str, err)
				} else {
					logger.Info("重新加载nginx", str)
				}
				time.Sleep(time.Second * 2)
			}

		}()

		result["web_url"] = webUrl
		msg = "映射完成"
		code = 0
	} else {
		msg = "notfind"
	}
}

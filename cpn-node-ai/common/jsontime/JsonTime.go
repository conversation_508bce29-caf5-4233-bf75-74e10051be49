package jsontime

import (
	"database/sql/driver"
	"time"
)

const TimeFormat = "2006-01-02 15:04:05"

type JsonTime time.Time

func (t *JsonTime) UnmarshalJSON(data []byte) (err error) {
	if len(data) == 2 {
		*t = JsonTime(time.Time{})
		return
	}

	//now, err := time.Parse(`"`+TimeFormat+`"`, string(data))
	now, err := time.ParseInLocation(`"`+TimeFormat+`"`, string(data), time.Local)
	if err != nil {
		return err
	}
	*t = JsonTime(now)
	return
}

func (t JsonTime) MarshalJSON() ([]byte, error) {
	b := make([]byte, 0, len(TimeFormat)+2)
	b = append(b, '"')
	b = time.Time(t).AppendFormat(b, TimeFormat)
	b = append(b, '"')
	return b, nil
}

func (t JsonTime) Value() (driver.Value, error) {
	if t.String() == "0001-01-01 00:00:00" {
		return nil, nil
	}
	return []byte(time.Time(t).Format(TimeFormat)), nil
}

func (t *JsonTime) Scan(v interface{}) error {
	tTime, _ := time.Parse("2006-01-02 15:04:05 +0800 CST", v.(time.Time).String())
	*t = JsonTime(tTime)
	return nil
}

func (t JsonTime) String() string {
	return time.Time(t).Format(TimeFormat)
}

func (t JsonTime) Time() time.Time {
	return time.Time(t)
}

func DefaultTime() JsonTime {

	// 获取 Asia/Shanghai 时区的 Location 对象
	loc, _ := time.LoadLocation("Asia/Shanghai")

	// 创建默认时间，使用 loc 时区
	t := time.Date(1900, time.January, 1, 0, 0, 0, 0, loc)
	return JsonTime(t)
}

func Now() JsonTime {
	// 创建默认时间，使用 loc 时区
	return JsonTime(time.Now())
}

func (t JsonTime) Unix() int64 {
	return time.Time(t).Unix()
}

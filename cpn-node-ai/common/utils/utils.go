package utils

import (
	"bytes"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"html/template"
	"io"
	"io/ioutil"
	"log"
	"math/rand"
	"net"
	"net/http"
	"os"
	"os/exec"
	"reflect"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"time"
	"unsafe"

	"github.com/google/uuid"
)

func OpenBrowser(url string) {
	var err error

	switch runtime.GOOS {
	case "linux":
		err = exec.Command("xdg-open", url).Start()
	case "windows":
		err = exec.Command("rundll32", "url.dll,FileProtocolHandler", url).Start()
	case "darwin":
		err = exec.Command("open", url).Start()
	}
	if err != nil {
		log.Println(err)
	}
}

func GetIp() (ip string) {
	ips, err := net.InterfaceAddrs()
	if err != nil {
		log.Println(err)
		return ip
	}

	for _, a := range ips {
		if ipNet, ok := a.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			if ipNet.IP.To4() != nil {
				ip = ipNet.IP.String()
				if strings.HasPrefix(ip, "10") {
					return
				}
				if strings.HasPrefix(ip, "172") {
					return
				}
				if strings.HasPrefix(ip, "192.168") {
					return
				}
				ip = ""
			}
		}
	}
	return
}

func GetClientIp(header http.Header) string {
	xForwardedFor := header.Get("X-Forwarded-For")
	ary := strings.Split(xForwardedFor, ",")
	for _, value := range ary {
		if !strings.HasPrefix(value, "192") {
			return value
		}
	}
	return ""
}

func GetLocalIP() string {
	// 获取本地所有网络接口
	interfaces, err := net.Interfaces()
	if err != nil {
		logger.Error(err)
	}

	// 遍历网络接口
	for _, iface := range interfaces {
		// 忽略loopback接口（如lo0）和没有物理地址的接口
		if iface.Flags&net.FlagUp == 0 || iface.Flags&net.FlagLoopback != 0 {
			continue
		}

		// 获取每个接口的地址
		addrs, err := iface.Addrs()
		if err != nil {
			logger.Error(err)
		}

		// 遍历接口的所有地址
		for _, addr := range addrs {
			// 确保地址是IPv4格式
			ipnet, ok := addr.(*net.IPNet)
			if ok && ipnet.IP.To4() != nil {
				return ipnet.IP.String()
			}
		}
	}
	return ""
}

var sizeKB = 1024
var sizeMB = sizeKB * 1024
var sizeGB = sizeMB * 1024

func Bytes2Size(num int64) string {
	numStr := ""
	unit := "B"
	if num/int64(sizeGB) > 1 {
		numStr = fmt.Sprintf("%.2f", float64(num)/float64(sizeGB))
		unit = "GB"
	} else if num/int64(sizeMB) > 1 {
		numStr = fmt.Sprintf("%d", int(float64(num)/float64(sizeMB)))
		unit = "MB"
	} else if num/int64(sizeKB) > 1 {
		numStr = fmt.Sprintf("%d", int(float64(num)/float64(sizeKB)))
		unit = "KB"
	} else {
		numStr = fmt.Sprintf("%d", num)
	}
	return numStr + " " + unit
}

func Seconds2Time(num int64) (time string) {
	if num/31104000 > 0 {
		//time += strconv.Itoa(num/31104000) + " 年 "
		time += fmt.Sprintf("%d 年 ", num/31104000)
		num %= 31104000
	}
	if num/2592000 > 0 {
		//time += strconv.Itoa(num/2592000) + " 个月 "
		time += fmt.Sprintf("%d 个月 ", num/2592000)
		num %= 2592000
	}
	if num/86400 > 0 {
		//time += strconv.Itoa(num/86400) + " 天 "
		time += fmt.Sprintf("%d 天 ", num/86400)
		num %= 86400
	}
	if num/3600 > 0 {
		//time += strconv.Itoa(num/3600) + " 小时 "
		time += fmt.Sprintf("%d 小时 ", num/3600)
		num %= 3600
	}
	if num/60 > 0 {
		//time += strconv.Itoa(num/60) + " 分钟 "
		time += fmt.Sprintf("%d 分钟 ", num/60)
		num %= 60
	}
	//time += strconv.Itoa(num) + " 秒"
	time += fmt.Sprintf("%d 秒 ", num)
	return
}

func Interface2String(inter interface{}) string {
	switch inter.(type) {
	case string:
		return inter.(string)
	case int:
		return fmt.Sprintf("%d", inter.(int))
	case float64:
		return fmt.Sprintf("%f", inter.(float64))
	}
	return "Not Implemented"
}

func UnescapeHTML(x string) interface{} {
	return template.HTML(x)
}

func IntMax(a int, b int) int {
	if a >= b {
		return a
	} else {
		return b
	}
}

func FileMd5(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}

func GetMd5(oMd5Str string) string {

	has := md5.Sum([]byte(oMd5Str))
	md5Str := hex.EncodeToString(has[:])
	if len(md5Str) != 32 {
		logger.Error("err md5位数不正确，", md5Str, "  ", oMd5Str)
		return ""
	}
	return md5Str
}

func GetUUID() string {
	code := uuid.New().String()
	code = strings.Replace(code, "-", "", -1)
	return code
}

const keyChars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"

func init() {
	rand.Seed(time.Now().UnixNano())
}

func GenerateKey() string {
	rand.Seed(time.Now().UnixNano())
	key := make([]byte, 48)
	for i := 0; i < 16; i++ {
		key[i] = keyChars[rand.Intn(len(keyChars))]
	}
	uuid_ := GetUUID()
	for i := 0; i < 32; i++ {
		c := uuid_[i]
		if i%2 == 0 && c >= 'a' && c <= 'z' {
			c = c - 'a' + 'A'
		}
		key[i+16] = c
	}
	return string(key)
}

func CreateCaptcha(num int) string {
	str := "1"
	for i := 0; i < num; i++ {
		str += strconv.Itoa(0)
	}
	str10 := str
	int10, err := strconv.ParseInt(str10, 10, 32)
	if err != nil {
		fmt.Println(err)
		return ""
	} else {
		j := int32(int10)
		return fmt.Sprintf("%0"+strconv.Itoa(num)+"v", rand.New(rand.NewSource(time.Now().UnixNano())).Int31n(j))
	}
}

func GetRandomString(length int) string {
	rand.Seed(time.Now().UnixNano())
	key := make([]byte, length)
	for i := 0; i < length; i++ {
		key[i] = keyChars[rand.Intn(len(keyChars))]
	}
	return string(key)
}

func GetTimestamp() int64 {
	return time.Now().Unix()
}

func GetTimeString() string {
	now := time.Now()
	return fmt.Sprintf("%s%d", now.Format("20060102150405"), now.UnixNano()%1e9)
}

func Max(a int, b int) int {
	if a >= b {
		return a
	} else {
		return b
	}
}

func GetOrDefault(env string, defaultValue int) int {
	if env == "" || os.Getenv(env) == "" {
		return defaultValue
	}
	num, err := strconv.Atoi(os.Getenv(env))
	if err != nil {
		logger.Error(fmt.Sprintf("failed to parse %s: %s, using default value: %d", env, err.Error(), defaultValue))
		return defaultValue
	}
	return num
}

func MessageWithRequestId(message string, id string) string {
	return fmt.Sprintf("%s (request id: %s)", message, id)
}

// 生成大写字母和数字的组合的 6 位字符串，不包括字母 'O' 和数字 '0'
func GenerateCouponString(num int) string {
	// 定义字符集合
	charSet := "ABCDEFGHJKLMNPQRSTUVWXYZ123456789"

	// 设置随机种子
	source := rand.NewSource(time.Now().UnixNano())
	randGen := rand.New(source)

	// 生成 6 位字符串
	result := make([]byte, num)
	for i := range result {
		result[i] = charSet[randGen.Intn(len(charSet))]
	}
	return string(result)
}

// 生成指定位数的随机数
func GenerateRandomNumber(digits int) int {
	// 创建随机种子
	source := rand.NewSource(time.Now().UnixNano())
	randGen := rand.New(source)

	// 计算随机数的上下限
	min := 1
	max := 1
	for i := 0; i < digits-1; i++ {
		min *= 10
		max *= 10
	}
	max = max*10 - 1

	// 生成指定位数的随机数
	return randGen.Intn(max-min+1) + min
}

func GenerateRandomNumberStr(digits int) string {
	// 定义字符集合
	charSet := "0123456789"

	// 设置随机种子
	source := rand.NewSource(time.Now().UnixNano())
	randGen := rand.New(source)

	// 生成 6 位字符串
	result := make([]byte, digits)
	for i := range result {
		result[i] = charSet[randGen.Intn(len(charSet))]
	}
	return string(result)
}

func GenerateRandomInt(min, max int) int {
	source := rand.NewSource(time.Now().UnixNano())
	randGen := rand.New(source)
	return randGen.Intn(max-min+1) + min
}

func IsMobile(mobile string) bool {
	result, _ := regexp.MatchString(`^(1[3|4|5|6|7|8|9][0-9]\d{4,8})$`, mobile)
	if result {
		return true
	} else {
		return false
	}
}

func FormatMobileStar(mobile string) string {
	if len(mobile) <= 10 {
		return mobile
	}
	return mobile[:3] + "****" + mobile[7:]
}

func Substring(s string, start, length int) string {

	rs := []rune(s)

	l := len(rs)

	end := start + length

	if start < 0 || start >= l || end < start || end > l {

		return ""

	}

	return string(rs[start:end])

}

func String2Int(str string) int {
	num, err := strconv.Atoi(str)
	if err != nil {
		return 0
	}
	return num
}

func String2Int64(str string) int64 {
	intValue, err := strconv.ParseInt(str, 10, 64)
	if err != nil {
		fmt.Println("转换错误:", err)
	}
	return intValue
}

func String2Uint(s string) uint {
	num, err := strconv.ParseUint(s, 10, 32)
	if err != nil {
		logger.Error(err)
		return 0
	}
	return uint(num)
}

func Int2String(num int) string {
	return strconv.Itoa(num)
}
func Int642String(num int64) string {
	return strconv.FormatInt(num, 10)
}
func Uint2String(num uint) string {
	return strconv.FormatUint(uint64(num), 10)
}

func String2Time(s string) (time.Time, error) {
	return time.ParseInLocation(jsontime.TimeFormat, s, time.Local)
}

func GetStructFromJson(dest interface{}, jsonStr string) error { //传进来的dest 要加&
	if err := json.Unmarshal([]byte(jsonStr), dest); err != nil {
		logger.Error(err, "===", jsonStr, "===")
		return err
	}
	return nil
}

func GetStructFromMap(dest interface{}, mm map[string]interface{}) error { //传进来的dest 要加&
	jsonStr := GetJsonFromStruct(mm)
	if err := json.Unmarshal([]byte(jsonStr), dest); err != nil {
		logger.Error(err, "===", jsonStr, "===")
		return err
	}
	return nil
}

func GetJsonFromStruct(m interface{}) string {
	jsonbyte, err := json.Marshal(m)
	if err != nil {
		logger.Error(err)
		return ""
	}
	return string(jsonbyte)
}

func GetBytesFromStruct(m interface{}) []byte {
	jsonbyte, err := json.Marshal(m)
	if err != nil {
		logger.Error(err)
		return jsonbyte
	}
	return jsonbyte
}

func GetMapFromJson(jsonStr string) map[string]interface{} {
	var data map[string]interface{}
	// 反序列化 JSON 字符串
	err := json.Unmarshal([]byte(jsonStr), &data)
	if err != nil {
		logger.Error(err, jsonStr)
		return nil
	}
	return data
}

func GetMapAryFromJson(jsonStr string) []map[string]interface{} {
	var data []map[string]interface{}
	// 反序列化 JSON 字符串
	err := json.Unmarshal([]byte(jsonStr), &data)
	if err != nil {
		logger.Error(err, jsonStr)
		return nil
	}
	return data
}

func GetStructAryFromJson(ary interface{}, jsonStr string) error { //ary是地址 需要加&  在外面第一结构体数组 var data []struct
	//var data []map[string]interface{}
	// 反序列化 JSON 字符串
	err := json.Unmarshal([]byte(jsonStr), ary)
	if err != nil {
		logger.Error(err, "jsonStr：", jsonStr)
		return err
	}
	return nil
}

func Scan(source interface{}, target interface{}) error {

	mSource := make(map[string]reflect.Value)
	if err := StructScan2Map(source, mSource); err != nil {
		logger.Error(err)
		return err
	}
	targetValue := reflect.ValueOf(target).Elem()
	targetType := reflect.TypeOf(target).Elem()
	for i := 0; i < targetType.NumField(); i++ {
		field := targetValue.Field(i)
		fieldName := targetType.Field(i).Name
		//typeName := targetType.Field(i).Type.Name()

		if !field.CanSet() {
			continue
		}
		if val, ok := mSource[fieldName]; ok {
			//fmt.Println(val.Type(), "        ", field.Type())
			if val.Type() == field.Type() {
				field.Set(val)
			} else if val.Type() == reflect.TypeOf(time.Time{}) {
				if field.Type() == reflect.TypeOf(jsontime.JsonTime{}) {
					t := jsontime.JsonTime(val.Interface().(time.Time))
					field.Set(reflect.ValueOf(t))
				}
			}
		}
	}
	fmt.Println(target)
	return nil
}

func StructScan2Map(source interface{}, m map[string]reflect.Value) error {
	data_t := reflect.TypeOf(source) //获得结构体的类型
	//fmt.Println("数据结构体的类型为:", data_t.Name())
	data_v := reflect.ValueOf(source)        //获得结构体所有字段值
	for i := 0; i < data_t.NumField(); i++ { //遍历结构体类型的所有字段
		data_f := data_t.Field(i)
		val := data_v.Field(i)
		//fmt.Printf("字段名:%s, 字段类型:%v,  字段值:%v\n", data_f.Name, data_f.Type, val.Interface())

		typeName := data_f.Type.Name()
		typeNames := "Time,DeletedAt,JsonTime"
		if strings.Contains(typeNames, typeName) {
			m[data_f.Name] = val
		} else if val.Kind() == reflect.Struct {
			StructScan2Map(val.Interface(), m)
		} else {
			m[data_f.Name] = val
		}
	}
	return nil
}

func Post(url string, payload string) (string, error) {
	logger.Info(url, "   post payload:", payload)
	//authorization := "Basic eXhqOnl4ajEyMw=="
	req, err := http.NewRequest("POST", url, bytes.NewBuffer([]byte(payload)))
	if err != nil {
		logger.Error("NewRequest error:", err)
		return "", err
	}
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
	//req.Header.Set("Authorization", authorization)

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		logger.Error("Post Fatal error:", err)
		return "", err
	}

	defer res.Body.Close()

	content, err := ioutil.ReadAll(res.Body)
	if err != nil {
		logger.Error("Fatal error ", err)
		return "", err
	}

	str := (*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	return *str, nil
}

func Get(url string, headers map[string]string, cookies map[string]string) (*http.Response, error) {
	//authorization := "Basic eXhqOnl4ajEyMw=="
	//payload := ""
	//req, err := http.NewRequest("GET", url, bytes.NewBuffer([]byte(payload)))
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logger.Error("NewRequest error:", err)
		return nil, err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}
	if cookies != nil {
		for key, value := range cookies {
			cookie := &http.Cookie{
				Name:  key,
				Value: value,
			}
			req.AddCookie(cookie)
		}
	}

	//req.Header.Set("Content-Type", "application/json;charset=utf-8")
	//req.Header.Set("Authorization", authorization)

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		//logger.Error("Post Fatal error:", err)
	}

	//defer res.Body.Close()

	return res, err
	//data := res.Cookies()
	//var ttwid string
	//for _, c := range data {
	//	if c.Name == "ttwid" {
	//		ttwid = c.Value
	//		break
	//	}
	//}
	//fmt.Println(ttwid)
	//
	//content, err := ioutil.ReadAll(res.Body)
	//if err != nil {
	//	logger.Error("Fatal error ", err)
	//	return "", err
	//}
	//
	//str := (*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	//return *str, nil
}

package model

import (
	"errors"
	"fmt"
	"gorm.io/gorm"
	"time"
	"zcloud-ai/enums"
	"zcloud-ai/utils/logger"
)

type Txt2Img struct {
	gorm.Model
	UserId            uint      `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	OrigWhere         int       `json:"orig_where" gorm:"type:int;not null;default:0;comment:哪里来的数据"`
	OrigId            uint      `json:"orig_id" gorm:"type:bigint;not null;default:0;comment:来源记录ID"`
	EnableHr          bool      `json:"enable_hr" gorm:"type:tinyint;not null;default:0;comment:"`
	DenoisingStrength int       `json:"denoising_strength"  gorm:"type:int;not null;default:0;comment:"`
	FirstphaseWidth   int       `json:"firstphase_width"  gorm:"type:int;not null;default:0;comment:"`
	FirstphaseHeight  int       `json:"firstphase_height"  gorm:"type:int;not null;default:0;comment:"`
	Prompt            string    `json:"prompt" gorm:"type:varchar(1500);not null;default:'';comment:画面描述"`
	NegativePrompt    string    `json:"negative_prompt" gorm:"type:varchar(1500);not null;default:'';comment:我不想要什么样的风格画面描述"`
	Styles            string    `json:"styles" gorm:"type:json;comment:"`
	Seed              int64     `json:"seed" gorm:"type:bigint;not null;default:-1;comment:"`
	Subseed           int64     `json:"subseed" gorm:"type:bigint;not null;default:-1;comment:"`
	SubseedStrength   int       `json:"subseed_strength" gorm:"type:tinyint;not null;default:0;comment:"`
	SeedResizeFromW   int       `json:"seed_resize_from_w" gorm:"type:int;not null;default:0;comment:" `
	SeedResizeFromH   int       `json:"seed_resize_from_h" gorm:"type:int;not null;default:0;comment:" `
	BatchCount        int       `json:"batch_count" gorm:"type:tinyint;not null;default:0;comment:每次生成的批次数量" `
	BatchSize         int       `json:"batch_size" gorm:"type:tinyint;not null;default:1;comment:每次生成的图片数量" `
	NIter             int       `json:"n_iter" gorm:"type:tinyint;not null;default:1;comment:" `
	Steps             int       `json:"steps" gorm:"type:tinyint;not null;default:50;comment:生成图片的迭代步数(Sampling Steps)"`
	CfgScale          float32   `json:"cfg_scale" gorm:"type:float;not null;default:0;comment:" `
	Width             int       `json:"width" gorm:"type:int;not null;default:0;comment:图像宽度" `
	Height            int       `json:"height" gorm:"type:int;not null;default:0;comment:图像高度" `
	BaseWidth         int       `json:"base_width" gorm:"type:int;not null;default:0;comment:基础图像宽度" `
	BaseHeight        int       `json:"base_height" gorm:"type:int;not null;default:0;comment:基础图像高度" `
	RestoreFaces      bool      `json:"restore_faces" gorm:"type:tinyint;not null;default:0;comment:优化面部，绘制面部图像特别注意" `
	Tiling            bool      `json:"tiling" gorm:"type:tinyint;not null;default:0;comment:生成一个可以平铺的图像；" `
	Eta               int       `json:"eta" gorm:"type:int;not null;default:0;comment:"`
	SChurn            int       `json:"s_churn" gorm:"type:int;not null;default:0;comment:"`
	STmax             int       `json:"s_tmax" gorm:"type:int;not null;default:0;comment:"`
	STmin             int       `json:"s_tmin" gorm:"type:int;not null;default:0;comment:"`
	SNoise            int       `json:"s_noise" gorm:"type:int;not null;default:1;comment:"`
	OverrideSettings  string    `json:"override_settings" gorm:"type:json;';comment:"`
	SamplerIndex      string    `json:"sampler_index" gorm:"type:varchar(50);not null;default:'Euler';comment:Sampling method(Euler、LMS)"`
	CompleteAt        time.Time `json:"complete_at" gorm:"type:datetime;default:'1900-01-01';comment:生成完成时间"`
	Md5Name           string    `json:"md5_name" gorm:"type:varchar(50);not null;default:'';comment:生成的图片名称"`
	State             int       `json:"state" gorm:"type:int;not null;default:0;comment:状态 0等待 1正在执行 2执行成功 3执行失败"`

	SdapiType         int    `json:"sdapi_type" gorm:"type:tinyint;not null;default:0;comment:接口类型对应SdApiTypeEnum 0txt2txt 1img2img" `
	InitImages        string `json:"init_images" gorm:"type:json;';comment:"`
	IncludeInitImages bool   `json:"include_init_images" gorm:"type:tinyint;not null;default:0;comment:"`
}

func (Txt2Img) TableName() string {
	return "T_Txt2Img"
}

func (o *Txt2Img) GetByID(id uint) error {
	err := db.First(o, id).Error
	return err
}

func (o *Txt2Img) GetByAnimeID(animeId uint) error {
	err := db.Debug().Where("orig_where =1 and orig_id = ?", animeId).Limit(1).Find(o).Error
	return err
}

func (o *Txt2Img) GetOneByAnimeID(animeId uint) error {
	err := db.Debug().Where("orig_where =1 and orig_id = ?", animeId).Order("id desc").First(o).Error
	return err
}

func (o *Txt2Img) GetEarly() error {
	err := db.Debug().Where("state = ?", 0).Order("id asc").Limit(1).Find(o).Error
	return err
}

func (o *Txt2Img) Save() error {
	return db.Save(o).Error
}

func (o *Txt2Img) SetState(state int) error {
	return db.Model(o).Updates(Txt2Img{State: state}).Error
}

func (oo *Txt2Img) SetComplete() error {
	// 开启事务
	tx := db.Begin()

	err := tx.Model(oo).Updates(Txt2Img{State: enums.ImgStateEnum.Success,
		CompleteAt: time.Now(),
	}).Error
	if err == nil {
		var o Anime
		o.ID = oo.OrigId
		err = tx.Model(o).Updates(Anime{State: enums.ModelStateEnum.Complete}).Error
	}

	if err != nil {
		tx.Rollback()
	} else {
		tx.Commit()
	}
	return err
}

func (oo *Txt2Img) SetAnimeComplete(outputImages string) error {

	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		transStr := fmt.Sprintf("SetAnimeComplete%d", oo.ID)
		if oo.OrigWhere != enums.OrigWhereEnum.Anime {
			logger.Error(transStr, "oo.OrigWhere != enums.OrigWhereEnum.Anime", oo.OrigWhere)
			return errors.New("参数不匹配")
		}

		if err := tx.Model(&oo).Updates(Txt2Img{State: enums.ImgStateEnum.Success,
			CompleteAt: time.Now(),
		}).Error; err != nil {
			logger.Error(transStr, err)
			return err
		}

		var anime Anime
		anime.ID = oo.OrigId
		if anime.ID == 0 {
			logger.Error(transStr, "anime.ID==0")
			return errors.New("anime.ID==0")
		}
		if err := tx.Model(&anime).Updates(Anime{OutputImages: outputImages, State: enums.ModelStateEnum.Complete}).Error; err != nil {
			logger.Error(transStr, err)
			return err
		}
		return nil
	})

}

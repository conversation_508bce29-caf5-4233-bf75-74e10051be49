package model

type ArtParm struct {
	Prompt         string  `json:"prompt" gorm:"type:varchar(1500);not null;default:'';comment:风格描述"`
	NegativePrompt string  `json:"negative_prompt" gorm:"type:varchar(1500);not null;default:'';comment:我不想要什么样的风格画面描述"`
	ModelName      string  `json:"model_name" gorm:"type:varchar(100);not null;default:'';comment:模型名称"`
	ModelHash      string  `json:"model_hash" gorm:"type:varchar(50);not null;default:'';comment:模型hash"`
	Sampler        string  `json:"sampler" gorm:"type:varchar(50);not null;default:'';comment:采样方法"`
	CfgScale       float32 `json:"cfg_scale" gorm:"type:int;not null;default:0;comment:提示词相关性"`
	Steps          int     `json:"steps" gorm:"type:int;not null;default:0;comment:采样迭代步数"`
	Seed           int     `json:"seed" gorm:"type:int;not null;default:0;comment:随机种子"`
	ClipSkip       int     `json:"clip_skip" gorm:"type:int;not null;default:0;comment:跳过层数"`
}

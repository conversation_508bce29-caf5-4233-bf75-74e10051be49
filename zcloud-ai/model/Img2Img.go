package model

import (
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"time"
)

type Img2Img11 struct {
	gorm.Model
	//图像长宽，可以通过send to extras 进行扩大，所以这里不建议设置太大[显存小的特别注意]；
	Width  int `json:"width" gorm:"type:tinyint;not null;default:0;comment:图像宽度" `
	height int `json:"height" gorm:"type:tinyint;not null;default:0;comment:图像高度" `

	restore_faces bool `json:"restore_faces" gorm:"type:tinyint;not null;default:0;comment:优化面部，绘制面部图像特别注意" `
	tiling        bool `json:"tiling" gorm:"type:tinyint;not null;default:0;comment:生成一个可以平铺的图像；" `
	eta           int  `json:"eta" gorm:"type:int;not null;default:0;comment:"`

	s_churn           int    `json:"s_churn" gorm:"type:int;not null;default:0;comment:"`
	s_tmax            int    `json:"s_tmax" gorm:"type:int;not null;default:0;comment:"`
	s_tmin            int    `json:"s_tmin" gorm:"type:int;not null;default:0;comment:"`
	s_noise           int    `json:"s_noise" gorm:"type:int;not null;default:1;comment:"`
	override_settings string `json:"override_settings" gorm:"type:json;';comment:"`

	//扩散去噪算法的采样模式，会带来不一样的效果，ddim 和 pms(plms) 的结果差异会很大，很多人还会使用euler，具体没有系统测试；
	SamplerIndex string `json:"sampler_index" gorm:"type:varchar(50);not null;default:'Euler';comment:Sampling method(Euler、LMS)"`
	State        int    `gorm:"type:int;not null;default:0;comment:状态"`
}

type Img2Img struct {
	gorm.Model
	UserId    uint `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	OrigWhere int  `json:"orig_where" gorm:"type:int;not null;default:0;comment:哪里来的数据"`
	OrigId    uint `json:"orig_id" gorm:"type:bigint;not null;default:0;comment:来源记录ID"`

	InitImages string `json:"init_images" gorm:"type:json;';comment:"`

	ResizeMode            int             `json:"resize_mode" gorm:"type:int;not null;default:0;comment:"`
	DenoisingStrength     decimal.Decimal `json:"denoising_strength"  gorm:"type:decimal(16,2);not null;default:0;comment:"`
	Mask                  string          `json:"mask" gorm:"type:varchar(50);not null;default:'';comment:画面描述"`
	MaskBlur              int             `json:"mask_blur" gorm:"type:int;not null;default:0;comment:"`
	InpaintingFill        int             `json:"inpainting_fill" gorm:"type:int;not null;default:0;comment:"`
	InpaintFullRes        bool            `json:"inpaint_full_res" gorm:"type:tinyint;not null;default:0;comment:"`
	InpaintFullResPadding int             `json:"inpaint_full_res_padding" gorm:"type:int;not null;default:0;comment:"`
	InpaintingMaskInvert  int             `json:"inpainting_mask_invert" gorm:"type:int;not null;default:0;comment:"`
	Prompt                string          `json:"prompt" gorm:"type:varchar(1500);not null;default:'';comment:画面描述"`
	Styles                string          `json:"styles" gorm:"type:json;comment:"`

	Seed            int64  `json:"seed" gorm:"type:bigint;not null;default:-1;comment:"`
	Subseed         int64  `json:"subseed" gorm:"type:bigint;not null;default:-1;comment:"`
	SubseedStrength int    `json:"subseed_strength" gorm:"type:tinyint;not null;default:0;comment:"`
	SeedResizeFromW int    `json:"seed_resize_from_w" gorm:"type:int;not null;default:0;comment:" `
	SeedResizeFromH int    `json:"seed_resize_from_h" gorm:"type:int;not null;default:0;comment:" `
	SamplerName     string `json:"sampler_name" gorm:"type:varchar(50);not null;default:'';comment:"`

	/*
		EnableHr         bool `json:"enable_hr" gorm:"type:tinyint;not null;default:0;comment:"`
		FirstphaseWidth  int  `json:"firstphase_width"  gorm:"type:int;not null;default:0;comment:"`
		FirstphaseHeight int  `json:"firstphase_height"  gorm:"type:int;not null;default:0;comment:"`*/

	NegativePrompt string `json:"negative_prompt" gorm:"type:varchar(1500);not null;default:'';comment:我不想要什么样的风格画面描述"`

	BatchSize        int     `json:"batch_size" gorm:"type:tinyint;not null;default:1;comment:每次生成的图片数量" `
	NIter            int     `json:"n_iter" gorm:"type:tinyint;not null;default:1;comment:" `
	Steps            int     `json:"steps" gorm:"type:tinyint;not null;default:50;comment:生成图片的迭代步数(Sampling Steps)"`
	CfgScale         float32 `json:"cfg_scale" gorm:"type:float;not null;default:0;comment:" `
	Width            int     `json:"width" gorm:"type:int;not null;default:0;comment:图像宽度" `
	Height           int     `json:"height" gorm:"type:int;not null;default:0;comment:图像高度" `
	RestoreFaces     bool    `json:"restore_faces" gorm:"type:tinyint;not null;default:0;comment:优化面部，绘制面部图像特别注意" `
	Tiling           bool    `json:"tiling" gorm:"type:tinyint;not null;default:0;comment:生成一个可以平铺的图像；" `
	Eta              int     `json:"eta" gorm:"type:int;not null;default:0;comment:"`
	SChurn           int     `json:"s_churn" gorm:"type:int;not null;default:0;comment:"`
	STmax            int     `json:"s_tmax" gorm:"type:int;not null;default:0;comment:"`
	STmin            int     `json:"s_tmin" gorm:"type:int;not null;default:0;comment:"`
	SNoise           int     `json:"s_noise" gorm:"type:int;not null;default:1;comment:"`
	OverrideSettings string  `json:"override_settings" gorm:"type:json;';comment:"`
	SamplerIndex     string  `json:"sampler_index" gorm:"type:varchar(50);not null;default:'Euler';comment:Sampling method(Euler、LMS)"`

	IncludeInitImages bool `json:"include_init_images" gorm:"type:tinyint;not null;default:0;comment:"`

	CompleteAt time.Time `json:"complete_at" gorm:"type:datetime;comment:生成完成时间"`
	Md5Name    string    `json:"md5_name" gorm:"type:varchar(50);not null;default:'';comment:生成的图片名称"`
	State      int       `json:"state" gorm:"type:int;not null;default:0;comment:状态 0等待 1正在执行 2执行成功 3执行失败"`
}

func (Img2Img) TableName() string {
	return "T_Img2Img"
}

func (o *Img2Img) GetByID(id uint) error {
	return db.First(o, id).Error
}

func (o *Img2Img) Save() error {
	return db.Save(o).Error
}

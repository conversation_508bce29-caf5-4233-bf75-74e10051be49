package alipay

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/alipay"
	"github.com/shopspring/decimal"
	"net/http"
	"time"
	"zcloud-ai/model"
	"zcloud-ai/utils/logger"
)

func Callback11(c *gin.Context) {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("Notify:", e)
		}
	}()

	logger.Info("已经接收到支付宝回调v1.3")
	data, _ := c.GetRawData()
	//body := `{"id":"73ac31c1-ca7c-5521-a170-9f49590755ae","create_time":"2022-12-14T14:58:14+08:00","resource_type":"encrypt-resource","event_type":"TRANSACTION.SUCCESS","summary":"支付成功","resource":{"original_type":"transaction","algorithm":"AEAD_AES_256_GCM","ciphertext":"nzc/jDeuqpYftu3b7upbQTWuQx8XyyYBCq+JrycWS6wyQ/rf20dCEd23GeZVXoOVfc2r8vL92vvIl6+U/4isuxcIDcUjTt0mUgoPTK0lPyvsVBC0hPNdDam4Z7dEFR9lx1oVY0APengkLTEgfV/khilZGEJkEOK//xeyikUeXd4djJwFa0bkJx8Ovy8/l3K5SkoRI7zZmyhKUiWpCAeVu2v5Fy+1NMNa9PPhdNZFnVFZC4Fd9A5oeCn+zlYK3uAcLuUF35aUfUCipnRp6v0CJR4S54fqSaKFPzLkPr3Ro83f1DKXYFUnstD/8xWtqnCCoAVyxLR62WIDXKUhzBlWBjxukDRwmBnTAKoOiESPVxZnuzRt/YjP7Q5q0v8f9NaK2uMis+AOXW9ZmE6oADbylaw4SGkVth4c0ftihN9MG/Of6pGGgMY4mXHpnGXBxy8AuwbOZkMQCckdGb0nxuhZ392P3Gpm/Azn2fWN2ESkIu2nVW0HrrcNtLn2ygkxYu4Rq5m6sp9RsXd3c7cshaEExRjllGqiFKBdiAk/Lsz6FI+KbJia5yymua1XomhYLOasi+Ee6eI=","associated_data":"transaction","nonce":"a5BqG3MVwXJT"}}`
	//data = []byte(body)
	logger.Info(string(data))

	//{"app_id":"2021001191690325","auth_app_id":"2021001191690325","charset":"utf-8","method":"alipay.trade.page.pay.return","out_trade_no":"r2022122216252600000035","seller_id":"2088931329094002","sign":"Zak8pBm8YihC3vIkbaGQj7EhBa7W1lRIAYjY+yXXjp0LwZF6JNXdfkYB7rL/lt/5pUJNjxuZlYI+roURQlUV8rR0902m3YK3O/vhCzm26qGYgpDk9eTFore6Vz08h82MJcm2I8WT+MRm3891LPg++Vg1hqTpqEcvvf2prSR8YsJkvho0oFqBQhJfaEpCU0cohG2eliVAFI1BkFu0OUzalTsnLV99GRo+Iq8mAR/MYrMudkgJnGjGVq4wgAmvSE1VVJggOVQCHc9x89Kla/ttfZPeubDFwrIIqv6oqmNdhVa12WTJe8VEjGMjz1FHZpQr0h2dZdne0iijQI6UaV6gWQ==","sign_type":"RSA2","timestamp":"2022-12-22 16:28:49","total_amount":"0.01","trade_no":"2022122222001495421431424831","version":"1.0"}
	notifyReq, err := alipay.ParseNotifyToBodyMap(c.Request) // c.Request 是 gin 框架的写法
	if err != nil {
		logger.Error(err)
		return
	}
	logger.Info("notifyReq", notifyReq)
	jsonbyte, _ := json.Marshal(notifyReq)
	notifyStr := string(jsonbyte)
	logger.Info("解密前Json：", string(jsonbyte))

	PayService.setUp()
	// 支付宝异步通知验签（公钥模式）
	ok, err := alipay.VerifySign(PayService.alipayPublicKey, notifyReq)
	if err != nil || !ok {
		logger.Error(err, ok)
		return
	}

	// 支付宝异步通知验签（公钥证书模式）
	//ok, err = alipay.VerifySignWithCert("alipayCertPublicKey_RSA2.crt content", notifyReq)

	// 如果需要，可将 BodyMap 内数据，Unmarshal 到指定结构体指针 ptr
	//err = notifyReq.Unmarshal(ptr)

	if notifyReq.Get("method") == "alipay.trade.page.pay.return" {
		outTradeNo := notifyReq.Get("out_trade_no")
		tradeNo := notifyReq.Get("trade_no")
		totalAmount, err := decimal.NewFromString(notifyReq.Get("total_amount"))
		if err != nil {
			logger.Error(err)
			return
		}
		successTime, errTime := time.ParseInLocation("2006-01-02 15:04:05", notifyReq.Get("timestamp"), time.Local) //这里按照当前时区转
		if errTime != nil {
			logger.Error(errTime)
			return
		}

		var recharge model.Recharge
		if err := recharge.GetByOutTradeNo(outTradeNo); err != nil || recharge.ID <= 0 {
			logger.Error(outTradeNo, err)
			return
		}
		if !recharge.AmountCharge.Equal(totalAmount) {
			logger.Error(outTradeNo, " 订单金额不匹配 ", notifyStr)
			return
		}

		if len(recharge.PayTradeId) == 0 {
			recharge.PayTradeId = tradeNo
		}
		recharge.PayTime = successTime
		recharge.PayCallbackJson = notifyStr
		err = model.Transactions.RechargeSuccess(&recharge)
		if err != nil {
			logger.Error(err)
			return
		} else {
			c.String(http.StatusOK, "%s", "success")
			return
		}

	}
}

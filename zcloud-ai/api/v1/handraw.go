package v1

import (
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"mime/multipart"
	"net/http"
	"os"
	"path"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"
	"zcloud-ai/api/weixin"
	"zcloud-ai/enums"
	"zcloud-ai/middleware"
	"zcloud-ai/model"
	"zcloud-ai/service"
	"zcloud-ai/utils"
	"zcloud-ai/utils/config"
	"zcloud-ai/utils/errmsg"
	"zcloud-ai/utils/logger"
	"zcloud-ai/utils/myimg"
)

type handrawApi struct {
}

type guessReq struct {
	HandUuid string `json:"hand_uuid"`
	Prompt   string `json:"prompt"`
}
type guessResp struct {
	HandUuid     string `json:"hand_uuid"`
	InputImgMd5  string `json:"input_img_md5"`
	InputImgUrl  string `json:"input_img_url"`
	OutputImgUrl string `json:"output_img_url"`
	OutputImgMd5 string `json:"output_img_md5"`
}

type addResp struct {
	HandUuid      string   `json:"hand_uuid"`
	InputImgMd5   string   `json:"input_img_md5"`
	InputImgUrl   string   `json:"input_img_url"`
	OutputImgUrl  string   `json:"output_img_url"`
	OutputImgsMd5 []string `json:"output_imgs_md5"`
}

type publishReq struct {
	HandUuid string `json:"hand_uuid"`
	Md5      string `json:"md5"`
	Share    int    `json:"share"`
}

type stateReq struct {
	HandUuid   string `json:"hand_uuid"`
	QueryCount int    `json:"query_count"`
}
type stateResp struct {
	HandUuid     string `json:"hand_uuid"`
	OutputImgUrl string `json:"output_img_url"`
}
type stateMd5Req struct {
	OutputImgMd5 string `json:"output_img_md5"`
}
type stateMd5Resp struct {
	OutputImgMd5 string `json:"output_img_md5"`
	OutputImgUrl string `json:"output_img_url"`
}

type listFallReq struct {
	HandUuid string `json:"hand_uuid"`
	LastId   uint   `json:"last_id"`
	PageSize int    `json:"page_size"`
}

type listFallResp struct {
	HandUuid     string `json:"hand_uuid"`
	InputImgUrl  string `json:"input_img_url"`
	OutputImgMd5 string `json:"output_img_md5"`
	OutputImgUrl string `json:"output_img_url"`
	Width        int    `json:"width"`
	Height       int    `json:"height"`
}

type listOutImgReq struct {
	HandUuid string `json:"hand_uuid"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}
type listOutImgResp struct {
	HandUuid     string `json:"hand_uuid"`
	InputImgUrl  string `json:"input_img_url"`
	OutputImgMd5 string `json:"output_img_md5"`
	OutputImgUrl string `json:"output_img_url"`
	Width        int    `json:"width"`
	Height       int    `json:"height"`
	Share        int    `json:"share"`
}

type detailReq struct {
	HandUuid string `json:"hand_uuid"`
}
type detailResp struct {
	UserId       uint   `json:"user_id"`
	HandUuid     string `json:"hand_uuid"`
	Width        int    `json:"width"`
	Height       int    `json:"height"`
	InputImgMd5  string `json:"input_img_md5"`
	InputImgUrl  string `json:"input_img_url"`
	OutputImgUrl string `json:"output_img_url"`
	DrawPath     string `json:"draw_path"`
	DrawType     int    `json:"draw_type"`
	Prompt       string `json:"prompt"`
}

func (obj handrawApi) LaunchGuess(c *gin.Context) {
	var code int

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	userId := claims.UserId

	//prompt, _ := c.GetPostForm("prompt")
	parentUuid, _ := c.GetPostForm("parent_uuid")

	drawPath, _ := c.GetPostForm("draw_path")
	if drawPath == "" {
		//errmsg.Abort(c, errmsg.FAIL, "请画图")
		//return
		drawPath = "[]"
	}

	//from, _ := c.GetPostForm("from")

	drawType := 0
	drawTypeStr, _ := c.GetPostForm("draw_type")
	if drawTypeStr != "" {
		if num, err := strconv.Atoi(drawTypeStr); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		} else {
			drawType = num
		}
	}

	handFrom := 0
	handFromStr, _ := c.GetPostForm("hand_from")
	if handFromStr != "" {
		if num, err := strconv.Atoi(handFromStr); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		} else {
			handFrom = num
		}
	}

	userEnvStr, _ := c.GetPostForm("user_env")
	logger.Info("userEnv:", userEnvStr)
	var userEnv model.UserEnv
	if userEnvStr != "" {
		if err := utils.GetStructFromJson(&userEnv, userEnvStr); err != nil {
			logger.Error(err)
		}
	}

	priceCoin := 0
	priceCoinStr, _ := c.GetPostForm("price_coin")
	if priceCoinStr != "" {
		if num, err := strconv.Atoi(priceCoinStr); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		} else {
			priceCoin = num
			if priceCoin != 0 && priceCoin < 20 {
				logger.Error("err priceCoin:", priceCoin)
				errmsg.Abort(c, errmsg.FAIL, "参数错误")
				return
			}
		}
	}
	if priceCoin < 0 {
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	} else if priceCoin > 0 && priceCoin != 20 {
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	batchCount := 0
	batchCountStr, _ := c.GetPostForm("batch_count")
	if batchCountStr != "" {
		if num, err := strconv.Atoi(batchCountStr); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		} else {
			batchCount = num
		}
	}

	ext := ""
	var f *multipart.FileHeader
	if parentUuid == "" {
		ff, err := c.FormFile("file")
		if err != nil {
			errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
			return
		}
		ext = path.Ext(ff.Filename) // 输出 .html
		f = ff
	}

	var user model.User
	if err := user.GetByID(userId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	if priceCoin == 0 {
		if user.HandrawFree <= 0 {
			if user.Coin-user.FrozenCoin < 20 {
				logger.Error(errors.New("幸运值不足"))
				//errmsg.Abort(c, errmsg.FAIL, "免费次数已用完，幸运值不足，请充值或者明天再来")
				//return

				var balance model.CoinBalance
				countAd, err := balance.CountByOrderTypeToady(claims.UserId, enums.OrderTypeEnum.AdReward)
				if err != nil {
					logger.Error(err)
				}
				if userEnv.UniPlatform == "mp-weixin" {
					result := make(map[string]interface{})
					result["ad_reward_coin"] = 100
					result["ad_leave_count"] = 10 - countAd

					c.JSON(http.StatusOK, gin.H{
						"code":   errmsg.CoinNotEnough,
						"result": result,
						"msg":    fmt.Sprintf("观看广告立即获得幸运值，今日仅剩%d次", 10-countAd),
					})
					return
				} else {
					errmsg.Abort(c, errmsg.CoinNotEnough, "幸运值不足，请充值或者明天再来")
					return
				}
			} else {
				priceCoin = 20
			}
		}
	} else {
		if user.Coin-user.FrozenCoin < 20 {
			//logger.Error(errors.New("幸运值不足"))
			//errmsg.Abort(c, errmsg.FAIL, "免费次数已用完，幸运值不足，请充值或者明天再来")
			//return

			var balance model.CoinBalance
			countAd, err := balance.CountByOrderTypeToady(claims.UserId, enums.OrderTypeEnum.AdReward)
			if err != nil {
				logger.Error(err)
			}
			if userEnv.UniPlatform == "mp-weixin" {
				result := make(map[string]interface{})
				result["ad_reward_coin"] = 100
				result["ad_leave_count"] = 10 - countAd

				c.JSON(http.StatusOK, gin.H{
					"code":   errmsg.CoinNotEnough,
					"result": result,
					"msg":    fmt.Sprintf("观看广告立即获得幸运值，今日仅剩%d次", 10-countAd),
				})
				return
			} else {
				errmsg.Abort(c, errmsg.FAIL, "幸运值不足，请充值或者明天再来")
				return
			}

		}
	}

	//20221218/user-cd/cd6fa037da4e59ee94b8db4fc87c163e.base64
	//20221218/out-cd/cd6fa037da4e59ee94b8db4fc87c163e.base64
	//https://aigc.cyuai.com/output/20221218/out-cd/cd6fa037da4e59ee94b8db4fc87c163e.base64

	//filepath := config.TempImgFilePath + filename
	//relativePath := fmt.Sprintf("%s/user-%s/%s", time.Now().Format("20060102"), pre2, filename)

	uuid := uuid.New()
	uuidStr := strings.Replace(uuid.String(), "-", "", -1)
	inputMd5 := uuidStr

	inputRelativePath := fmt.Sprintf("%s/handraw/", time.Now().Format("20060102"))
	inputFilename := uuidStr + strings.ToLower(ext)
	inputFilePath := inputRelativePath + inputFilename

	handraw := model.Handraw{
		UserId:       claims.UserId,
		HandUuid:     uuidStr,
		HandFrom:     handFrom,
		InputImgMd5:  inputMd5,
		InputImgPath: inputFilePath,
		DrawPath:     drawPath,
		DrawType:     drawType,
		Prompt:       "",
		GuessUserId:  0,
		PushJson:     "{}",
		BatchCount:   batchCount,
		PriceCoin:    priceCoin,
	}

	if err := handraw.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "创建绘图失败")
		return
	}

	//if batchCount == 0 {
	//	oMd5Str := fmt.Sprintf("%d,%s", user.ID, handraw.CreatedAt.Format("2006-01-02 15:04:05.000"))
	//	has := md5.Sum([]byte(oMd5Str))
	//	md5Str := hex.EncodeToString(has[:])
	//	if len(md5Str) != 32 {
	//		logger.Error(errors.New("md5位数不正确"))
	//		errmsg.Abort(c, errmsg.FAIL, "手绘图名称生成失败")
	//		return
	//	}
	//
	//	if err := handraw.SetOutputImgMd5(md5Str); err != nil {
	//		logger.Error(err)
	//		errmsg.Abort(c, errmsg.FAIL, "设置手绘图标识出错")
	//		return
	//	}
	//}

	if parentUuid == "" {
		dirPath := config.DiffusionFilePath + inputRelativePath
		filepath := config.DiffusionFilePath + inputFilePath

		if _, err := os.Stat(dirPath); os.IsNotExist(err) {
			// mkdir 创建目录，mkdirAll 可创建多层级目录
			if err := os.MkdirAll(dirPath, os.ModePerm); err != nil {
				logger.Error(err, dirPath)
				errmsg.Abort(c, errmsg.FAIL, "创建路径失败")
				return
			}
		}

		if err := c.SaveUploadedFile(f, filepath); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "图片保存失败")
			return
		}
		img, _, err := myimg.FileToImg(filepath)
		if err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "获取图片信息失败")
			return
		}
		width := img.Bounds().Size().X
		height := img.Bounds().Size().Y

		if err := handraw.SetStateAndSize(enums.HandrawStateEnum.Uploaded, width, height); err != nil {
			logger.Error(err)
		}
		if user.Openid != "" {

			small := myimg.ResizeImg(256, 256, img, true)
			smallFilePath := service.ImgData.GetSmallImagePath(filepath)
			if err := myimg.ImgToFile(small, smallFilePath); err != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "缩略图生成失败")
			}

			imgSecCheckResp, _, err := weixin.ImgSecCheck(smallFilePath)

			if err := os.Remove(smallFilePath); err != nil && !os.IsNotExist(err) {
				logger.Error("删除缩略图失败", smallFilePath, err)
			}

			if err != nil {
				logger.Error(err)
				if err := handraw.SetState(enums.HandrawStateEnum.SecFail); err != nil {
					logger.Error(err)
				}
				errmsg.Abort(c, errmsg.FAIL, "图片校验失败")
				return
			}
			if imgSecCheckResp.ErrCode == 87014 {
				if err := handraw.SetState(enums.HandrawStateEnum.SecRisky); err != nil {
					logger.Error(err)
				}
				if err := os.Remove(filepath); err != nil {
					logger.Error(err)
				}
				if err := os.Remove(filepath); err != nil {
					logger.Error(err)
				}
				errmsg.Abort(c, errmsg.FAIL, "图片内容校验未通过，请更换图片")
				return
			}

			if imgSecCheckResp.ErrCode != 0 {
				logger.Error("图片校验出错", imgSecCheckResp, " userId:", userId)
				if err := handraw.SetState(enums.HandrawStateEnum.SecFail); err != nil {
					logger.Error(err)
				}
				errmsg.Abort(c, errmsg.FAIL, "图片内容校验失败，请重试")
				return
			}

			if imgSecCheckResp.ErrCode == 0 {
				if err := handraw.SetState(enums.HandrawStateEnum.SecPass); err != nil {
					logger.Error(err)
				}
			}
		}

	} else {
		if err := handraw.SetState(enums.HandrawStateEnum.Uploaded); err != nil {
			logger.Error(err)
		}
	}

	//开始扣费或者扣免费次数
	if err := model.Transactions.HandrawCost(&handraw); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "计费失败")
		return
	}

	json := service.HandrawService.GetPushTemplate(handraw.ID)
	if json == "" {
		errmsg.Abort(c, errmsg.FAIL, "生成绘图数据失败")
		return
	}
	if err := handraw.SetPushJson(json); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存绘图数据失败")
		return
	}

	result := make(map[string]interface{})

	resp := addResp{
		HandUuid:    handraw.HandUuid,
		InputImgMd5: handraw.InputImgMd5,
		InputImgUrl: config.DiffusionDomain + inputFilePath,
	}
	result["handraw"] = resp

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "添加成功",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "数据上传失败")
	}
}

func (obj handrawApi) Guess(c *gin.Context) {
	var code int
	var oReq guessReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	userId := claims.UserId

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var user model.User
	err = user.GetByID(claims.UserId)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	handUuid := oReq.HandUuid
	prompt := oReq.Prompt

	if prompt == "" {
		errmsg.Abort(c, errmsg.FAIL, "请输入您要猜的内容")
		return
	}
	checkMsg, err := weixin.MsgSecCheckResult(prompt, user.Openid)
	if err == nil && checkMsg != "" {
		logger.Error("userID:", claims.UserId, " checkMsg:", checkMsg, "  ", prompt)
		errmsg.Abort(c, errmsg.FAIL, checkMsg)
		return
	}
	if err != nil {
		logger.Error(err)
	}

	var handraw model.Handraw
	if err := handraw.GetByUuid(handUuid); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取数据出错")
		return
	}

	oMd5Str := fmt.Sprintf("%s,%d,%s,%d", handraw.HandUuid, userId, time.Now().Format("2006-01-02 15:04:05.000"), utils.GetRandom(1000, 9999))
	has := md5.Sum([]byte(oMd5Str))
	md5Str := hex.EncodeToString(has[:])
	if len(md5Str) != 32 {
		logger.Error(errors.New("md5位数不正确"))
		errmsg.Abort(c, errmsg.FAIL, "手绘图名称生成失败")
		return
	}
	outImg := model.OutImg{
		UserId:      handraw.UserId,
		GuessUserId: userId,
		Prompt:      prompt,
		OrigWhere:   enums.OrigWhereEnum.Handraw,
		OrigId:      handraw.ID,
		Md5:         md5Str,
	}

	if err := outImg.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "批量添加数据出错")
	}

	transPrompt := ""
	if len(outImg.Prompt) > 0 {
		transPrompt = service.TencentCloud.TextTranslate(outImg.Prompt)
		if utf8.RuneCountInString(transPrompt) == 0 || utf8.RuneCountInString(transPrompt) < utf8.RuneCountInString(outImg.Prompt) {
			logger.Error("翻译失效 ", handraw.ID, transPrompt, handraw.Prompt)
			transPrompt = outImg.Prompt
		}
	}

	endJson := strings.Replace(handraw.PushJson, "{==={CustomMd5}===}", outImg.Md5, -1)
	endJson = strings.Replace(endJson, "{==={Prompt}===}", transPrompt, -1)
	if _, err := service.HandrawService.PushRedisQueue(endJson); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "发送绘图数据失败")
		return
	}

	result := make(map[string]interface{})

	resp := guessResp{
		HandUuid:     handraw.HandUuid,
		InputImgMd5:  handraw.InputImgMd5,
		InputImgUrl:  config.DiffusionDomain + handraw.InputImgPath,
		OutputImgMd5: md5Str,
	}
	result["handraw"] = resp

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "猜好了",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "数据上传失败")
	}
}

func (obj handrawApi) Add(c *gin.Context) {
	var code int

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	userId := claims.UserId

	prompt, _ := c.GetPostForm("prompt")

	checkMsg, err := weixin.MsgSecCheckResult(prompt, "")
	if err == nil && checkMsg != "" {
		errmsg.Abort(c, errmsg.FAIL, checkMsg)
		return
	}
	if err != nil {
		logger.Error(err)
	}

	parentUuid, _ := c.GetPostForm("parent_uuid")

	drawPath, _ := c.GetPostForm("draw_path")
	if drawPath == "" {
		//errmsg.Abort(c, errmsg.FAIL, "请画图")
		//return
		drawPath = "[]"
	}

	from, _ := c.GetPostForm("from")

	drawType := 0
	drawTypeStr, _ := c.GetPostForm("draw_type")
	if drawTypeStr != "" {
		if num, err := strconv.Atoi(drawTypeStr); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		} else {
			drawType = num
		}
	}

	handFrom := 0
	handFromStr, _ := c.GetPostForm("hand_from")
	if handFromStr != "" {
		if num, err := strconv.Atoi(handFromStr); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		} else {
			handFrom = num
		}
	}

	priceCoin := 0
	priceCoinStr, _ := c.GetPostForm("price_coin")
	if priceCoinStr != "" {
		if num, err := strconv.Atoi(priceCoinStr); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		} else {
			priceCoin = num
			if priceCoin != 0 && priceCoin < 20 {
				logger.Error("err priceCoin:", priceCoin)
				errmsg.Abort(c, errmsg.FAIL, "参数错误")
				return
			}
		}
	}
	if priceCoin < 0 {
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	} else if priceCoin > 0 && priceCoin != 20 {
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	batchCount := 0
	batchCountStr, _ := c.GetPostForm("batch_count")
	if batchCountStr != "" {
		if num, err := strconv.Atoi(batchCountStr); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		} else {
			batchCount = num
		}
	}

	ext := ""
	var f *multipart.FileHeader
	if parentUuid == "" {
		ff, err := c.FormFile("file")
		if err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
			return
		}
		ext = path.Ext(ff.Filename) // 输出 .html
		f = ff
	}

	var user model.User
	if err := user.GetByID(userId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	if from == "children" { //儿童版手绘
		priceCoin = 0
		if user.HandrawFree <= 0 {
			if user.ChildrenExpires.Before(time.Now()) {
				logger.Error(errors.New("会员已到期，请开通会员"))
				errmsg.Abort(c, errmsg.FAIL, "会员已到期，请开通会员")
				return
			}
		}
	} else {

		if priceCoin == 0 {
			if user.HandrawFree <= 0 {
				if user.Coin-user.FrozenCoin < 20 {
					logger.Error(errors.New("幸运值不足"))
					errmsg.Abort(c, errmsg.FAIL, "免费次数已用完，幸运值不足，请充值或者明天再来")
					return
				} else {
					priceCoin = 20
				}
			}
		} else {
			if user.Coin-user.FrozenCoin < 20 {
				logger.Error(errors.New("幸运值不足"))
				errmsg.Abort(c, errmsg.FAIL, "免费次数已用完，幸运值不足，请充值或者明天再来")
				return
			}
		}

	}

	//20221218/user-cd/cd6fa037da4e59ee94b8db4fc87c163e.base64
	//20221218/out-cd/cd6fa037da4e59ee94b8db4fc87c163e.base64
	//https://aigc.cyuai.com/output/20221218/out-cd/cd6fa037da4e59ee94b8db4fc87c163e.base64

	//filepath := config.TempImgFilePath + filename
	//relativePath := fmt.Sprintf("%s/user-%s/%s", time.Now().Format("20060102"), pre2, filename)

	uuid := uuid.New()
	uuidStr := strings.Replace(uuid.String(), "-", "", -1)
	inputMd5 := uuidStr

	inputRelativePath := fmt.Sprintf("%s/handraw/", time.Now().Format("20060102"))
	inputFilename := uuidStr + strings.ToLower(ext)
	inputFilePath := inputRelativePath + inputFilename

	if parentUuid != "" {
		var parent model.Handraw
		if err := parent.GetByUuid(parentUuid); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "未找到引用的数据")
			return
		}
		inputMd5 = parent.InputImgMd5
		inputFilePath = parent.InputImgPath
	}
	//inputMd5 = "cbe2492b8643475aabde66e8dff75344"
	//inputFilePath = "20230223/handraw/cbe2492b8643475aabde66e8dff75344.jpg"

	handraw := model.Handraw{
		UserId:       claims.UserId,
		HandUuid:     uuidStr,
		HandFrom:     handFrom,
		InputImgMd5:  inputMd5,
		InputImgPath: inputFilePath,
		DrawPath:     drawPath,
		DrawType:     drawType,
		Prompt:       prompt,
		GuessUserId:  claims.UserId,
		PushJson:     "{}",
		BatchCount:   batchCount,
		PriceCoin:    priceCoin,
	}

	if err := handraw.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "创建绘图失败")
		return
	}

	if batchCount == 0 {
		oMd5Str := fmt.Sprintf("%d,%s", user.ID, handraw.CreatedAt.Format("2006-01-02 15:04:05.000"))
		has := md5.Sum([]byte(oMd5Str))
		md5Str := hex.EncodeToString(has[:])
		if len(md5Str) != 32 {
			logger.Error(errors.New("md5位数不正确"))
			errmsg.Abort(c, errmsg.FAIL, "手绘图名称生成失败")
			return
		}

		if err := handraw.SetOutputImgMd5(md5Str); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "设置手绘图标识出错")
			return
		}
	}

	if parentUuid == "" {
		dirPath := config.DiffusionFilePath + inputRelativePath
		filepath := config.DiffusionFilePath + inputFilePath

		if _, err := os.Stat(dirPath); os.IsNotExist(err) {
			// mkdir 创建目录，mkdirAll 可创建多层级目录
			if err := os.MkdirAll(dirPath, os.ModePerm); err != nil {
				logger.Error(err, dirPath)
				errmsg.Abort(c, errmsg.FAIL, "创建路径失败")
				return
			}
		}

		if err := c.SaveUploadedFile(f, filepath); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "图片保存失败")
			return
		}
		img, _, err := myimg.FileToImg(filepath)
		if err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "获取图片信息失败")
			return
		}
		width := img.Bounds().Size().X
		height := img.Bounds().Size().Y

		if err := handraw.SetStateAndSize(enums.HandrawStateEnum.Uploaded, width, height); err != nil {
			logger.Error(err)
		}
		if user.Openid != "" {

			small := myimg.ResizeImg(256, 256, img, true)
			smallFilePath := service.ImgData.GetSmallImagePath(filepath)
			if err := myimg.ImgToFile(small, smallFilePath); err != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "缩略图生成失败")
			}

			imgSecCheckResp, _, err := weixin.ImgSecCheck(smallFilePath)

			if err := os.Remove(smallFilePath); err != nil && !os.IsNotExist(err) {
				logger.Error("删除缩略图失败", smallFilePath, err)
			}

			if err != nil {
				logger.Error(err)
				if err := handraw.SetState(enums.HandrawStateEnum.SecFail); err != nil {
					logger.Error(err)
				}
				errmsg.Abort(c, errmsg.FAIL, "图片校验失败")
				return
			}
			if imgSecCheckResp.ErrCode == 87014 {
				if err := handraw.SetState(enums.HandrawStateEnum.SecRisky); err != nil {
					logger.Error(err)
				}
				if err := os.Remove(filepath); err != nil {
					logger.Error(err)
				}
				if err := os.Remove(filepath); err != nil {
					logger.Error(err)
				}
				errmsg.Abort(c, errmsg.FAIL, "图片内容校验未通过，请更换图片")
				return
			}

			if imgSecCheckResp.ErrCode != 0 {
				logger.Error("图片校验出错", imgSecCheckResp, " userId:", userId)
				if err := handraw.SetState(enums.HandrawStateEnum.SecFail); err != nil {
					logger.Error(err)
				}
				errmsg.Abort(c, errmsg.FAIL, "图片内容校验失败，请重试")
				return
			}

			if imgSecCheckResp.ErrCode == 0 {
				if err := handraw.SetState(enums.HandrawStateEnum.SecPass); err != nil {
					logger.Error(err)
				}
			}
		}

	} else {
		if err := handraw.SetState(enums.HandrawStateEnum.Uploaded); err != nil {
			logger.Error(err)
		}
	}

	//开始扣费或者扣免费次数
	if err := model.Transactions.HandrawCost(&handraw); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "计费失败")
		return
	}

	json := service.HandrawService.GetPushJson(handraw.ID)
	if json == "" {
		errmsg.Abort(c, errmsg.FAIL, "生成绘图数据失败")
		return
	}
	if err := handraw.SetPushJson(json); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存绘图数据失败")
		return
	}

	var outImgsMd5 []string
	if batchCount == 0 {
		if _, err := service.HandrawService.PushRedisQueue(json); err != nil {
			errmsg.Abort(c, errmsg.FAIL, "发送绘图数据失败")
			return
		}
	} else {
		var outImages []model.OutImg
		for i := 0; i < batchCount; i++ {
			oMd5Str := fmt.Sprintf("%d,%s,%d", user.ID, handraw.CreatedAt.Format("2006-01-02 15:04:05.000"), i)
			has := md5.Sum([]byte(oMd5Str))
			md5Str := hex.EncodeToString(has[:])
			if len(md5Str) != 32 {
				logger.Error(errors.New("md5位数不正确"))
				errmsg.Abort(c, errmsg.FAIL, "手绘图名称生成失败")
				return
			}
			outImg := model.OutImg{
				UserId:    userId,
				OrigWhere: enums.OrigWhereEnum.Handraw,
				OrigId:    handraw.ID,
				Md5:       md5Str,
			}
			outImgsMd5 = append(outImgsMd5, md5Str)
			outImages = append(outImages, outImg)
		}
		if err := outImages[0].BatchCreate(outImages); err != nil {
			errmsg.Abort(c, errmsg.FAIL, "批量添加数据出错")
		}

		for _, item := range outImages {
			endJson := strings.Replace(json, "{{CustomMd5}}", item.Md5, -1)
			if _, err := service.HandrawService.PushRedisQueue(endJson); err != nil {
				errmsg.Abort(c, errmsg.FAIL, "发送绘图数据失败")
				return
			}
		}

	}

	result := make(map[string]interface{})

	resp := addResp{
		HandUuid:      handraw.HandUuid,
		InputImgMd5:   handraw.InputImgMd5,
		InputImgUrl:   config.DiffusionDomain + inputFilePath,
		OutputImgsMd5: outImgsMd5,
	}
	result["handraw"] = resp

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "添加成功",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "数据上传失败")
	}
}

func (obj handrawApi) Publish(c *gin.Context) {
	var code int
	var msg string
	var oReq publishReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	var outImg model.OutImg
	if err := outImg.GetByMd5(oReq.Md5); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取图片数据出错")
		return
	}
	if outImg.UserId != claims.UserId {
		errmsg.Abort(c, errmsg.FAIL, "权限出错")
		return
	}
	if outImg.Share == enums.ShareEnum.Forbid {
		errmsg.Abort(c, errmsg.FAIL, "该图片已禁止发布")
		return
	}

	share := enums.ShareEnum.Normal
	if oReq.Share == enums.ShareEnum.Applying {
		share = enums.ShareEnum.Applying
	}
	//logger.Info("share:", share, " enums.ShareEnum.Applying:", enums.ShareEnum.Applying)

	if err := outImg.SetShare(share); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "发布出错")
		return
	}
	result := make(map[string]interface{})
	result["md5"] = oReq.Md5
	result["share"] = share
	c.JSON(http.StatusOK, gin.H{
		"code":   code,
		"msg":    "发布成功",
		"result": result,
	})
}

func (obj handrawApi) GetShareListFall(c *gin.Context) {
	var code int
	var msg string
	var oReq listFallReq

	//claims := c.Value("claims").(*middleware.MyClaims)
	//if claims.UserId <= 0 {
	//	errmsg.Abort(c, errmsg.FAIL, "请先登录")
	//	return
	//}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if oReq.PageSize <= 0 || oReq.PageSize > 50 {
		oReq.PageSize = 10
	}

	var outImg model.OutImg
	ary, err := outImg.GetShareListFall(enums.OrigWhereEnum.Handraw, enums.ShareEnum.Applying, oReq.LastId, oReq.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取瀑布流数据出错")
		return
	}

	outAry := make([]listFallResp, 0)
	for _, item := range ary {
		imgUrl := ""
		if item.Path != "" {
			imgUrl = config.DiffusionDomain + item.Path
		}
		var handraw model.Handraw
		if err := handraw.GetById(item.OrigId); err == nil {
			inputImgUrl := ""
			if len(handraw.InputImgPath) > 32 {
				inputImgUrl = config.DiffusionDomain + handraw.InputImgPath
			}
			resp := listFallResp{
				HandUuid:     handraw.HandUuid,
				InputImgUrl:  inputImgUrl,
				Width:        handraw.Width,
				Height:       handraw.Height,
				OutputImgMd5: item.Md5,
				OutputImgUrl: imgUrl,
			}
			outAry = append(outAry, resp)
		}
	}
	result := make(map[string]interface{})
	result["items"] = outAry
	if len(ary) > 0 {
		result["last_id"] = ary[len(ary)-1].ID
	} else {
		result["last_id"] = 0
	}

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})

	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

func (obj handrawApi) GetPublishImgList(c *gin.Context) {
	var code int
	var msg string
	var oReq listOutImgReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 50 {
		oReq.PageSize = 10
	}

	origId := uint(0)
	if oReq.HandUuid != "" {
		var handraw model.Handraw
		if err := handraw.GetByUuid(oReq.HandUuid); err != nil {
			logger.Error(err, "oReq.HandUuid:", oReq.HandUuid)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		}
		origId = handraw.ID
	}

	var outImg model.OutImg
	ary, total, err := outImg.GetList(claims.UserId, enums.OrigWhereEnum.Handraw, origId, 2, oReq.Page, oReq.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取图片列表数据出错")
		return
	}

	outAry := make([]listOutImgResp, 0)
	for _, item := range ary {
		imgUrl := ""
		if item.Path != "" {
			imgUrl = config.DiffusionDomain + item.Path
		}
		var handraw model.Handraw
		if err := handraw.GetById(item.OrigId); err == nil {
			inputImgUrl := ""
			if len(handraw.InputImgPath) > 32 {
				inputImgUrl = config.DiffusionDomain + handraw.InputImgPath
			}
			resp := listOutImgResp{
				HandUuid:     handraw.HandUuid,
				InputImgUrl:  inputImgUrl,
				Width:        handraw.Width,
				Height:       handraw.Height,
				OutputImgMd5: item.Md5,
				OutputImgUrl: imgUrl,
			}
			outAry = append(outAry, resp)
		}
	}

	result := make(map[string]interface{})
	result["items"] = outAry
	result["total"] = total

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})

	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

func (obj handrawApi) GetOutImgList(c *gin.Context) {
	var code int
	var msg string
	var oReq listOutImgReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 50 {
		oReq.PageSize = 10
	}

	origId := uint(0)
	searchUserId := claims.UserId
	if oReq.HandUuid != "" {
		var handraw model.Handraw
		if err := handraw.GetByUuid(oReq.HandUuid); err != nil {
			logger.Error(err, "oReq.HandUuid:", oReq.HandUuid)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		}
		origId = handraw.ID
	}
	if origId > 0 {
		searchUserId = 0
	}

	var outImg model.OutImg
	ary, total, err := outImg.GetList(searchUserId, enums.OrigWhereEnum.Handraw, origId, -1, oReq.Page, oReq.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取图片列表数据出错")
		return
	}

	//outAry := make([]listOutImgResp, 0)
	//for _, item := range ary {
	//	imgUrl := ""
	//	if item.Path != "" {
	//		imgUrl = config.DiffusionDomain + item.Path
	//	}
	//	outAry = append(outAry, listOutImgResp{OutputImgUrl: imgUrl, OutputImgMd5: item.Md5})
	//}

	outAry := make([]listOutImgResp, 0)
	for _, item := range ary {
		imgUrl := ""
		if item.Path != "" {
			imgUrl = config.DiffusionDomain + item.Path
		}
		var handraw model.Handraw
		if err := handraw.GetById(item.OrigId); err == nil {
			inputImgUrl := ""
			if len(handraw.InputImgPath) > 32 {
				inputImgUrl = config.DiffusionDomain + handraw.InputImgPath
			}
			resp := listOutImgResp{
				HandUuid:     handraw.HandUuid,
				InputImgUrl:  inputImgUrl,
				Width:        handraw.Width,
				Height:       handraw.Height,
				OutputImgMd5: item.Md5,
				OutputImgUrl: imgUrl,
				Share:        item.Share,
			}
			outAry = append(outAry, resp)
		}
	}

	result := make(map[string]interface{})
	result["items"] = outAry
	result["total"] = total

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})

	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

func (obj handrawApi) GetDetail(c *gin.Context) {
	var code int
	var msg string
	var oReq detailReq

	//claims := c.Value("claims").(*middleware.MyClaims)
	//if claims.UserId <= 0 {
	//	errmsg.Abort(c, errmsg.FAIL, "请先登录")
	//	return
	//}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	var handraw model.Handraw
	if err := handraw.GetByUuid(oReq.HandUuid); err != nil {
		logger.Error(err, "oReq.HandUuid:", oReq.HandUuid)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}
	outputImgUrl := ""
	if len(handraw.OutputImgPath) > 32 {
		outputImgUrl = config.DiffusionDomain + handraw.OutputImgPath
	}
	inputImgUrl := ""
	if len(handraw.InputImgPath) > 32 {
		inputImgUrl = config.DiffusionDomain + handraw.InputImgPath
	}

	resp := detailResp{
		UserId:       handraw.UserId,
		HandUuid:     handraw.HandUuid,
		InputImgMd5:  handraw.InputImgMd5,
		InputImgUrl:  inputImgUrl,
		Prompt:       handraw.Prompt,
		DrawPath:     handraw.DrawPath,
		DrawType:     handraw.DrawType,
		OutputImgUrl: outputImgUrl,
	}

	result := make(map[string]interface{})
	result["handraw"] = resp

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
	}
}

func (obj handrawApi) GetState(c *gin.Context) {
	var code int
	var msg string
	var oReq stateReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	var handraw model.Handraw
	if err := handraw.GetByUuid(oReq.HandUuid); err != nil {
		logger.Error(err, "oReq.HandUuid:", oReq.HandUuid)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}
	outputImgUrl := ""
	result := make(map[string]interface{})
	if len(handraw.OutputImgPath) > 32 {
		outputImgUrl = config.DiffusionDomain + handraw.OutputImgPath

		resp := addResp{
			HandUuid:     handraw.HandUuid,
			InputImgMd5:  handraw.InputImgMd5,
			InputImgUrl:  config.DiffusionDomain + handraw.InputImgPath,
			OutputImgUrl: outputImgUrl,
		}
		result["handraw"] = resp
		if code == errmsg.SUCCESS {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    "",
				"result": result,
			})
		}
		return
	}

	resp := stateResp{
		HandUuid:     handraw.HandUuid,
		OutputImgUrl: outputImgUrl,
	}

	result["handraw"] = resp

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "数据上传失败")
	}
}

func (obj handrawApi) GetStateByMd5(c *gin.Context) {
	var code int
	var msg string
	var oReq stateMd5Req

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	var outImg model.OutImg
	if err := outImg.GetByMd5(oReq.OutputImgMd5); err != nil {
		logger.Error(err, "oReq.ImgMd5:", oReq.OutputImgMd5)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	outputImgUrl := ""
	if len(outImg.Path) > 32 {
		outputImgUrl = config.DiffusionDomain + outImg.Path
	}

	resp := stateMd5Resp{
		OutputImgMd5: oReq.OutputImgMd5,
		OutputImgUrl: outputImgUrl,
	}

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": resp,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "获取状态失败")
	}
}

var HandrawApi handrawApi

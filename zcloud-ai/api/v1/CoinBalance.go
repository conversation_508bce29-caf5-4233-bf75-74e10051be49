package v1

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"zcloud-ai/middleware"
	"zcloud-ai/model"
	"zcloud-ai/model/req"
	"zcloud-ai/utils/errmsg"
)

type coinBalanceApi struct {
}

func (obj coinBalanceApi) GetList(c *gin.Context) {
	var code int
	var msg string
	var listReq req.CoinBalanceListReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	er := c.ShouldBindJSON(&listReq)
	if er != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if listReq.PageSize < 1 {
		listReq.PageSize = 1
	}
	if listReq.Page < 1 {
		listReq.Page = 1
	}

	var o model.CoinBalance
	data, total, err := o.GetList(claims.UserId, listReq.KW, listReq.Page, listReq.PageSize)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询出错")
		return
	}

	result := make(map[string]interface{})
	result["items"] = data
	result["total"] = total

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":    code,
			"message": "",
			"msg":     "",
			"result":  result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

var CoinBalanceApi coinBalanceApi

package v1

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"zcloud-ai/utils/errmsg"
)

// Logout 前台退出
func Logout(c *gin.Context) {
	var code int

	/*
		claims := c.Value("claims").(*middleware.MyClaims)
		if claims.UserId <= 0 {
			errmsg.Abort(c, errmsg.FAIL, "请先登录")
			return
		}*/

	//{"code":0,"message":"Token has been destroyed","type":"success"}
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":    code,
			"message": "Token has been destroyed",
			"msg":     "Token has been destroyed",
			"type":    "success",
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "出错了")
	}

}

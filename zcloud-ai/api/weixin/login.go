package weixin

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"net/http"
	"strings"
	"time"
	"zcloud-ai/enums"
	"zcloud-ai/middleware"
	"zcloud-ai/model"
	"zcloud-ai/model/req"
	"zcloud-ai/service"
	"zcloud-ai/utils"
	"zcloud-ai/utils/config"
	"zcloud-ai/utils/errmsg"
	"zcloud-ai/utils/logger"
)

func Login(c *gin.Context) {
	var code int
	var msg string
	var token string

	var regReq RegReq
	var user model.User

	er := c.ShouldBindJSON(&regReq)
	if er != nil {
		logger.Error("数据获取失败")
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if len(regReq.JsCode) < 5 {
		logger.Error("参数错误")
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	logger.Info("开始微信登录", regReq.JsCode)
	code2SessionResp, err := GetOpenId(regReq.JsCode)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取OpenId错误")
		return
	}

	err = user.GetByOpenid(code2SessionResp.Openid)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "查询OpenId失败")
		return
	}

	logger.Info("微信登录：", code2SessionResp.Openid, "  ", user.ID)
	if user.ID > 0 && len(user.Mobile) == 0 {
		if user.PhoneInfo != "" {
			var resp PhoneNumberResp
			resp.ErrCode = -1
			err = json.Unmarshal([]byte(user.PhoneInfo), &resp)
			if err == nil && resp.ErrCode == 0 {
				user.Mobile = resp.PhoneInfo.PhoneNumber
			}
		}
	}

	if user.ID > 0 {
		if user.Coin < 100 {

		}
	}

	if user.ID == 0 {
		logger.Info("开始注册新用户 openid:", code2SessionResp.Openid, " UserSystem:", regReq.UserSystem)
		user.Openid = code2SessionResp.Openid
		user.Unionid = code2SessionResp.Unionid
		user.Platform = enums.PlatFormEnum.WeiXin
		if len(regReq.ShareUuid) == 32 {
			user.InvitedCode = regReq.ShareUuid
		}
		user.PhoneInfo = "{}"
		user.Plat = enums.PlatEnum.WeiXin
		user.UserSystem = "{}"
		if regReq.UserSystem != "" {
			userSystemStr := strings.Replace(regReq.UserSystem, `"lat":"",`, `"lat":0,`, -1)
			userSystemStr = strings.Replace(userSystemStr, `"lat": "",`, `"lng":0,`, -1)
			userSystemStr = strings.Replace(userSystemStr, `"lng":"",`, `"lng":0,`, -1)
			userSystemStr = strings.Replace(userSystemStr, `"lng": "",`, `"lng":0,`, -1)
			logger.Info("替换后userSystem:", userSystemStr)
			var userSystem model.UserEnv
			if err := utils.GetStructFromJson(&userSystem, userSystemStr); err != nil {
				logger.Error(err)
				user.UserSystem = regReq.UserSystem
			} else {
				userSystem.Ip = utils.GetClientIp(c.Request.Header)
				jsonStr := utils.GetJsonFromStruct(userSystem)
				logger.Info("复制IP userSystem:", jsonStr)
				if jsonStr != "" {
					user.UserSystem = jsonStr
				}
			}
		} else {
			userEnvStr := c.Request.Header.Get("User-Env")
			logger.Info("userEnvStr:", userEnvStr)
			if userEnvStr != "" {
				var userSystem model.UserEnv
				if err := utils.GetStructFromJson(&userSystem, userEnvStr); err != nil {
					logger.Error(err, userEnvStr)
					user.UserSystem = userEnvStr
				} else {
					userSystem.Ip = utils.GetClientIp(c.Request.Header)
					jsonStr := utils.GetJsonFromStruct(userSystem)
					logger.Info("复制IP userSystem:", jsonStr)
					if jsonStr != "" {
						user.UserSystem = jsonStr
					}
				}
			}
		}

		err = user.Save()
		if err != nil {
			logger.Error(err)
			user.UserSystem = "{}"
			err = user.Save()
		}

		if err != nil || user.ID == 0 {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "生成记录失败")
			return
		}
		err = service.UserService.NewUserAddCoin(user.ID)
		if err != nil {
			logger.Error("赠送幸运值失败 userId:", user.ID)
		}
		go func() {
			avatar, err := service.UserService.RandomAvatar()
			if err != nil {
				logger.Error(err)
			}
			nickName, err := service.UserService.RandomNickname()
			if err != nil {
				logger.Error(err)
			}
			if err := user.SetNicknameAvatar(nickName, avatar); err != nil {
				logger.Error(err)
			}
		}()
	}

	token, err = middleware.NewJWT().SetToken(user.ID, user.Username, user.Mobile)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "生成Token失败")
		return
	}

	//loginResp := req.LoginResp{
	//	UserId:         user.ID,
	//	Username:       user.Username,
	//	Mobile:         utils.FormatMobileStar(user.Mobile),
	//	Openid:         code2SessionResp.Openid,
	//	InvitationCode: user.InvitationCode,
	//}

	avatarUrl := ""
	if user.Avatar != "" {
		avatarUrl = config.DiffusionDomain + service.ImgData.GetSmallImagePath(user.Avatar)
	}

	toDay := time.Now().Format("2006-01-02")
	ichiMoreDay := user.IchiMore.Format("2006-01-02")
	dailySignDay := user.DailySign.Format("2006-01-02")
	dailyShareDay := user.DailyShare.Format("2006-01-02")

	mobile := user.Mobile
	if mobile == "" && user.PhoneInfo != "" {
		var resp PhoneNumberResp
		resp.ErrCode = -1
		err := json.Unmarshal([]byte(user.PhoneInfo), &resp)
		if err == nil && resp.ErrCode == 0 {
			mobile = resp.PhoneInfo.PhoneNumber
		}
	}

	resp := req.UserInfoResp{
		UserId:         user.ID,
		Username:       user.Username,
		AvatarUrl:      avatarUrl,
		Mobile:         utils.FormatMobileStar(mobile),
		InvitationCode: user.InvitationCode,
		IsDailySign:    dailySignDay == toDay,
		IsIchiMore:     ichiMoreDay == toDay,
		IsDailyShare:   dailyShareDay == toDay,
		LeaveCoin:      user.Coin - user.FrozenCoin,
	}

	result := make(map[string]interface{})
	result["token"] = token
	result["user"] = resp

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":    code,
			"message": "登录成功",
			"msg":     "登录成功",

			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

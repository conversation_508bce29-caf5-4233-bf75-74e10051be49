package weixin

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
	"net/http"
	"time"
	"zcloud-ai/model"
	"zcloud-ai/utils/logger"
)

func Notify(c *gin.Context) {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("Notify:", e)
		}
	}()

	logger.Info("已经接收到微信回调v1.3")
	data, _ := c.GetRawData()
	//body := `{"id":"73ac31c1-ca7c-5521-a170-9f49590755ae","create_time":"2022-12-14T14:58:14+08:00","resource_type":"encrypt-resource","event_type":"TRANSACTION.SUCCESS","summary":"支付成功","resource":{"original_type":"transaction","algorithm":"AEAD_AES_256_GCM","ciphertext":"nzc/jDeuqpYftu3b7upbQTWuQx8XyyYBCq+JrycWS6wyQ/rf20dCEd23GeZVXoOVfc2r8vL92vvIl6+U/4isuxcIDcUjTt0mUgoPTK0lPyvsVBC0hPNdDam4Z7dEFR9lx1oVY0APengkLTEgfV/khilZGEJkEOK//xeyikUeXd4djJwFa0bkJx8Ovy8/l3K5SkoRI7zZmyhKUiWpCAeVu2v5Fy+1NMNa9PPhdNZFnVFZC4Fd9A5oeCn+zlYK3uAcLuUF35aUfUCipnRp6v0CJR4S54fqSaKFPzLkPr3Ro83f1DKXYFUnstD/8xWtqnCCoAVyxLR62WIDXKUhzBlWBjxukDRwmBnTAKoOiESPVxZnuzRt/YjP7Q5q0v8f9NaK2uMis+AOXW9ZmE6oADbylaw4SGkVth4c0ftihN9MG/Of6pGGgMY4mXHpnGXBxy8AuwbOZkMQCckdGb0nxuhZ392P3Gpm/Azn2fWN2ESkIu2nVW0HrrcNtLn2ygkxYu4Rq5m6sp9RsXd3c7cshaEExRjllGqiFKBdiAk/Lsz6FI+KbJia5yymua1XomhYLOasi+Ee6eI=","associated_data":"transaction","nonce":"a5BqG3MVwXJT"}}`
	//data = []byte(body)
	logger.Info(string(data))

	req := new(notify.Request)
	if err := json.Unmarshal(data, &req); err != nil {
		logger.Error(err)
		c.JSON(http.StatusOK, gin.H{"code": "FAIL", "message": "失败"})
		return
	}

	if req.Summary == "支付成功" {
		logger.Info("支付成功 id:", req.ID)
		plaintext, err := utils.DecryptAES256GCM(
			PayService.MchAPIv3Key(), req.Resource.AssociatedData, req.Resource.Nonce, req.Resource.Ciphertext,
		)
		if err != nil || len(plaintext) == 0 {
			logger.Error(err, "|", plaintext, "| id:", req.ID)
			c.JSON(http.StatusOK, gin.H{"code": "FAIL", "message": "失败"})
			return
		}

		content := new(payments.Transaction)
		if err := json.Unmarshal([]byte(plaintext), &content); err != nil {
			logger.Error(req.ID, err)
			c.JSON(http.StatusOK, gin.H{"code": "FAIL", "message": "失败"})
			return
		}

		if *content.TradeState == "SUCCESS" {
			var recharge model.Recharge
			if err := recharge.GetByOutTradeNo(*content.OutTradeNo); err != nil || recharge.ID <= 0 {
				logger.Error(req.ID, err)
				c.JSON(http.StatusOK, gin.H{"code": "FAIL", "message": "失败"})
				return
			}
			successTime, errTime := time.ParseInLocation("2006-01-02T15:04:05+08:00", *content.SuccessTime, time.Local) //这里按照当前时区转
			if errTime != nil {
				logger.Error(req.ID, errTime)
				c.JSON(http.StatusOK, gin.H{"code": "FAIL", "message": "失败"})
				return
			}

			req.Resource.Plaintext = plaintext
			jsonByte, err := json.Marshal(req)
			if err != nil {
				logger.Error(req.ID, err)
				c.JSON(http.StatusOK, gin.H{"code": "FAIL", "message": "失败"})
				return
			}

			recharge.PayTime = successTime
			recharge.PayCallbackJson = string(jsonByte)
			err = model.Transactions.RechargeSuccess(&recharge)
			if err != nil {
				logger.Error(req.ID, err)
				c.JSON(http.StatusOK, gin.H{"code": "FAIL", "message": "失败"})
				return
			} else {
				c.JSON(http.StatusOK, gin.H{
					"code":    "SUCCESS",
					"message": "成功",
				})
				return
			}
		}
	}
	c.JSON(http.StatusOK, gin.H{"code": "FAIL", "message": "失败"})
}

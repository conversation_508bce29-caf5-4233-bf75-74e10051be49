package auth

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"zcloud-ai/enums"
	"zcloud-ai/middleware"
	"zcloud-ai/model"
	"zcloud-ai/utils/config"
	"zcloud-ai/utils/errmsg"
	"zcloud-ai/utils/logger"
)

type outImgApi struct {
}

type delReq struct {
	OutputImgMd5 string `json:"output_img_md5"`
}

type listReq struct {
	UserId   uint   `json:"user_id"`
	HandUuid string `json:"hand_uuid"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type listResp struct {
	ID           uint   `json:"id"`
	UserId       uint   `json:"user_id"`
	Nickname     string `json:"nickname"`
	AvatarUrl    string `json:"avatar_url"`
	HandUuid     string `json:"hand_uuid"`
	GuessUserId  uint   `json:"guess_user_id"`
	Prompt       string `json:"prompt"`
	InputImgUrl  string `json:"input_img_url"`
	OutputImgMd5 string `json:"output_img_md5"`
	OutputImgUrl string `json:"output_img_url"`
	Width        int    `json:"width"`
	Height       int    `json:"height"`
	CreateAt     string `json:"create_at"`
}

type waterfallResp struct {
	UserId       uint   `json:"user_id"`
	HandUuid     string `json:"hand_uuid"`
	GuessUserId  uint   `json:"guess_user_id"`
	Prompt       string `json:"prompt"`
	OutputImgMd5 string `json:"output_img_md5"`
	OutputImgUrl string `json:"output_img_url"`
	Width        int    `json:"width"`
	Height       int    `json:"height"`
	CreateAt     string `json:"create_at"`
}

func (obj outImgApi) GetList(c *gin.Context) {
	var code int
	var msg string
	var oReq listReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 50 {
		oReq.PageSize = 10
	}

	var handraw model.Handraw
	if oReq.HandUuid != "" {
		if err := handraw.GetByUuid(oReq.HandUuid); err != nil {
			logger.Error(err, "oReq.HandUuid:", oReq.HandUuid)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		}
	}

	var outImg model.OutImg
	ary, total, err := outImg.GetList(oReq.UserId, enums.OrigWhereEnum.Handraw, handraw.ID, -1, oReq.Page, oReq.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取图片列表数据出错")
		return
	}

	outAry := make([]listResp, 0)
	for _, item := range ary {
		imgUrl := ""
		if item.Path != "" {
			imgUrl = config.DiffusionDomain + item.Path
		}

		nickName := ""
		avatarUrl := ""
		var user model.User
		if item.GuessUserId > 0 {
			if err := user.GetByID(item.GuessUserId); err != nil {
				logger.Error(err, item.GuessUserId)
			}
			if user.Username != "" {
				nickName = user.Username
			} else {
				nickName = user.Nickname
			}
			if user.Avatar != "" {
				avatarUrl = config.DiffusionDomain + user.Avatar
			}
		}

		var hand model.Handraw
		if err := hand.GetById(item.OrigId); err != nil {
			logger.Error(err)
		}

		inputImgUrl := ""
		if hand.InputImgPath != "" {
			inputImgUrl = config.DiffusionDomain + hand.InputImgPath
		}

		resp := listResp{
			ID:           item.ID,
			UserId:       item.UserId,
			Nickname:     nickName,
			AvatarUrl:    avatarUrl,
			HandUuid:     handraw.HandUuid,
			Width:        handraw.Width,
			Height:       handraw.Height,
			InputImgUrl:  inputImgUrl,
			OutputImgMd5: item.Md5,
			OutputImgUrl: imgUrl,
			Prompt:       item.Prompt,
			GuessUserId:  item.GuessUserId,
			CreateAt:     item.CreatedAt.Format("2006-01-02 15:04"),
		}
		outAry = append(outAry, resp)
	}

	result := make(map[string]interface{})
	result["items"] = outAry
	result["total"] = total

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

func (obj outImgApi) GetWaterfall(c *gin.Context) {
	var code int
	var msg string
	var oReq listReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 50 {
		oReq.PageSize = 10
	}

	var handraw model.Handraw
	if oReq.HandUuid != "" {
		if err := handraw.GetByUuid(oReq.HandUuid); err != nil {
			logger.Error(err, "oReq.HandUuid:", oReq.HandUuid)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		}
	}

	var outImg model.OutImg
	ary, total, err := outImg.GetList(oReq.UserId, enums.OrigWhereEnum.Handraw, handraw.ID, -1, oReq.Page, oReq.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取图片列表数据出错")
		return
	}

	outAry := make([]waterfallResp, 0)
	for _, item := range ary {
		imgUrl := ""
		if item.Path != "" {
			imgUrl = config.DiffusionDomain + item.Path
		}

		resp := waterfallResp{
			HandUuid:     handraw.HandUuid,
			Width:        handraw.Width,
			Height:       handraw.Height,
			OutputImgMd5: item.Md5,
			OutputImgUrl: imgUrl,
			Prompt:       item.Prompt,
			GuessUserId:  item.GuessUserId,
			CreateAt:     item.CreatedAt.Format("2006-01-02 15:04"),
		}
		outAry = append(outAry, resp)
	}

	result := make(map[string]interface{})
	result["items"] = outAry
	result["total"] = total

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})

	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

func (obj outImgApi) Del(c *gin.Context) {
	var code int
	var msg string

	var oReq delReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Delete) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	var outImg model.OutImg
	if err := outImg.GetByMd5(oReq.OutputImgMd5); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "查询记录出错")
		return
	}

	if outImg.Path != "" {
		absolutePath := config.DiffusionFilePath + outImg.Path
		if err := os.Remove(absolutePath); err != nil && !os.IsNotExist(err) {
			logger.Error(err, absolutePath)
			errmsg.Abort(c, errmsg.FAIL, "删除图片出错")
			return
		}
	}

	if err := outImg.Del(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "删除记录失败")
		return
	}

	msg = "删除成功"
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  msg,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

var OutImgApi outImgApi

package enums

import "reflect"

type productCategoryEnum_ struct {
	Consumable, NonConsumable, Subscription string
}

var ProductCategoryEnum = productCategoryEnum_{
	Consumable:    "CON", //消耗型
	NonConsumable: "NON", //非消耗型
	Subscription:  "SUB", //订阅
}

func (c productCategoryEnum_) GetKey(value string) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if vo.Field(i).Interface().(string) == value {
			return typeVo.Field(i).Name
		}
	}
	return ""
}

package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"log"
	"strconv"
	"time"
	"zcloud-ai/enums"
	"zcloud-ai/model"
	"zcloud-ai/utils/logger"
	"zcloud-ai/utils/myredis"
)

type diffusionOut_ struct {
}

func (d *diffusionOut_) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("DiffusionOut奔溃:", e)
		}
	}()
	logger.Info("DiffusionOut.Run 开始循环获取")
	testCount := -1
	for {
		value, err := d.PopRedisQueue()

		if err != nil {
			logger.Error("DiffusionOut.Run PushRedisQueue to Retry:", value, err)
			if len(value) > 0 {
				d.PushRedisQueueRetry(value)
			}
			continue
		}
		if len(value) == 0 {
			if testCount == -1 {
				logger.Info("DiffusionQueueOut:", "空字符串，获取下一个数据", " len:", len(value), testCount)
			}

			testCount += 1
			if testCount == 12 {
				logger.Info("DiffusionQueueOut:", "空字符串，获取下一个数据", " len:", len(value), testCount)
				testCount = 0
			}
			continue
		} else {
			logger.Info("DiffusionQueueOut:", value, " len:", len(value))
		}
		logger.Info("DiffusionOut json:", value)
		if err := d.SaveToImage(value); err != nil {
			logger.Error(err)
		}
	}
}

func (d *diffusionOut_) SaveToImage(value string) error {

	var resp DiffusionResp
	if err := json.Unmarshal([]byte(value), &resp); err != nil {
		logger.Error(err, value)
		d.PushRedisQueueRetry(value)
		return errors.New("解析数据失败：")
	}

	/*
		path := config.DiffusionFilePath + resp.OutputRelativePath
		exists, err := utils.PathFileExists(path)
		if err != nil {
			logger.Error(err, path)
			d.PushRedisQueueRetry(value)
			return errors.New("判断文件是否存在出错")
		}
		if !exists {
			logger.Error(err, "图片不存在", path, value)
			return errors.New("图片不存在")
		}*/

	tmp, err := strconv.ParseInt(resp.OutId, 10, 64)
	if err != nil {
		// 参数错误
		if len(value) > 0 {
			d.PushRedisQueueRetry(value)
		}
		logger.Error("strconv.ParseInt 错误，value:%v", resp)
		return err
	}
	txt2imgId := uint(tmp)
	var txt2img model.Txt2Img
	if err := txt2img.GetByID(txt2imgId); err != nil {
		if len(value) > 0 {
			d.PushRedisQueueRetry(value)
		}
		logger.Error(err)
		return err
	}
	count := txt2img.BatchSize
	if txt2img.BatchCount > txt2img.BatchSize {
		count = txt2img.BatchCount
	}

	diffImg := model.DiffImg{
		UserId:     txt2img.UserId,
		OrigWhere:  txt2img.OrigWhere,
		OrigId:     txt2img.OrigId,
		DiffWhere:  1,
		DiffId:     txt2img.ID,
		BatchNum:   resp.BatchNum,
		Md5:        resp.OutputMd5,
		Path:       resp.OutputRelativePath,
		Parameters: "{}",
	}

	customData := customData_{}
	if resp.CustomData != "" {
		if err := json.Unmarshal([]byte(resp.CustomData), &customData); err != nil {
			logger.Error(err)
			return err
		}
	}

	if customData.Level > 0 { //需要超分
		diffImg.UpdatedAt = time.Now()
	}

	if err := diffImg.Save(); err != nil || diffImg.ID == 0 {
		if len(value) > 0 {
			d.PushRedisQueueRetry(value)
		}
		logger.Error(err)
		return err
	}

	if customData.Level > 0 { //需要超分

		if txt2img.UserId == 27 {

			json, err := UpScaleServiceV2.GetUpScaleJson(diffImg.Path, diffImg.ID, customData.Level, customData.UpscaleW, customData.UpscaleH)
			if err != nil {
				logger.Error("生成超分Json出错", err)
			}
			logger.Info("超分Json:", json)
			if err := UpScaleServiceV2.PushRedisUpscaleState(diffImg.ID, customData.Level); err != nil {
				logger.Error(err)
			}
			size, err := UpScaleServiceV2.PushRedisQueue(json)
			if err != nil {
				logger.Error("Push超分队列出错", err)
			}
			logger.Info("Push超分队列成功", "size:", size, "json:", json)

		} else {

			json, err := UpScaleService.GetUpScaleJson(diffImg.Path, diffImg.ID, customData.Level, customData.UpscaleW, customData.UpscaleH)
			if err != nil {
				logger.Error("生成超分Json出错", err)
			}
			logger.Info("超分Json:", json)
			if err := UpScaleService.PushRedisUpscaleState(diffImg.ID, customData.Level); err != nil {
				logger.Error(err)
			}
			size, err := UpScaleService.PushRedisQueue(json)
			if err != nil {
				logger.Error("Push超分队列出错", err)
			}
			logger.Info("Push超分队列成功", "size:", size, "json:", json)

		}

	}

	if count == 1 {
		var ary []string
		ary = append(ary, resp.OutputRelativePath)
		jsonByte, jsonErr := json.Marshal(ary)
		if jsonErr != nil {
			logger.Error(jsonErr)
			return jsonErr
		}
		if err := txt2img.SetAnimeComplete(string(jsonByte)); err != nil {
			logger.Error(err)
			return err
		}
	}
	if count > 1 {
		startNano := time.Now().UnixMilli()
		lockKey := fmt.Sprintf("DiffusionByTxt2imgId:%d", txt2imgId)
		lock := myredis.Lock(lockKey)
		if lock {
			field := fmt.Sprintf("%d", txt2imgId)
			/*
				exist, err := myredis.HExist(enums.RedisKeyEnum.DiffusionHashOut, field)
				if err != nil {
					logger.Error(err)
					return err
				}
				if !exist {
					var slice = make([]string, count)
					by, err := json.Marshal(slice)
					if err != nil {
						logger.Error(err)
						return err
					}
					value = string(by)
				}*/

			imagesStr, err := myredis.HGet(enums.RedisKeyEnum.DiffusionHashOut, field)
			if err != nil && err.Error() != "redis: nil" {
				logger.Error(lockKey, err)
				return err
			}
			if imagesStr == "" {
				var slice = make([]string, count)
				by, err := json.Marshal(slice)
				if err != nil {
					logger.Error(lockKey, err)
					return err
				}
				imagesStr = string(by)
			}

			var aryImages []string
			if err := json.Unmarshal([]byte(imagesStr), &aryImages); err != nil {
				logger.Error(lockKey, err)
				return err
			}
			aryImages[diffImg.BatchNum] = diffImg.Path
			{
				by, err := json.Marshal(aryImages)
				if err != nil {
					logger.Error(lockKey, err)
					return err
				}
				imagesStr = string(by)
				_, err = myredis.HSet(enums.RedisKeyEnum.DiffusionHashOut, field, imagesStr)
				if err != nil {
					logger.Error(lockKey, err)
					return err
				}
			}

			bComplete := true
			for i := 0; i < len(aryImages); i++ {
				if aryImages[i] == "" {
					bComplete = false
					break
				}
			}
			if bComplete {
				logger.Info(lockKey, "全部图片接收完成")
				if err := txt2img.SetAnimeComplete(imagesStr); err != nil {
					logger.Error(lockKey, err)
					return err
				}

				n, err := myredis.HDel(enums.RedisKeyEnum.DiffusionHashOut, field)
				if err != nil {
					logger.Error(lockKey, err, n)
					return err
				}
			}
			myredis.UnLock(lockKey)
		} else {
			logger.Info(lockKey, "lock 失败 key:", lockKey)
			return err
		}
		endNano := time.Now().UnixMilli()
		runNano := endNano - startNano
		logger.Info(lockKey, "耗时:", runNano)
	}
	return nil
}

func (d *diffusionOut_) RedoComplete(txt2imgId uint) (string, error) {

	var txt2Img model.Txt2Img
	bDel := false
	if err := txt2Img.GetByID(txt2imgId); err != nil {
		if err == gorm.ErrRecordNotFound { //记录已经删除了，开始删除图片，删除hash
			bDel = true
		}
	}

	field := fmt.Sprintf("%d", txt2imgId)
	lockKey := "GetKeyValue" + field
	imagesStr, err := myredis.HGet(enums.RedisKeyEnum.DiffusionHashOut, field)
	logger.Info(lockKey, imagesStr)
	if err != nil && err.Error() != "redis: nil" {
		logger.Error(lockKey, err)
		return "", err
	}
	if imagesStr == "" {
		return "", nil
	}

	var aryImages []string
	if err := json.Unmarshal([]byte(imagesStr), &aryImages); err != nil {
		logger.Error(lockKey, err)
		return imagesStr, err
	}

	if bDel {
		for i := 0; i < len(aryImages); i++ {
			if len(aryImages[i]) > 10 {
				logger.Info(aryImages[i])
			}
		}
		n, err := myredis.HDel(enums.RedisKeyEnum.DiffusionHashOut, field)
		if err != nil {
			logger.Error(lockKey, err, n)
			return imagesStr, err
		}
		logger.Info("清理完成 txt2ImgId:", field)
		return "清理完成", err
	}

	bComplete := true
	for i := 0; i < len(aryImages); i++ {
		if aryImages[i] == "" {
			bComplete = false
			break
		}
	}
	if bComplete {
		logger.Info(lockKey, "全部图片接收完成")
		var txt2img model.Txt2Img
		if err := txt2img.GetByID(txt2imgId); err != nil || txt2img.ID == 0 {
			logger.Error(lockKey, err)
			return imagesStr, err
		}
		if err := txt2img.SetAnimeComplete(imagesStr); err != nil {
			logger.Error(lockKey, err)
			return imagesStr, err
		}
		n, err := myredis.HDel(enums.RedisKeyEnum.DiffusionHashOut, field)
		if err != nil {
			logger.Error(lockKey, err, n)
			return imagesStr, err
		}
	}
	return imagesStr, nil
}

func (d *diffusionOut_) PopRedisQueue() (string, error) {
	value, err := myredis.BRPop(enums.RedisKeyEnum.DiffusionQueueOut)
	return value, err
}

func (o *diffusionOut_) PushRedisQueueRetry(value string) (int64, error) {
	size, err := myredis.LPush(enums.RedisKeyEnum.DiffusionQueueOutRetry, value)
	if err != nil {
		log.Println(err)
	}
	return size, err
}

func (d *diffusionOut_) PopRedisQueueRetry() (DiffusionResp, string, error) {
	var resp DiffusionResp
	value, _ := myredis.BRPop(enums.RedisKeyEnum.DiffusionQueueOutRetry)
	if len(value) == 0 {
		return resp, value, errors.New("队列无数据")
	}

	err := json.Unmarshal([]byte(value), &resp)
	if err != nil {
		logger.Error(err, value)
		return resp, value, errors.New("解析数据失败：")
	}
	return resp, value, nil
}

func (o *diffusionOut_) PushRedisQueueErr(value string) (int64, error) {
	size, err := myredis.LPush(enums.RedisKeyEnum.DiffusionQueueOutErr, value)
	if err != nil {
		log.Println(err)
	}
	return size, err
}

func (d *diffusionOut_) PopRedisQueueErr() (DiffusionResp, string, error) {
	var resp DiffusionResp
	value, _ := myredis.BRPop(enums.RedisKeyEnum.DiffusionQueueOutErr)
	if len(value) == 0 {
		return resp, value, errors.New("队列无数据")
	}

	err := json.Unmarshal([]byte(value), &resp)
	if err != nil {
		logger.Error(err, value)
		return resp, value, errors.New("解析数据失败：")
	}
	return resp, value, nil
}

type DiffusionResp struct {
	OutId              string `json:"out_id"`
	BatchNum           int    ` json:"batch_num"`
	OutputMd5          string `json:"output_md5"`
	OutputRelativePath string `json:"output_relative_path"`
	CustomData         string `json:"custom_data"`
}

type customData_ struct {
	UpscaleW  int  `json:"upscale_w"`
	UpscaleH  int  `json:"upscale_h"`
	Level     int  `json:"level"`
	Txt2imgId uint `json:"txt2img_id"`
}

var DiffusionOut diffusionOut_

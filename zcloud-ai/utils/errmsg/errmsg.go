package errmsg

import (
	"github.com/gin-gonic/gin"
	"net/http"
)

const (
	SUCCESS       = 0
	FAIL          = 1
	TokenInvalid  = 3
	CoinNotEnough = 2
)

func Abort(c *gin.Context, code int, msg string) {
	if code == SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":    code,
			"result":  nil,
			"message": msg,
			"msg":     msg,
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code":    code,
			"result":  nil,
			"message": msg,
			"msg":     msg,
		})
	}

	c.Abort()
}

package model

import "gorm.io/gorm"

type SdLora struct {
	gorm.Model
	Title  string `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	Hash   string `json:"hash" gorm:"type:varchar(50);not null;default:'';comment:哈希"`
	Path   string `json:"path" gorm:"type:varchar(50);not null;default:'';comment:文件路径"`
	Cover  string `json:"cover" gorm:"type:varchar(100);not null;default:'';comment:封面图像"`
	Remark string `json:"remark" gorm:"type:varchar(150);not null;default:'';comment:备注" `
}

func (SdLora) TableName() string {
	return "T_SdLora"
}

func (o *SdLora) GetById(id uint) error {
	return db.First(o, id).Error
}

func (o *SdLora) GetList(dest interface{}, id uint, title string, hash string, page int, pageSize int) (int64, error) {

	var total int64
	tx := db.Debug().Model(o)
	if id > 0 {
		tx.Where("id=?", id)
	}
	if title != "" {
		tx.Where("title like ?", title)
	}
	if hash != "" {
		tx.Where("hash = ?", hash)
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *SdLora) Save() error {
	return db.Save(o).Error
}

func (o *SdLora) SetCover(path string) error {
	return db.Model(o).Updates(SdLora{Cover: path}).Error
}

package model

import (
	"errors"
	"time"

	"gorm.io/gorm"
)

type ShopOut struct {
	gorm.Model
	UserId      uint      `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	OrigWhere   int       `json:"orig_where" gorm:"type:int;not null;default:0;comment:哪里来的数据"`
	OrigId      uint      `json:"orig_id" gorm:"type:bigint;not null;default:0;comment:来源记录ID"`
	MadeType    int       `json:"made_type" gorm:"type:int;not null;default:0;comment:制作类型"`
	ShopFaceId  uint      `json:"shop_face_id" gorm:"type:bigint;not null;default:0;comment:脸膜ID"`
	ShopSceneId uint      `json:"shop_scene_id" gorm:"type:bigint;not null;default:0;comment:场景ID"`
	ModelName   string    `json:"model_name" gorm:"type:varchar(50);not null;default:'';comment:模型名称"`
	TotalIndex  int       `json:"total_index"  gorm:"type:int;not null;default:0;comment:本Id总序号"`
	BatchIndex  int       `json:"batch_index"  gorm:"type:int;not null;default:0;comment:批次索引"`
	Parameters  string    `json:"parameters" gorm:"type:json;';comment:参数Json"`
	PushJson    string    `json:"push_json" gorm:"type:json;';comment:推送给后端的绘图JSON模板"`
	PushAt      time.Time `json:"push_at" gorm:"type:datetime;default:'1900-01-01';comment:最后一次推送时间"`
	Info        string    `json:"info" gorm:"type:json;';comment:sd返回绘图参数"`
	Width       int       `json:"width" gorm:"type:int;not null;default:0;comment:参考图宽度"`
	Height      int       `json:"height" gorm:"type:int;not null;default:0;comment:参考图高度"`
	Md5         string    `json:"md5" gorm:"type:varchar(50);not null;default:'';comment:图片md5"`
	Path        string    `json:"path" gorm:"type:varchar(100);not null;default:'';comment:图片路径"`
	Path1       string    `json:"path1" gorm:"type:varchar(100);not null;default:'';comment:高分图片路径1"`
	Path2       string    `json:"path2" gorm:"type:varchar(100);not null;default:'';comment:高分图片路径2"`
	MergePath   string    `json:"merge_path" gorm:"type:varchar(150);not null;default:'';comment:合并图路径"`
	ScaleAt     time.Time `json:"scale_at" gorm:"type:datetime;default:'1900-01-01';comment:高分开始时间,高分成功后置为null"`
	PriceCoin   int       `json:"price_coin" gorm:"type:int;not null;default:0;comment:所需Coin"`
	OrderNo     string    `json:"order_no" gorm:"type:varchar(50);comment:订单编号"`
	State       int       `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态 0初始 3已完成"`
}

func (ShopOut) TableName() string {
	return "T_ShopOut"
}

func (o *ShopOut) Get() error {
	err := db.First(o, o.ID).Scan(o).Error
	return err
}

func (o *ShopOut) GetByID(id uint) error {
	err := db.First(o, id).Error
	return err
}
func (o *ShopOut) GetByID_1(id uint, dest interface{}) error {
	err := db.First(o, id).Scan(dest).Error
	return err
}
func (o *ShopOut) GetByMd5(md5 string) error {
	err := db.First(o, "md5 = ?", md5).Error
	return err
}
func (o *ShopOut) GetByMd5_1(md5 string, dest interface{}) error {
	err := db.First(o, "md5 = ?", md5).Scan(dest).Error
	return err
}

func (o *ShopOut) GetShareListFall(origWhere int, share int, lastId uint, limit int) ([]ShopOut, error) {
	ary := make([]ShopOut, 0)
	tx := db.Debug().Model(o).Where("orig_where=?", origWhere)
	if share >= 0 {
		tx = tx.Where("share>=?", share)
	} else {
		return ary, nil
	}
	if lastId > 0 {
		tx = tx.Where("id<?", lastId)
	}
	tx = tx.Order("id desc").Limit(limit).Scan(&ary)
	return ary, tx.Error
}

func (o *ShopOut) GetList(dest interface{}, outId uint, md5 string, userId uint, origWhere int, origId uint, showFaceId uint, modelName string, page int, pageSize int) (int64, error) {

	var total int64
	tx := db.Debug().Model(o).Where("orig_where=? ", origWhere)
	if outId > 0 {
		tx.Where("id=?", outId)
	}
	if md5 != "" {
		tx.Where("md5=?", md5)
	}
	if userId > 0 {
		tx.Where("user_id=?", userId)
	}
	if origId > 0 {
		tx.Where("orig_id=?", origId)
	}
	if showFaceId > 0 {
		tx.Where("shop_face_id=?", showFaceId)
	}
	if modelName != "" {
		tx.Where("model_name=?", modelName)
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *ShopOut) StatisticOutImg(start time.Time, end time.Time) (int64, error) {
	var total int64
	tx := db.Debug().Model(o).Where("created_at > ? AND created_at < ?", start, end).Where("path <> ''").Count(&total)
	err := tx.Error
	return total, err
}

func (o *ShopOut) GetShareList(origWhere int, roomType int, roomStyle int, share int, page int, pageSize int) ([]ShopOut, int64, error) {
	ary := make([]ShopOut, 0)
	var total int64
	tx := db.Debug().Model(o).Where("orig_where=? ", origWhere)
	if roomType > 0 {
		tx.Where("room_type=?", roomType)
	}
	if roomStyle > 0 {
		tx.Where("room_style=?", roomStyle)
	}
	if share > -1 {
		tx.Where("share>=?", share)
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return nil, 0, err
		}
	}
	tx.Order("order_index desc, id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(&ary)
	return ary, total, tx.Error
}

func (o *ShopOut) BatchCreate(value []ShopOut) error {
	return db.Create(&value).Error
}

func (o *ShopOut) Save() error {
	return db.Save(o).Error
}
func (o *ShopOut) Delete() error {
	return db.Delete(o).Error
}

func (o *ShopOut) SetShare(share int) error {
	return db.Model(o).Updates(map[string]interface{}{"share": share}).Error
}

func (o *ShopOut) SetShareInfo(showTitle string, orderIndex float64) error {
	return db.Model(o).Updates(map[string]interface{}{"show_title": showTitle, "order_index": orderIndex}).Error
}

func (o *ShopOut) SetPath(path string) error {
	return db.Model(o).Updates(ShopOut{Path: path}).Error
}

func (o *ShopOut) SetMergePath(path string) error {
	return db.Model(o).Updates(ShopOut{MergePath: path}).Error
}

func (o *ShopOut) SetPathAndWidth(path string, width int, height int) error {
	return db.Model(o).Updates(ShopOut{Path: path, Width: width, Height: height}).Error
}

func (o *ShopOut) SetPathAndInfo(path string, outJsonValue string, width int, height int) error {
	return db.Model(o).Updates(ShopOut{Path: path, Info: outJsonValue, Width: width, Height: height}).Error
}

func (o *ShopOut) SetInfo(outJsonValue string) error {
	return db.Model(o).Updates(ShopOut{Info: outJsonValue}).Error
}

/*
func (o *DiffImg) SetUpscalePath(level int, path string) error {
	if level == 1 {
		return db.Model(o).Updates(DiffImg{Path1: path, o.UpdatedAt: nil}).Error
	}

}*/

func (o *ShopOut) SetUpscalePath(id uint, level int, path string) error {
	if level == 1 {
		return db.Model(o).Where("id = ?", id).Updates(map[string]interface{}{"path1": path, "scale_at": nil}).Error
	} else if level == 2 {
		return db.Model(o).Where("id = ?", id).Updates(map[string]interface{}{"path2": path, "scale_at": nil}).Error
	} else {
		return errors.New("未找到level对应字段")
	}
}

func (o *ShopOut) SetUpscaleAt() error {
	return db.Model(o).Updates(ShopOut{ScaleAt: time.Now()}).Error
}

func (o *ShopOut) SetPushAt() error {
	return db.Model(o).Updates(ShopOut{PushAt: time.Now()}).Error
}

func (o *ShopOut) Del(tx *gorm.DB, id uint, userId uint) error {
	tx.Debug().Where("id=? and user_id=? ", userId, id, userId).Delete(o)
	return tx.Error
}

func (o *ShopOut) DelByOrig(tx *gorm.DB, userId uint, origWhere int, origId uint) error {
	tx.Debug().Where("user_id=? and orig_where=? and orig_id=?", userId, origWhere, origId).Delete(o)
	return tx.Error
}

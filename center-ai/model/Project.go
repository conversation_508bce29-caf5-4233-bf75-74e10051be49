package model

import (
	"center-ai/utils/logger"
	"gorm.io/gorm"
)

type Project struct {
	gorm.Model
	Title        string `json:"title" gorm:"type:varchar(20);comment:项目标题""`
	Bundle       string `json:"bundle" gorm:"type:varchar(50);comment:项目唯一ID"`
	MessageToken string `json:"message_token" gorm:"type:varchar(50);comment:消息token"`
	Remark       string `json:"remark" gorm:"type:varchar(150);comment:备注" `
}

func (Project) TableName() string {
	return "T_Project"
}

func (o *Project) GetBundleById(id uint) string {
	if err := db.First(o, id).Error; err != nil {
		logger.Error(err)
	}
	return o.Bundle
}

func (o *Project) GetById(id uint) error {
	return db.First(o, id).Error
}
func (o *Project) GetByBundle(bundle string) error {
	return db.First(o).Where("bundle=?", bundle).Error
}

func (o *Project) GetByBundleAndTitle(bundle string, title string) error {
	return db.First(o).Where("bundle=? and title= ?", bundle, title).Error
}

func (o *Project) GetList(dest interface{}) error {
	tx := db.Debug().Model(o)
	tx.Order("id desc")
	tx.Scan(dest)
	return tx.Error
}

func (o *Project) Save() error {
	return db.Save(o).Error
}

package service

import (
	"center-ai/utils/logger"
	"github.com/go-co-op/gocron"
	"time"
)

var RunPause bool //服务是否暂停
var RunTask [1]bool

func RunTimer() {

	//go func() {
	//	ticker := time.NewTicker(30 * time.Second)
	//	defer ticker.Stop()
	//	for range ticker.C {
	//		//logger.Info("开始执行每隔30秒业务逻辑")
	//		LoarService.LoadData(false)
	//		TemplatePrompt.LoadData(false)
	//		logger.Info("每隔30秒业务逻辑执行结束")
	//	}
	//}()

	go func() {
		ticker := time.NewTicker(1 * time.Second)
		defer ticker.Stop()
		for range ticker.C {
			logger.Info("开始执行MessageService业务逻辑")
			MessageService.Run()
			logger.Info("执行MessageService业务逻辑结束")
		}
	}()

	go func() {
		ticker := time.NewTicker(1 * time.Second)
		defer ticker.Stop()
		for range ticker.C {
			logger.Info("开始执行ShopShowService业务逻辑")
			ShopShowService.Run()
			logger.Info("ShopShowService业务逻辑出错,延迟10秒")
		}
	}()

	//go func() {
	//	ticker := time.NewTicker(1 * time.Second)
	//	defer ticker.Stop()
	//	for range ticker.C {
	//		logger.Info("开始执行MessageService业务逻辑")
	//		MessageService.Run()
	//		logger.Info("执行MessageService业务逻辑结束")
	//	}
	//}()

	//go func() {
	//	ticker := time.NewTicker(30 * time.Minute)
	//	defer ticker.Stop()
	//	for range ticker.C {
	//		logger.Info("开始执行ShopShowService业务逻辑")
	//		ShopShowService.Run()
	//		logger.Info("ShopShowService业务逻辑执行结束")
	//	}
	//}()

	go func() {
		// 创建一个新的调度器
		s := gocron.NewScheduler(time.Local)
		// 每天在上午2点01分执行任务
		s.Every(1).Day().At("02:01").Do(func() {
			logger.Info("开始执行定时任务...")
			if err := SegmentService.HClear(); err != nil {
				logger.Error("HClear fail:", err)
			}
			logger.Info("定时任务执行完成。")
		})
		// 开始调度器
		logger.Info("启动每日执行定时器")
		s.StartBlocking()
	}()

	//go func() {
	//	timezone, err := time.LoadLocation("Asia/Shanghai")
	//	if err != nil {
	//		logger.Error("清理任务设置失败", err)
	//	}
	//	s := gocron.NewScheduler(timezone)
	//	s.Every(1).Day().At("02:01").Do(func() {
	//		logger.Info("开始执行清理程序")
	//
	//		if err := SegmentService.HClear(); err != nil {
	//			logger.Error("val:", err)
	//		}
	//		logger.Info("清理程序执行完成")
	//	})
	//	logger.Info("启动每日执行定时器")
	//	s.StartBlocking()
	//}()

}

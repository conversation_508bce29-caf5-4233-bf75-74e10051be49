package service

import (
	"bytes"
	"center-ai/enums"
	"center-ai/model"
	"center-ai/utils/logger"
	"center-ai/utils/myredis"
	"center-ai/utils/tools"
	"encoding/json"
	"time"
)

type segment_ struct {
}

type SegmentOutputData struct {
	CustomMd5   string         `json:"custom_data"`
	Masks       string         `json:"masks"`
	CreateAt    model.JsonTime `json:"create_at"`
	ExecuteAt   model.JsonTime `json:"execute_at"`
	ExecuteTime uint           `json:"execute_time"`
}

type segmentInputData struct {
	CustomApp            string         `json:"custom_app"`
	CustomData           string         `json:"custom_data"`
	CustomMd5            string         `json:"custom_md5"`
	InputImgPath         string         `json:"input_img_path"`
	Width                int            `json:"width"`
	Height               int            `json:"height"`
	Masks                string         `json:"masks"`
	CreateAt             model.JsonTime `json:"create_at"`
	ExecuteAt            model.JsonTime `json:"execute_at"`
	ExecuteTime          uint           `json:"execute_time"`
	StabilityScoreThresh float64        `json:"stability_score_thresh"`
	PredIouThresh        float64        `json:"pred_iou_thresh"`
	StabilityScoreOffset int            `json:"stability_score_offset"`
	PointsPerBatch       int            `json:"points_per_batch"`
}

func (d *segment_) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("Segment奔溃:", e)
		}
	}()
	logger.Info("Segment.Run 开始循环获取图片")
	//for {
	//	//value, err := d.PopJson()
	//	//
	//	//if err != nil {
	//	//	logger.Error(value, err)
	//	//	continue
	//	//}
	//	//if value == "" {
	//	//	continue
	//	//}
	//	//logger.Info("接收到绘图json:", value)
	//	//if err := d.UpdateToImg(value); err != nil {
	//	//	logger.Error(err)
	//	//}
	//}
}

func (d *segment_) HGetJson(inputImgMd5 string) (segmentInputData, error) {
	value, err := myredis.HGet(enums.AigcRedisKeyEnum.SegmentPop, inputImgMd5)

	outputData := segmentInputData{}

	if err := json.Unmarshal([]byte(value), &outputData); err != nil {
		logger.Info(inputImgMd5, "     ", value)
		logger.Error(err)
		return outputData, err
	}

	return outputData, err
}

func (d *segment_) HGetJsonStr(inputImgMd5 string) (string, error) {
	value, err := myredis.HGet(enums.AigcRedisKeyEnum.SegmentPop, inputImgMd5)

	return value, err
}

func (d *segment_) HRemoveJson(inputImgMd5 string) (int64, error) {
	value, err := myredis.HDel(enums.AigcRedisKeyEnum.SegmentPop, inputImgMd5)
	return value, err
}

func (d *segment_) HClear() error {
	logger.Info("开始删除key:", enums.AigcRedisKeyEnum.SegmentPop)
	err := myredis.Del(enums.AigcRedisKeyEnum.SegmentPop)
	ll, err := myredis.LLen(enums.AigcRedisKeyEnum.SegmentPop)
	logger.Info(enums.AigcRedisKeyEnum.SegmentPop, "长度:", ll, err)
	return err
}

func (o *segment_) PushJson(inputImgMd5 string, inputImgPath string, stabilityScoreThresh float64, predIouThresh float64, stabilityScoreOffset int, pointsPerBatch int) (int64, error) {

	//segmentParm := ""
	//segmentParmMap := tools.GetMapFromJson(segmentParm)
	//segmentParmMap["custom_app"] = "segment"
	//segmentParmMap["custom_md5"] = inputImgMd5
	//segmentParmMap["create_at"] = time.Now().Format("2006-01-02 15:04:05")

	if pointsPerBatch == 0 {
		pointsPerBatch = 128
	}

	var jsonTime model.JsonTime
	data := segmentInputData{
		CustomApp:            "segment",
		CustomMd5:            inputImgMd5,
		InputImgPath:         inputImgPath,
		CreateAt:             jsonTime.Now(),
		StabilityScoreThresh: stabilityScoreThresh,
		PredIouThresh:        predIouThresh,
		StabilityScoreOffset: stabilityScoreOffset,
		PointsPerBatch:       pointsPerBatch,
	}

	bf := bytes.NewBuffer([]byte{})
	jsonEncoder := json.NewEncoder(bf)
	jsonEncoder.SetEscapeHTML(false)
	jsonEncoder.Encode(data)

	logger.Info("SegmentPushJson:", enums.AigcRedisKeyEnum.SegmentPush, "     ", bf.String())
	size, err := myredis.LPush(enums.AigcRedisKeyEnum.SegmentPush, bf.String())
	if err != nil {
		logger.Error(err)
		return size, err
	}
	return size, err
}

func (o *segment_) PushJsonSegment(inputImgMd5 string, inputImgPath string, segmentParm string) (int64, error) {

	segmentParmMap := tools.GetMapFromJson(segmentParm)
	segmentParmMap["custom_app"] = "segment"
	segmentParmMap["custom_data"] = ""
	segmentParmMap["custom_md5"] = inputImgMd5
	segmentParmMap["input_img_path"] = inputImgPath
	segmentParmMap["create_at"] = time.Now().Format("2006-01-02 15:04:05")

	//if pointsPerBatch == 0 {
	//	pointsPerBatch = 128
	//}

	//var jsonTime model.JsonTime
	//data := segmentInputData{
	//	CustomApp:            "segment",
	//	CustomMd5:            inputImgMd5,
	//	InputImgPath:         inputImgPath,
	//	CreateAt:             jsonTime.Now(),
	//	StabilityScoreThresh: stabilityScoreThresh,
	//	PredIouThresh:        predIouThresh,
	//	StabilityScoreOffset: stabilityScoreOffset,
	//	PointsPerBatch:       pointsPerBatch,
	//}
	//
	//bf := bytes.NewBuffer([]byte{})
	//jsonEncoder := json.NewEncoder(bf)
	//jsonEncoder.SetEscapeHTML(false)
	//jsonEncoder.Encode(data)
	json := tools.GetJsonFromMap(segmentParmMap)

	logger.Info("SegmentPushJson:", enums.AigcRedisKeyEnum.SegmentPush, "     ", json)
	size, err := myredis.LPush(enums.AigcRedisKeyEnum.SegmentPush, json)
	if err != nil {
		logger.Error(err)
		return size, err
	}
	return size, err
}

//func (o *segment_) HSetJson(inputImgMd5 string, inputImgPath string) (bool, error) {
//	var jsonTime model.JsonTime
//	jsonTime.Now()
//	data := segmentInputData{
//		CustomApp:    "segment",
//		InputImgMd5:  inputImgMd5,
//		InputImgPath: inputImgPath,
//		CreateAt:     jsonTime,
//	}
//	bf := bytes.NewBuffer([]byte{})
//	jsonEncoder := json.NewEncoder(bf)
//	jsonEncoder.SetEscapeHTML(false)
//	jsonEncoder.Encode(data)
//
//	logger.Info("Json:", inputImgPath)
//	bol, err := myredis.HSet(enums.AigcRedisKeyEnum.SegmentMap, inputImgMd5, bf.String())
//	if err != nil {
//		logger.Error(err)
//		return bol, err
//	}
//	return bol, err
//}

var SegmentService segment_

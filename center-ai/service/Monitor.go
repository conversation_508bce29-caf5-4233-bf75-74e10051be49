package service

import (
	"center-ai/model"
	"center-ai/utils/logger"
	"center-ai/utils/tools"
	"runtime"
	"strings"
)

type monitor_ struct {
}

var MonitorService monitor_

func (d *monitor_) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	logger.Info("MonitorService.Run 开始")

	queryParm := make(map[string]interface{})
	queryParm["status"] = 1
	page := 1
	pageSize := 1000
	var monitor model.Monitor
	var arr = make([]model.Monitor, 0)
	if total, err := monitor.List(&arr, queryParm, page, pageSize); err != nil {
		logger.Error(err)
		return
	} else {
		logger.Info("需要监测", total, "条数据")
		for _, item := range arr {
			if strings.HasPrefix(item.CheckUrl, "http") {
				if resp, err := tools.Get(item.CheckUrl, nil, nil); err != nil {
					logger.Error("resp:", resp)
					if err := item.SetCheckResult("fail"); err != nil {
						logger.Error(err)
					}
				} else {
					if err := item.SetCheckResult("success"); err != nil {
						logger.Error(err)
					}
				}
			} else {
				logger.Error("CheckUrl不可访问:", item.CheckUrl)
			}
		}
	}
}

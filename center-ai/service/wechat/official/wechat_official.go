package wechatofficial

import (
	"center-ai/model"
	wechat_config "center-ai/service/wechat"
	"center-ai/utils/config"
	"center-ai/utils/logger"
	"center-ai/utils/tools"
	"context"
	"github.com/silenceper/wechat/v2"
	"github.com/silenceper/wechat/v2/cache"
	"github.com/silenceper/wechat/v2/officialaccount"
	"strings"
	"time"
)

type wechatOfficialAccount_ struct {
	Wc              *wechat.Wechat
	OfficialAccount *officialaccount.OfficialAccount
}

func NewWechatOfficialAccount(wc *wechat.Wechat) *wechatOfficialAccount_ {
	if strings.Contains(tools.GetLocalIP(), "200.3") {
		wechat_config.OfficialConfig.AppID += "1"
	}
	logger.Info("NewWechatOfficialAccount ", "wechat_config.OfficialConfig.AppID:", wechat_config.OfficialConfig, "  tools.GetLocalIP():", tools.GetLocalIP())
	officialAccount := wc.GetOfficialAccount(wechat_config.OfficialConfig)
	return &wechatOfficialAccount_{
		Wc:              wc,
		OfficialAccount: officialAccount,
	}
}

var WechatOfficialAccount = *NewWechatOfficialAccount(InitWechat())

// InitWechat 获取wechat实例
// 在这里已经设置了全局cache，则在具体获取公众号/小程序等操作实例之后无需再设置，设置即覆盖
func InitWechat() *wechat.Wechat {
	wc := wechat.NewWechat()
	ctx := context.Background()
	redisOpts := &cache.RedisOpts{
		Host:     config.RedisAddr,
		Password: config.RedisPassWord,
		//Database: 0,
		//MaxActive:   cfg.Redis.MaxActive,
		//MaxIdle:     cfg.Redis.MaxIdle,
		//IdleTimeout: cfg.Redis.IdleTimeout,
	}

	logger.Info("redisOpts:", tools.GetJsonFromStruct(*redisOpts))
	redis := cache.NewRedis(ctx, redisOpts)

	key := "redistest"
	val1 := time.Now().Format(model.TimeFormat)
	if err := redis.Set(key, val1, time.Second); err != nil {
		logger.Error("redistest Error", err)
	}
	logger.Info("redistest:", redis.Get(key))
	wc.SetCache(redis)
	return wc
}

package myimg

import (
	"bytes"
	"center-ai/utils/logger"
	"center-ai/utils/tools"
	"encoding/base64"
	"errors"
	"fmt"
	"github.com/disintegration/imaging"
	"github.com/nfnt/resize"
	"github.com/rwcarlsen/goexif/exif"
	"golang.org/x/image/webp"
	"image"
	"image/color"
	"image/draw"
	"image/jpeg"
	"image/png"
	"io/ioutil"
	"log"
	"os"
	"path"
	"strings"
)

type myimg_ struct {
}

const (
	OrientationUnspecified = 0
	OrientationNormal      = 1
	OrientationFlipH       = 2
	OrientationRotate180   = 3
	OrientationFlipV       = 4
	OrientationTranspose   = 5
	OrientationRotate270   = 6
	OrientationTransverse  = 7
	OrientationRotate90    = 8
)

func resizeImg() {
	// open "test.jpg"
	file, err := os.Open("test.jpg")
	if err != nil {
		log.Println(err)
	}
	// decode jpeg into image.Image
	img, err := jpeg.Decode(file)
	if err != nil {
		log.Println(err)
	}
	file.Close()
	// resize to width 1000 using Lanczos resampling
	//and preserve aspect ratio
	m := resize.Resize(1000, 0, img, resize.Lanczos3)
	out, err := os.Create("test_resized.jpg")
	if err != nil {
		log.Println(err)
	}
	defer out.Close()
	// write new image to file
	jpeg.Encode(out, m, nil)
}

func img2base64() (string, error) {
	f, err := os.Open("ubuntu.png")
	if err != nil {
		panic(err)
	}
	all, _ := ioutil.ReadAll(f)
	str := base64.StdEncoding.EncodeToString(all)
	fmt.Printf("%s", str)
	return str, nil
}

func readImage() {
	f, err := os.Open("ubuntu.png")
	if err != nil {
		panic(err)
	}
	// decode图片
	m, err := png.Decode(f)
	if err != nil {
		panic(err)
	}
	fmt.Printf("%v", m.Bounds()) // 图片长宽

	fmt.Printf("%v", m.ColorModel()) // 图片颜色模型

	fmt.Printf("%v", m.At(100, 100)) // 该像素点的颜色

}

func Base64ToFile(base64Str string, filepath string) error { //base64Str 前面不需要加data:image/png; data:image/jpeg;base64,

	base64Str = ClearBase64Str(base64Str)
	ddd, err := base64.StdEncoding.DecodeString(base64Str) //成图片文件并把文件写入到buffer
	err = ioutil.WriteFile(filepath, ddd, 0666)            //"./output.jpg"
	return err
}

func Base64ToBuffer(base64Str string) (*bytes.Buffer, error) {
	base64Str = ClearBase64Str(base64Str)
	ddd, err := base64.StdEncoding.DecodeString(base64Str) //成图片文件并把文件写入到buffer
	bbb := bytes.NewBuffer(ddd)                            // 必须加一个buffer 不然没有read方法就会报错
	return bbb, err
}

func BufferToImg(bbb *bytes.Buffer) (image.Image, string, error) {
	m, str, err := image.Decode(bbb) // 图片文件解码
	return m, str, err
}

func Base64ToImg(base64Str string) (image.Image, error) {
	bbb, err := Base64ToBuffer(base64Str)
	if err != nil {
		return nil, err
	}
	img, _, err := BufferToImg(bbb)
	return img, err
}

func ClearBase64Str(base64Str string) string {
	if strings.HasPrefix(base64Str, "data:") || strings.HasPrefix(base64Str, "base64,") {
		ary := strings.Split(base64Str, "base64,")
		if len(ary) != 2 {
			err := errors.New("base64字符串格式不正确")
			logger.Error(err)
		}
		base64Str = ary[1]
	}
	return base64Str
}

func ImgToFile(subImg image.Image, filepath string) error {
	emptyBuff := bytes.NewBuffer(nil) //开辟一个新的空buff
	err := jpeg.Encode(emptyBuff, subImg, nil)
	err = ioutil.WriteFile(filepath, emptyBuff.Bytes(), 0666)
	return err
}

func ImgToJpegFile(subImg image.Image, filepath string, quality int) error {
	emptyBuff := bytes.NewBuffer(nil) //开辟一个新的空buff
	err := jpeg.Encode(emptyBuff, subImg, &jpeg.Options{Quality: quality})
	err = ioutil.WriteFile(filepath, emptyBuff.Bytes(), 0666)
	return err
}

func ImgToPngFile(subImg image.Image, filepath string) error {
	emptyBuff := bytes.NewBuffer(nil) //开辟一个新的空buff
	err := png.Encode(emptyBuff, subImg)
	err = ioutil.WriteFile(filepath, emptyBuff.Bytes(), 0666)
	return err
}

func ReadOrientation(filename string) int {
	file, err := os.Open(filename)
	if err != nil {
		logger.Error("failed to open file, err: ", err)
		return 0
	}
	defer file.Close()

	//img, _, err := image.Decode(file)
	//fmt.Println(img.Bounds().Dx(), "    ", img.Bounds().Dy())

	x, err := exif.Decode(file)
	if err != nil {
		logger.Error("failed to decode file, err: ", err)
		return 0
	}
	if x == nil {
		return 0
	}

	orientation, err := x.Get(exif.Orientation)
	if err != nil {
		logger.Error("failed to get orientation, err: ", err)
		return 0
	}
	orientVal, err := orientation.Int(0)
	if err != nil {
		logger.Error("failed to convert type of orientation, err: ", err)
		return 0
	}
	return orientVal
}

// 旋转90度
func Rotate90(m image.Image) image.Image {
	rotate90 := image.NewRGBA(image.Rect(0, 0, m.Bounds().Dy(), m.Bounds().Dx()))
	// 矩阵旋转
	for x := m.Bounds().Min.Y; x < m.Bounds().Max.Y; x++ {
		for y := m.Bounds().Max.X - 1; y >= m.Bounds().Min.X; y-- {
			//  设置像素点
			rotate90.Set(m.Bounds().Max.Y-x, y, m.At(y, x))
		}
	}
	return rotate90
}

// 旋转180度

func Rotate180(m image.Image) image.Image {
	rotate180 := image.NewRGBA(image.Rect(0, 0, m.Bounds().Dx(), m.Bounds().Dy()))
	// 矩阵旋转
	for x := m.Bounds().Min.X; x < m.Bounds().Max.X; x++ {
		for y := m.Bounds().Min.Y; y < m.Bounds().Max.Y; y++ {
			//  设置像素点
			rotate180.Set(m.Bounds().Max.X-x, m.Bounds().Max.Y-y, m.At(x, y))
		}
	}
	return rotate180
}

// 旋转270度
func Rotate270(m image.Image) image.Image {
	rotate270 := image.NewRGBA(image.Rect(0, 0, m.Bounds().Dy(), m.Bounds().Dx()))
	// 矩阵旋转
	for x := m.Bounds().Min.Y; x < m.Bounds().Max.Y; x++ {
		for y := m.Bounds().Max.X - 1; y >= m.Bounds().Min.X; y-- {
			// 设置像素点
			rotate270.Set(x, m.Bounds().Max.X-y, m.At(y, x))
		}
	}
	return rotate270
}

func ReadTextChunksFromPng(inputFilePath string) (tEXtChunks []TEXtChunk, err error) {
	//https://github.com/chrisbward/go-png-chunks
	//	//gopngchunks "github.com/chrisbward/go-png-chunks"
	f, err := os.Open(inputFilePath)
	if err != nil {
		return tEXtChunks, fmt.Errorf("os.Open(): %s", err)
	}
	defer f.Close()
	data, err := ioutil.ReadAll(f)
	if err != nil {
		return tEXtChunks, err
	}
	tEXtChunks, err = GetAlltEXtChunks(data)
	if err != nil {
		return tEXtChunks, err
	}
	return tEXtChunks, nil
}

func WriteTextChunkToPng1(pngImg image.Image, outputFilePath string, tEXtChunkToWrite TEXtChunk) error {

	var buffer bytes.Buffer
	if err := png.Encode(&buffer, pngImg); err != nil {
		return err
	}

	//w, err := gopngchunks.WritetEXtToPngBytes(buffer.Bytes(), tEXtChunkToWrite)
	w, err := WritetEXtToPngBytes(buffer.Bytes(), tEXtChunkToWrite)
	if err != nil {
		return err
	}

	ioutil.WriteFile(outputFilePath, w.Bytes(), 0666)

	return nil
}

func WriteTextChunkToPng(inputFilePath string, outputFilePath string, tEXtChunkToWrite TEXtChunk) error {
	f, err := os.Open(inputFilePath)
	if err != nil {
		return err
	}
	defer f.Close()
	data, err := ioutil.ReadAll(f)
	if err != nil {
		return err
	}
	//tEXtChunkToWrite := gopngchunks.TEXtChunk{
	//	Key:   "helloworld",
	//	Value: "helloWorld",
	//}
	w, err := WritetEXtToPngBytes(data, tEXtChunkToWrite)
	if err != nil {
		return err
	}

	//out, err := os.Create(outputFilePath)
	//if err != nil {
	//	return err
	//}
	//defer out.Close()
	//out.Write(w.Bytes())

	ioutil.WriteFile(outputFilePath, w.Bytes(), 0666)
	return nil
}

func ImgToBase64(subImg image.Image) (string, error) {

	/*
		emptyBuff := bytes.NewBuffer(nil)                  //开辟一个新的空buff
		err := jpeg.Encode(emptyBuff, subImg, nil)         //img写入到buff
		dist := make([]byte, 50000)                        //开辟存储空间
		base64.StdEncoding.Encode(dist, emptyBuff.Bytes()) //buff转成base64
		//fmt.Println(string(dist))                           //输出图片base64(type = []byte)
		//_ = ioutil.WriteFile("./base64pic.txt", dist, 0666) //buffer输出到jpg文件中（不做处理，直接写到文件）
		return string(dist), err*/

	var b bytes.Buffer
	err := png.Encode(&b, subImg)
	str := base64.StdEncoding.EncodeToString(b.Bytes())
	return str, err
}

func FileToBase64(filename string) (string, error) {
	b, err := ioutil.ReadFile(filename) //我还是喜欢用这个快速读文件
	if err != nil {
		return "", err
	}
	str := base64.StdEncoding.EncodeToString(b)
	return str, err
}

func FileToImg(filename string) (image.Image, string, error) {
	b, err := ioutil.ReadFile(filename) //我还是喜欢用这个快速读文件
	if err != nil {
		return nil, "", err
	}

	baseName := path.Base(filename)            // 输出 name.html
	ext := strings.ToLower(path.Ext(baseName)) // 输出 .html
	if ext == ".webp" {
		// 尝试以 webp 进行解码
		if m, err := webp.Decode(bytes.NewReader(b)); err != nil {
			return nil, ext, err
		} else {
			return m, ext, nil
		}
		//img, err := webp.Decode(bytes.NewReader(b))
		//return img, ext, err
		return nil, ext, fmt.Errorf("暂时不支持webp格式")
	} else if ext == ".avif" && false {
		//
		//err := libheif.Init(libheif.Config{LibraryConfig: library.Config{
		//	Command: library.Command{
		//		BinPath: "~/go/bin",
		//		Args:    []string{"run", "library/worker_example/main.go"},
		//	},
		//}})
		//if err != nil {
		//	return nil, ext, err
		//}
		//
		//b, err := os.ReadFile(filename)
		//renderedFile, err := library.RenderFile(&b, library.RenderOptions{
		//	OutputFormat: library.RenderFileOutputFormatPNG,
		//})
		//img, dec, err := image.Decode(bytes.NewReader(*renderedFile.Output))
		//if err != nil {
		//	logger.Error(err, dec)
		//}
		//
		////https://github.com/klippa-app/go-libheif/
		//if err := libheif.Init(libheif.Config{LibraryConfig: library.Config{
		//	Command: library.Command{
		//		BinPath: "/Users/<USER>/go/pkg/mod/github.com/klippa-app/go-libheif@v1.2.0",
		//		Args:    []string{"run", "library/worker_example/main.go"},
		//	},
		//}}); err != nil {
		//	return nil, ext, err
		//}
		//
		////img, err := libheif.DecodeImage(bytes.NewReader(b))
		////if err != nil {
		////	return nil, ext, err
		////}
		return nil, ext, err
	}
	return BufferToImg(bytes.NewBuffer(b))
}

func CutImage(bbb *bytes.Buffer) (image.Image, error) { //裁剪图片
	m, _, err := image.Decode(bbb) // 图片文件解码
	rgbImg := m.(*image.YCbCr)
	subImg := rgbImg.SubImage(image.Rect(0, 0, 200, 200)).(*image.YCbCr) //图片裁剪x0 y0 x1 y1
	return subImg, err
}

func ResizeImg(w int, h int, subImg image.Image, full bool) image.Image {
	x := subImg.Bounds().Size().X
	y := subImg.Bounds().Size().Y

	floatX := float64(x)
	floatY := float64(y)
	floatW := float64(w)
	floatH := float64(h)

	nW := w
	nH := int(floatY / (floatX / floatW))
	if full {
		if nH < h {
			nH = h
			nW = int(floatX / (floatY / floatH))
		}
	}
	//logger.Info("resizeimg", x, y, w, h)
	return resize.Resize(uint(nW), uint(nH), subImg, resize.NearestNeighbor)
}

func CroppedImg(w int, h int, subImg image.Image) image.Image {
	//tmpImg := resize.Thumbnail(uint(w), uint(h), subImg, resize.Lanczos3)
	tmpImg := ResizeImg(w, h, subImg, true)
	centerCropImg := imaging.CropAnchor(tmpImg, w, h, imaging.Center) //剪切图片,从中心截取200 大小的图
	return centerCropImg
}

func WindowImg(winWidth int, winHeight int, subImg image.Image) image.Image {
	// 计算等比例缩放后的尺寸
	bounds := subImg.Bounds()
	//fmt.Println(bounds.Max.X, "   ", bounds.Max.Y)
	//fmt.Println(bounds.Size().X, "   ", bounds.Size().Y)
	//fmt.Println(bounds.Dx(), "    ", bounds.Dy())
	hRatio := float64(bounds.Dy()) / 2000 //bounds.Dy() 宽度
	wRatio := float64(bounds.Dx()) / 1496 //bounds.Dx()高度
	var newWidth, newHeight uint
	if hRatio > wRatio {
		newWidth = uint(float64(bounds.Dx()) / hRatio)
		newHeight = 2000
	} else {
		newWidth = 1496
		newHeight = uint(float64(bounds.Dy()) / wRatio)
	}

	// 缩放图片
	//newImg := resize.Resize(newWidth, newHeight, subImg, resize.Lanczos3)

	//// 等比例缩放图片
	//newImg := resize.Thumbnail(uint(winWidth), uint(winHeight), subImg, resize.Lanczos3)
	newImg := resize.Thumbnail(uint(newWidth), uint(newHeight), subImg, resize.Lanczos3)

	// 创建一个白色背景的图片
	bg := image.NewRGBA(image.Rect(0, 0, winWidth, winHeight))
	white := color.RGBA{255, 255, 255, 255}
	for i := 0; i < bg.Bounds().Dx(); i++ {
		for j := 0; j < bg.Bounds().Dy(); j++ {
			bg.Set(i, j, white)
		}
	}

	// 将缩放的图片复制到背景图片上
	offset := image.Pt((winWidth-newImg.Bounds().Dx())/2, (winHeight-newImg.Bounds().Dy())/2)
	draw.Draw(bg, newImg.Bounds().Add(offset), newImg, image.Point{}, draw.Over)
	return bg
}

func WindowImg1(winWidth int, winHeight int, subImg image.Image) image.Image {
	// 计算等比例缩放后的尺寸
	bounds := subImg.Bounds()
	//fmt.Println(bounds.Max.X, "   ", bounds.Max.Y)
	//fmt.Println(bounds.Size().X, "   ", bounds.Size().Y)
	//fmt.Println(bounds.Dx(), "    ", bounds.Dy())
	hRatio := float64(bounds.Dy()) / 2000 //bounds.Dy() 宽度
	wRatio := float64(bounds.Dx()) / 1496 //bounds.Dx()高度
	var newWidth, newHeight uint
	if hRatio > wRatio {
		newWidth = uint(float64(bounds.Dx()) / hRatio)
		newHeight = 2000
	} else {
		newWidth = 1496
		newHeight = uint(float64(bounds.Dy()) / wRatio)
	}

	// 缩放图片
	//newImg := resize.Resize(newWidth, newHeight, subImg, resize.Lanczos3)

	//// 等比例缩放图片
	//newImg := resize.Thumbnail(uint(winWidth), uint(winHeight), subImg, resize.Lanczos3)
	newImg := resize.Thumbnail(uint(newWidth), uint(newHeight), subImg, resize.Lanczos3)

	// 创建一个白色背景的图片
	bg := image.NewRGBA(image.Rect(0, 0, winWidth, winHeight))
	white := color.RGBA{0, 0, 0, 0}
	for i := 0; i < bg.Bounds().Dx(); i++ {
		for j := 0; j < bg.Bounds().Dy(); j++ {
			bg.Set(i, j, white)
		}
	}

	// 将缩放的图片复制到背景图片上
	offset := image.Pt((winWidth-newImg.Bounds().Dx())/2, (winHeight-newImg.Bounds().Dy())/2)
	draw.Draw(bg, newImg.Bounds().Add(offset), newImg, image.Point{}, draw.Over)
	return bg
}

func TurnMaskTransparentToWhite(img image.Image) image.Image {
	// 创建一个新的图片，用于存储转换后的结果
	newImg := image.NewRGBA(img.Bounds())

	// 遍历图片的每个像素
	for x := 0; x < img.Bounds().Dx(); x++ {
		for y := 0; y < img.Bounds().Dy(); y++ {
			// 获取原始像素的颜色
			//r, g, b, a := img.At(x, y).RGBA()
			_, _, _, a := img.At(x, y).RGBA()

			// 如果像素是透明的，设置新的颜色为白色
			// 否则，设置新的颜色为黑色
			if a == 0 {
				newImg.Set(x, y, color.RGBA{255, 255, 255, 255})
			} else {
				newImg.Set(x, y, color.RGBA{0, 0, 0, 255})
			}
		}
	}
	return newImg
}

func TurnTransparentToBlack(img image.Image) image.Image {
	// 创建一个新的图片，用于存储转换后的结果
	newImg := image.NewRGBA(img.Bounds())

	// 遍历图片的每个像素
	for x := 0; x < img.Bounds().Dx(); x++ {
		for y := 0; y < img.Bounds().Dy(); y++ {
			// 获取原始像素的颜色
			//r, g, b, a := img.At(x, y).RGBA()
			_, _, _, a := img.At(x, y).RGBA()

			// 如果像素是透明的，设置新的颜色为白色
			// 否则，设置新的颜色为黑色
			if a == 0 {
				newImg.Set(x, y, color.RGBA{0, 0, 0, 255})
			} else {
				newImg.Set(x, y, color.RGBA{255, 255, 255, 255})
			}
		}
	}
	return newImg
}

func TurnTransparentToBlackForHead(img image.Image) image.Image { //只换头特殊处理
	// 创建一个新的图片，用于存储转换后的结果
	newImg := image.NewRGBA(img.Bounds())

	// 遍历图片的每个像素
	for x := 0; x < img.Bounds().Dx(); x++ {
		for y := 0; y < img.Bounds().Dy(); y++ {
			// 获取原始像素的颜色
			//r, g, b, a := img.At(x, y).RGBA()
			_, _, _, a := img.At(x, y).RGBA()

			// 如果像素是透明的，设置新的颜色为白色
			// 否则，设置新的颜色为黑色
			if x == 0 && y == 0 || x == 0 && y == img.Bounds().Dy()-1 || x == img.Bounds().Dx()-1 && y == 0 || x == img.Bounds().Dx()-1 && y == img.Bounds().Dy()-1 {
				newImg.Set(x, y, color.RGBA{255, 255, 255, 255})
				continue
			}

			if a == 0 {
				newImg.Set(x, y, color.RGBA{0, 0, 0, 255})
			} else {
				newImg.Set(x, y, color.RGBA{255, 255, 255, 255})
			}
		}
	}
	return newImg
}

func SwitchBlackWhite(img image.Image) image.Image {
	// 创建一个新的图片，用于存储转换后的结果
	newImg := image.NewRGBA(img.Bounds())

	// 遍历图片的每个像素
	for x := 0; x < img.Bounds().Dx(); x++ {
		for y := 0; y < img.Bounds().Dy(); y++ {
			// 获取原始像素的颜色
			r, g, b, a := img.At(x, y).RGBA() //0~65535
			//_, _, _, a := img.At(x, y).RGBA()

			// 如果像素是透明的，设置新的颜色为白色
			// 否则，设置新的颜色为黑色
			if r == 0 && g == 0 && b == 0 {
				newImg.Set(x, y, color.RGBA{255, 255, 255, 255})
			} else if r > 0 || g > 0 || b > 0 {
				newImg.Set(x, y, color.RGBA{0, 0, 0, 255})
			} else {
				newImg.Set(x, y, color.RGBA{uint8(r), uint8(g), uint8(b), uint8(a)})
			}
		}
	}
	return newImg
}

func TurnToWhiteBackground(img image.Image) image.Image {

	canvas := image.NewRGBA(image.Rect(0, 0, img.Bounds().Dx(), img.Bounds().Dy()))
	white := image.NewUniform(image.White)
	draw.Draw(canvas, canvas.Bounds(), white, image.Point{}, draw.Src)
	draw.Draw(canvas, canvas.Bounds(), img, image.Point{}, draw.Over)
	return canvas
}
func TurnToBackgroundColor(img image.Image, bgColorStr string) image.Image {
	ary := strings.Split(bgColorStr, ",")
	if len(ary) != 4 {
		logger.Error("bgColorStr 参数不正确，转为白底", bgColorStr)
		bgColorStr = "255,255,255,255"
		ary = strings.Split(bgColorStr, ",")
	}
	ary[3] = "255"
	bgColor := color.RGBA{tools.ParseUint8(ary[0]), tools.ParseUint8(ary[1]), tools.ParseUint8(ary[2]), tools.ParseUint8(ary[3])}
	canvas := image.NewRGBA(image.Rect(0, 0, img.Bounds().Dx(), img.Bounds().Dy()))
	white := image.NewUniform(bgColor)
	draw.Draw(canvas, canvas.Bounds(), white, image.Point{}, draw.Src)
	if ary[3] == "0" {
		// 使用 for 循环遍历每个像素，并将其颜色设置为透明色
		for y := 0; y < canvas.Bounds().Dy(); y++ {
			for x := 0; x < canvas.Bounds().Dx(); x++ {
				canvas.Set(x, y, bgColor)
			}
		}
	}
	draw.Draw(canvas, canvas.Bounds(), img, image.Point{}, draw.Over)
	return canvas
}

func MapBodyFromMask(original image.Image, mask image.Image, invert bool, bgRgbaStr string) image.Image {

	if bgRgbaStr == "" {
		bgRgbaStr = "255,255,255,0"
	}
	ary := strings.Split(bgRgbaStr, ",")
	if len(ary) != 4 {
		logger.Error("bgRgbaStr 参数不正确，转为白底", bgRgbaStr)
		bgRgbaStr = "255,255,255,0"
		ary = strings.Split(bgRgbaStr, ",")
	}
	bR := tools.ParseUint8(ary[0])
	bG := tools.ParseUint8(ary[1])
	bB := tools.ParseUint8(ary[2])
	bA := tools.ParseUint8(ary[3])

	// 创建一个新的图片，用于存储提取的内容
	newImg := image.NewRGBA(original.Bounds())

	// 遍历原图的每个像素
	for x := 0; x < original.Bounds().Dx(); x++ {
		for y := 0; y < original.Bounds().Dy(); y++ {
			// 获取蒙版图的像素颜色
			r, g, b, _ := mask.At(x, y).RGBA()

			// 如果蒙版图的像素是黑色，复制原图的像素到新图
			// 否则，设置新图的像素为透明
			if invert {
				if r < 65535 || g < 65535 || b < 65535 { //不是纯白就设置背景色
					newImg.Set(x, y, color.RGBA{bR, bG, bB, bA})
				} else {
					newImg.Set(x, y, original.At(x, y))
				}
			} else {
				if r == 0 && g == 0 && b == 0 {
					newImg.Set(x, y, original.At(x, y))
				} else {
					newImg.Set(x, y, color.RGBA{bR, bG, bB, bA})
				}
			}

		}
	}
	return newImg
}

func MapFromMask(original image.Image, mask image.Image, invert bool, bgRgbaStr string) image.Image {

	if bgRgbaStr == "" {
		bgRgbaStr = "255,255,255,255"
	}
	ary := strings.Split(bgRgbaStr, ",")
	if len(ary) != 4 {
		logger.Error("bgRgbaStr 参数不正确，转为白底", bgRgbaStr)
		bgRgbaStr = "255,255,255,255"
		ary = strings.Split(bgRgbaStr, ",")
	}
	bR := tools.ParseUint8(ary[0])
	bG := tools.ParseUint8(ary[1])
	bB := tools.ParseUint8(ary[2])
	bA := tools.ParseUint8(ary[3])

	// 创建一个新的图片，用于存储提取的内容
	newImg := image.NewRGBA(original.Bounds())

	// 遍历原图的每个像素
	for x := 0; x < original.Bounds().Dx(); x++ {
		for y := 0; y < original.Bounds().Dy(); y++ {
			// 获取蒙版图的像素颜色
			r, g, b, a := mask.At(x, y).RGBA()

			// 蒙版图的背景是纯白或者存透明 判断条件
			if invert {
				if a == 0 || (r == 65535 && g == 65535 && b == 65535 && a == 65535) { //白底或者透明底
					newImg.Set(x, y, original.At(x, y))
				} else {
					newImg.Set(x, y, color.RGBA{bR, bG, bB, bA})
				}
			} else {
				if a == 0 || (r == 65535 && g == 65535 && b == 65535 && a == 65535) { //白底或者透明底
					newImg.Set(x, y, color.RGBA{bR, bG, bB, bA})
				} else {
					newImg.Set(x, y, original.At(x, y))
				}
			}
		}
	}
	return newImg
}

func MaskFromBody(original image.Image, mask image.Image, invert bool, maskPixels int) image.Image {
	//目前只测试了invert 为true的情况
	bgRgbaStr := "255,255,255,255"
	ary := strings.Split(bgRgbaStr, ",")
	if len(ary) != 4 {
		logger.Error("bgRgbaStr 参数不正确，转为白底", bgRgbaStr)
		bgRgbaStr = "255,255,255,255"
		ary = strings.Split(bgRgbaStr, ",")
	}
	bR := tools.ParseUint8(ary[0])
	bG := tools.ParseUint8(ary[1])
	bB := tools.ParseUint8(ary[2])
	bA := tools.ParseUint8(ary[3])

	outLineImg := GetBodyOutline(original, maskPixels)

	// 创建一个新的图片，用于存储提取的内容
	newImg := image.NewRGBA(original.Bounds())

	// 遍历原图的每个像素
	for x := 0; x < original.Bounds().Dx(); x++ {
		for y := 0; y < original.Bounds().Dy(); y++ {

			or, og, ob, oa := original.At(x, y).RGBA()
			if or != 0 || og != 0 || ob != 0 {

			}
			if oa > 0 && oa < 65535 {

			}

			// 获取蒙版图的像素颜色
			r, g, b, a := mask.At(x, y).RGBA()
			if r != 0 || g != 0 || b != 0 || a != 0 {
				//if r != 65535 && g != 65535 && b != 65535 {
				//	fmt.Println(r, g, b, a)
				//}

			}

			if invert {
				// 如果蒙版图的像素是黑色，将该位置像素设置成白色(背景色)
				// 否则，查看该位置原图像素 如果原图像素不透明，将该位置设置成黑，
				//                      如果原图像素透明,  检测轮廓图
				if r < 65535 || g < 65535 || b < 65535 {
					newImg.Set(x, y, color.RGBA{bR, bG, bB, bA})
				} else {
					if oa < 65535 {
						newImg.Set(x, y, color.RGBA{bR, bG, bB, bA})
					} else {
						newImg.Set(x, y, color.RGBA{0, 0, 0, 255})
					}

					if oa == 65535 {
						newImg.Set(x, y, color.RGBA{0, 0, 0, 255})
					} else {
						lr, lg, lb, la := outLineImg.At(x, y).RGBA()
						if lr > 0 && lg > 0 && lb > 0 && la > 0 { //在白色轮廓上
							newImg.Set(x, y, color.RGBA{0, 0, 0, 255})
						} else {
							newImg.Set(x, y, color.RGBA{bR, bG, bB, bA})
						}
					}
				}

				//if r != 0 || g != 0 || b != 0 {
				//	newImg.Set(x, y, color.RGBA{0, 0, 0, 255})
				//} else { //有一点颜色的
				//	newImg.Set(x, y, color.RGBA{bR, bG, bB, bA})
				//}
				//if r == 0 && g == 0 && b == 0 {
				//	newImg.Set(x, y, color.RGBA{bR, bG, bB, bA})
				//} else {
				//	newImg.Set(x, y, color.RGBA{0, 0, 0, 255})
				//}
			} else {
				// 如果蒙版图的像素是黑色，复制原图的像素到新图
				// 否则，设置新图的像素为透明
				if r != 0 || g != 0 || b != 0 {
					newImg.Set(x, y, color.RGBA{bR, bG, bB, bA})
				} else {
					newImg.Set(x, y, color.RGBA{0, 0, 0, 255})
				}

				//if r == 0 && g == 0 && b == 0 {
				//	newImg.Set(x, y, color.RGBA{0, 0, 0, 255})
				//} else {
				//	newImg.Set(x, y, color.RGBA{bR, bG, bB, bA})
				//}
			}

		}
	}
	return newImg
}

func MaskCorrectFromBody(original image.Image, mask image.Image, effectA uint32) image.Image { //蒙版和原始透明图叠加,去除蒙版中原始图为透明的像素
	//58981
	// 创建一个新的图片，用于存储提取的内容
	newImg := image.NewRGBA(original.Bounds())
	// 遍历原图的每个像素
	for x := 0; x < original.Bounds().Dx(); x++ {
		for y := 0; y < original.Bounds().Dy(); y++ {

			// 获取蒙版图的像素颜色
			r, g, b, a := mask.At(x, y).RGBA()
			if r == 0 && g == 0 && b == 0 && a == 65535 { //蒙版黑色区域
				_, _, _, oa := original.At(x, y).RGBA()
				//if or > 0 || og > 0 || ob > 0 || oa > 0 { //主体图有内容区域
				if oa > effectA { //主体图有内容区域
					newImg.Set(x, y, color.RGBA{0, 0, 0, 255})
				} else {
					newImg.Set(x, y, color.RGBA{255, 255, 255, 255})
				}
			} else {
				newImg.Set(x, y, color.RGBA{255, 255, 255, 255})
			}
		}
	}
	return newImg
}

func MaskClip(originMask image.Image, cropX, cropY, cropWidth, cropHeight int) image.Image {
	// 确保裁剪区域在图像范围内
	bounds := originMask.Bounds()
	if cropX < 0 {
		cropX = 0
	}
	if cropY < 0 {
		cropY = 0
	}
	if cropX+cropWidth > bounds.Dx() {
		cropWidth = bounds.Dx() - cropX
	}
	if cropY+cropHeight > bounds.Dy() {
		cropHeight = bounds.Dy() - cropY
	}
	canvas := image.NewRGBA(image.Rect(0, 0, bounds.Dx(), bounds.Dy()))
	white := image.NewUniform(image.White)
	draw.Draw(canvas, canvas.Bounds(), white, image.Point{}, draw.Src)
	for x := cropX; x < cropX+cropWidth; x++ {
		for y := cropY; y < cropY+cropHeight; y++ {
			r, g, b, _ := originMask.At(x, y).RGBA()
			if r == 0 && g == 0 && b == 0 {
				canvas.Set(x, y, color.RGBA{0, 0, 0, 255})
			}
		}
	}
	return canvas
}

func MaskResize(mask image.Image, size int) image.Image {
	// 创建一个新的图片，用于存储提取的内容
	if size == 0 {
		return mask
	}

	reduce := false
	if size < 0 {
		reduce = true
		size = size * -1
	}

	bounds := mask.Bounds()
	newImg := image.NewRGBA(bounds)

	// 将背景图像绘制到目标图像
	draw.Draw(newImg, bounds, mask, image.Point{}, draw.Src)

	//newImg := image.NewRGBA(mask.Bounds())

	removePoint := make(map[string]image.Point)

	// 遍历原图的每个像素
	for x := 0; x < mask.Bounds().Dx(); x++ {
		for y := 0; y < mask.Bounds().Dy(); y++ {
			// 获取蒙版图的像素颜色
			r, g, b, a := mask.At(x, y).RGBA()
			if r == 0 && g == 0 && b == 0 && a == 65535 { //蒙版黑色区域
				if reduce {
					y = y - 1
					for i := 0; i < size; i++ {
						y = y + 1
						if y < mask.Bounds().Dy() {
							key := fmt.Sprintf("%d_%d", x, y)
							if _, ok := removePoint[key]; !ok {
								removePoint[key] = image.Point{X: x, Y: y}
							}
						}
					}
				} else {
					for i := 0; i < size; i++ {
						y = y - 1
						if y >= 0 {
							key := fmt.Sprintf("%d_%d", x, y)
							if _, ok := removePoint[key]; !ok {
								removePoint[key] = image.Point{X: x, Y: y}
							}
						}
					}
				}

				break
			}
		}

		for y := mask.Bounds().Dy() - 1; y >= 0; y-- {
			// 获取蒙版图的像素颜色
			r, g, b, a := mask.At(x, y).RGBA()
			if r == 0 && g == 0 && b == 0 && a == 65535 { //蒙版黑色区域
				if reduce {
					y = y + 1
					for i := 0; i < size; i++ {
						y = y - 1
						if y >= 0 {
							key := fmt.Sprintf("%d_%d", x, y)
							if _, ok := removePoint[key]; !ok {
								removePoint[key] = image.Point{X: x, Y: y}
							}
						}
					}
				} else {
					for i := 0; i < size; i++ {
						y = y + 1
						if y < mask.Bounds().Dy() {
							key := fmt.Sprintf("%d_%d", x, y)
							if _, ok := removePoint[key]; !ok {
								removePoint[key] = image.Point{X: x, Y: y}
							}
						}
					}
				}

				break
			}
		}
	}

	for y := 0; y < mask.Bounds().Dy(); y++ {
		for x := 0; x < mask.Bounds().Dx(); x++ {
			// 获取蒙版图的像素颜色
			r, g, b, a := mask.At(x, y).RGBA()
			if r == 0 && g == 0 && b == 0 && a == 65535 { //蒙版黑色区域
				if reduce {
					x = x - 1
					for i := 0; i < size; i++ {
						x = x + 1
						if x < mask.Bounds().Dx() {
							key := fmt.Sprintf("%d_%d", x, y)
							if _, ok := removePoint[key]; !ok {
								removePoint[key] = image.Point{X: x, Y: y}
							}
						}
					}

				} else {
					for i := 0; i < size; i++ {
						x = x - 1
						if x >= 0 {
							key := fmt.Sprintf("%d_%d", x, y)
							if _, ok := removePoint[key]; !ok {
								removePoint[key] = image.Point{X: x, Y: y}
							}
						}
					}
				}
				break
			}
		}

		for x := mask.Bounds().Dx() - 1; x >= 0; x-- {
			// 获取蒙版图的像素颜色
			r, g, b, a := mask.At(x, y).RGBA()
			if r == 0 && g == 0 && b == 0 && a == 65535 { //蒙版黑色区域
				if reduce {
					x = x + 1
					for i := 0; i < size; i++ {
						x = x - 1
						if x >= 0 {
							key := fmt.Sprintf("%d_%d", x, y)
							if _, ok := removePoint[key]; !ok {
								removePoint[key] = image.Point{X: x, Y: y}
							}
						}
					}
				} else {
					for i := 0; i < size; i++ {
						x = x + 1
						if x < mask.Bounds().Dx() {
							key := fmt.Sprintf("%d_%d", x, y)
							if _, ok := removePoint[key]; !ok {
								removePoint[key] = image.Point{X: x, Y: y}
							}
						}
					}
				}
				break
			}
		}
	}

	if reduce {
		for _, v := range removePoint {
			newImg.Set(v.X, v.Y, color.RGBA{255, 255, 255, 255})
		}
	} else {
		for _, v := range removePoint {
			newImg.Set(v.X, v.Y, color.RGBA{0, 0, 0, 255})
		}
	}

	return newImg
}

func MaskMargin(mask image.Image, direction string, size int) image.Image {
	//direction:left top bottom right
	// 创建一个新的图片，用于存储提取的内容
	if direction != "left" && direction != "top" && direction != "bottom" && direction != "right" {
		logger.Errorf("方向参数错误", size)
		return mask
	}

	reduce := false
	if size < 0 {
		reduce = true
		size = size * -1
	}

	bounds := mask.Bounds()
	newImg := image.NewRGBA(bounds)

	// 将背景图像绘制到目标图像
	draw.Draw(newImg, bounds, mask, image.Point{}, draw.Src)

	//newImg := image.NewRGBA(mask.Bounds())

	removePoint := make(map[string]image.Point)
	// 遍历原图的每个像素
	if direction == "top" || direction == "bottom" {
		for x := 0; x < mask.Bounds().Dx(); x++ {

			if direction == "top" {
				for y := 0; y < mask.Bounds().Dy(); y++ {
					// 获取蒙版图的像素颜色
					r, g, b, a := mask.At(x, y).RGBA()
					if r == 0 && g == 0 && b == 0 && a == 65535 { //蒙版黑色区域
						if reduce {
							y = y - 1
							for i := 0; i < size; i++ {
								y = y + 1
								if y < mask.Bounds().Dy() {
									key := fmt.Sprintf("%d_%d", x, y)
									if _, ok := removePoint[key]; !ok {
										removePoint[key] = image.Point{X: x, Y: y}
									}
								}
							}
						} else {
							for i := 0; i < size; i++ {
								y = y - 1
								if y >= 0 {
									key := fmt.Sprintf("%d_%d", x, y)
									if _, ok := removePoint[key]; !ok {
										removePoint[key] = image.Point{X: x, Y: y}
									}
								}
							}
						}

						break
					}
				}
			} else {
				for y := mask.Bounds().Dy() - 1; y >= 0; y-- {
					// 获取蒙版图的像素颜色
					r, g, b, a := mask.At(x, y).RGBA()
					if r == 0 && g == 0 && b == 0 && a == 65535 { //蒙版黑色区域
						if reduce {
							y = y + 1
							for i := 0; i < size; i++ {
								y = y - 1
								if y >= 0 {
									key := fmt.Sprintf("%d_%d", x, y)
									if _, ok := removePoint[key]; !ok {
										removePoint[key] = image.Point{X: x, Y: y}
									}
								}
							}
						} else {
							for i := 0; i < size; i++ {
								y = y + 1
								if y < mask.Bounds().Dy() {
									key := fmt.Sprintf("%d_%d", x, y)
									if _, ok := removePoint[key]; !ok {
										removePoint[key] = image.Point{X: x, Y: y}
									}
								}
							}
						}

						break
					}
				}
			}
		}
	}

	if direction == "left" || direction == "right" {
		for y := 0; y < mask.Bounds().Dy(); y++ {
			if direction == "left" {
				for x := 0; x < mask.Bounds().Dx(); x++ {
					// 获取蒙版图的像素颜色
					r, g, b, a := mask.At(x, y).RGBA()
					if r == 0 && g == 0 && b == 0 && a == 65535 { //蒙版黑色区域
						if reduce {
							x = x - 1
							for i := 0; i < size; i++ {
								x = x + 1
								if x < mask.Bounds().Dx() {
									key := fmt.Sprintf("%d_%d", x, y)
									if _, ok := removePoint[key]; !ok {
										removePoint[key] = image.Point{X: x, Y: y}
									}
								}
							}

						} else {
							for i := 0; i < size; i++ {
								x = x - 1
								if x >= 0 {
									key := fmt.Sprintf("%d_%d", x, y)
									if _, ok := removePoint[key]; !ok {
										removePoint[key] = image.Point{X: x, Y: y}
									}
								}
							}
						}
						break
					}
				}
			} else {
				for x := mask.Bounds().Dx() - 1; x >= 0; x-- {
					// 获取蒙版图的像素颜色
					r, g, b, a := mask.At(x, y).RGBA()
					if r == 0 && g == 0 && b == 0 && a == 65535 { //蒙版黑色区域
						if reduce {
							x = x + 1
							for i := 0; i < size; i++ {
								x = x - 1
								if x >= 0 {
									key := fmt.Sprintf("%d_%d", x, y)
									if _, ok := removePoint[key]; !ok {
										removePoint[key] = image.Point{X: x, Y: y}
									}
								}
							}
						} else {
							for i := 0; i < size; i++ {
								x = x + 1
								if x < mask.Bounds().Dx() {
									key := fmt.Sprintf("%d_%d", x, y)
									if _, ok := removePoint[key]; !ok {
										removePoint[key] = image.Point{X: x, Y: y}
									}
								}
							}
						}

						break
					}
				}
			}
		}
	}

	if reduce {
		for _, v := range removePoint {
			newImg.Set(v.X, v.Y, color.RGBA{255, 255, 255, 255})
		}
	} else {
		for _, v := range removePoint {
			newImg.Set(v.X, v.Y, color.RGBA{0, 0, 0, 255})
		}
	}

	return newImg
}

func MaskAllFromBody(body image.Image, effectA uint32) image.Image { //从原始透明图获取蒙版  effectA:0-65535  一半是32767  3/4是49151 1/10是58981
	// 创建一个新的图片，用于存储提取的内容
	newImg := image.NewRGBA(body.Bounds())
	// 遍历原图的每个像素
	for x := 0; x < body.Bounds().Dx(); x++ {
		for y := 0; y < body.Bounds().Dy(); y++ {
			// 获取主体图的像素颜色
			_, _, _, a := body.At(x, y).RGBA()
			if a <= effectA {
				newImg.Set(x, y, color.RGBA{255, 255, 255, 255})
			} else {
				newImg.Set(x, y, color.RGBA{0, 0, 0, 255})
			}
		}
	}
	return newImg
}

func MaskInvert(mask image.Image) image.Image { //蒙版反转 黑变白 百变黑
	// 创建一个新的图片，用于存储提取的内容
	newImg := image.NewRGBA(mask.Bounds())
	// 遍历原图的每个像素
	for x := 0; x < mask.Bounds().Dx(); x++ {
		for y := 0; y < mask.Bounds().Dy(); y++ {

			// 获取蒙版图的像素颜色
			r, g, b, a := mask.At(x, y).RGBA()
			if r == 0 && g == 0 && b == 0 && a == 65535 { //蒙版黑色区域
				newImg.Set(x, y, color.RGBA{255, 255, 255, 255})
			} else {
				newImg.Set(x, y, color.RGBA{0, 0, 0, 255})
			}
		}
	}
	return newImg
}

func MergeImg(origin image.Image, crop image.Image, cropX int, cropY int) image.Image {
	// 创建目标图像，与背景图像大小相同
	bounds := origin.Bounds()
	destinationImage := image.NewRGBA(bounds)

	// 将背景图像绘制到目标图像
	draw.Draw(destinationImage, bounds, origin, image.Point{}, draw.Src)

	// 将覆盖图像绘制到目标图像的指定位置
	draw.Draw(destinationImage, crop.Bounds().Add(image.Point{cropX, cropY}), crop, image.Point{}, draw.Over)
	return destinationImage
}

// CropImg 裁剪图像
func CropImg(origin image.Image, cropX, cropY, cropWidth, cropHeight int) image.Image {
	// 确保裁剪区域在图像范围内
	bounds := origin.Bounds()
	if cropX < 0 {
		cropX = 0
	}
	if cropY < 0 {
		cropY = 0
	}
	if cropX+cropWidth > bounds.Dx() {
		cropWidth = bounds.Dx() - cropX
	}
	if cropY+cropHeight > bounds.Dy() {
		cropHeight = bounds.Dy() - cropY
	}

	// 创建一个新图像，裁剪区域的大小
	croppedImg := image.NewRGBA(image.Rect(0, 0, cropWidth, cropHeight))

	// 裁剪图像
	cropRect := image.Rect(cropX, cropY, cropX+cropWidth, cropY+cropHeight)
	draw.Draw(croppedImg, croppedImg.Bounds(), origin, cropRect.Min, draw.Src)

	return croppedImg
}

func GetBodyOutline(original image.Image, pixels int) image.Image {
	bgRgbaStr := "255,255,255,255"
	ary := strings.Split(bgRgbaStr, ",")
	if len(ary) != 4 {
		logger.Error("bgRgbaStr 参数不正确，转为白底", bgRgbaStr)
		bgRgbaStr = "255,255,255,255"
		ary = strings.Split(bgRgbaStr, ",")
	}

	// 创建一个新的图片，用于存储提取的内容
	newImg := image.NewRGBA(original.Bounds())

	//mOutLine := make(map[string]interface{})
	mOutLineY := make(map[int][]int)
	mOutLineX := make(map[int][]int)
	//fmt.Println(original.Bounds().Dx(), "   ", original.Bounds().Dy())
	for x := 0; x < original.Bounds().Dx(); x++ {
		for y := 0; y < original.Bounds().Dy(); y++ {
			_, _, _, oa := original.At(x, y).RGBA()
			if oa > 0 && oa < 65535 {
				if aryY, ok := mOutLineX[x]; ok {
					mOutLineX[x] = append(aryY, y)
				} else {
					mOutLineX[x] = make([]int, 0)
					mOutLineX[x] = append(mOutLineX[x], y)
				}
			}
		}
	}
	for y := 0; y < original.Bounds().Dy(); y++ {
		for x := 0; x < original.Bounds().Dx(); x++ {
			_, _, _, oa := original.At(x, y).RGBA()

			if oa > 0 && oa < 65535 {
				if aryX, ok := mOutLineY[y]; ok {
					mOutLineY[y] = append(aryX, x)
				} else {
					mOutLineY[y] = make([]int, 0)
					mOutLineY[y] = append(mOutLineY[y], x)
				}
			}
		}
	}

	for key, value := range mOutLineY {
		l := len(value)
		startX := value[0]
		endX := value[0]

		if l > 1 {
			startX = value[0] + pixels
			endX = value[l-1] - pixels
		}

		for i := 0; i < l; i++ {
			x := value[i]
			if x >= startX && x <= endX {
				newImg.Set(value[i], key, color.RGBA{255, 255, 255, 255})
			}
		}
	}

	for key, value := range mOutLineX {
		l := len(value)
		startY := value[0]
		endY := value[0]

		if l > 2 {
			startY = value[0] + pixels
			endY = value[l-1] - pixels
		}

		for i := 0; i < l; i++ {
			y := value[i]
			if y < startY || y > endY {
				newImg.Set(value[i], key, color.RGBA{0, 0, 0, 0})
			}
		}
	}

	return newImg
}

func GetExifComment(filename string) (string, error) {
	// 打开原始 PNG 图像文件
	inputFile, err := os.Open(filename)
	if err != nil {
		return "", err
	}
	defer inputFile.Close()

	// 解析原始 Exif 数据
	x, err := exif.Decode(inputFile)
	if err != nil {
		return "", err
	}
	return x.String(), nil
}

package push

import (
	"center-ai/utils/logger"
	"github.com/gin-gonic/gin"
	"io/ioutil"
	"net/http"
)

func Notify(c *gin.Context) {
	logger.Info("已经接收到Push回调")

	callBackIdStr := c.Request.Header.Get("X-HUAWEI-CALLBACK-ID")
	logger.Info("X-HUAWEI-CALLBACK-ID", callBackIdStr)

	body, _ := ioutil.ReadAll(c.Request.Body)
	logger.Info("---body/--- rn " + string(body))
	bodyStr := string(body)
	bodyStr = `{"statuses":[{"biTag":"GT_0726_b2bfe0bcb1804b62d152835c018fc738","requestId":"169035299611484243032101","appPackageName":"com.cyuai.aigc","deliveryStatus":{"result":256,"timestamp":1690352996558},"token":"IQAAAACy01nIAAC6lLrgdsCT70qt-BzK_VfxGsiEA_e_aRP6sWSY666jt4VaPeJlTQ3gzze1DK88ci8g4kdHSVRuRoCyREky3PjP6K0pP6EmSTYpjw"}]}`

	if bodyStr != "" {

	}

	c.JSON(http.StatusOK, gin.H{
		"code":    "0",
		"message": "success",
	})
}

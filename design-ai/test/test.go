package main

import (
	"design-ai/model"
	"design-ai/service"
	"design-ai/utils/logger"
	"design-ai/utils/myredis"
	"design-ai/utils/tools"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
)

func getPath(pid int) (string, error) {
	cmd := exec.Command("ps", "-p", strconv.Itoa(pid), "-o", "comm=")
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}
	command := strings.TrimSpace(string(output))
	cmd = exec.Command("which", command)
	output, err = cmd.Output()
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(string(output)), nil
}

//
//func getExecutableDir(pid int) (string, error) {
//	// 使用 ps 命令获取进程信息，awk 命令从输出结果中提取命令路径信息
//	cmd := exec.Command("ps", "-p", strconv.Itoa(pid), "-o", "comm=")
//	cmd2 := exec.Command("awk", "{print \"/proc/\"$1\"/exe\"}")
//	// 将两个命令串联起来
//	cmd2.Stdin, _ = cmd.StdoutPipe()
//	cmd2.Stderr = cmd.Stderr
//	err := cmd2.Start()
//	if err != nil {
//		return "", err
//	}
//	output, err := cmd.Output()
//	fmt.Println(output)
//	if err != nil {
//		return "", err
//	}
//	err = cmd.Wait()
//	if err != nil {
//		return "", err
//	}
//	exePath, err := cmd2.Output()
//	if err != nil {
//		return "", err
//	}
//	return strings.TrimSpace(string(exePath)), nil
//}

func getExecutableDir(pid int) (string, error) {
	cmd := exec.Command("ps", "-o", "pid=,comm=,command=", "-p", fmt.Sprintf("%d", pid))
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}
	lines := strings.Split(string(output), "\n")
	if len(lines) < 2 {
		return "", fmt.Errorf("failed to get process information")
	}
	fields := strings.Fields(lines[1])
	if len(fields) < 3 {
		return "", fmt.Errorf("failed to parse process information")
	}
	cmdPath := fields[2]
	return filepath.Dir(cmdPath), nil
}

func getExecutablePathByPID11(pid int) (string, error) {
	// 执行 lsof 命令查看进程打开的文件
	cmd := exec.Command("lsof", "-a", "-d", "txt", "-p", strconv.Itoa(pid))
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}

	// 从 lsof 命令输出中找到执行文件对应的文件描述符
	var fd string
	for _, line := range strings.Split(string(output), "\n") {
		fields := strings.Fields(line)
		if len(fields) >= 2 && fields[1] == strconv.Itoa(pid) {
			fd = fields[3]
			break
		}
	}
	if fd == "" {
		return "", fmt.Errorf("failed to find file descriptor for PID %d", pid)
	}

	// 执行 readlink 命令查看文件描述符对应的文件路径
	cmd = exec.Command("readlink", "-f", fmt.Sprintf("/proc/%d/fd/%s", pid, fd))
	output, err = cmd.Output()
	if err != nil {
		return "", err
	}

	return strings.TrimSpace(string(output)), nil
}

func getExecutablePath(pid int) (string, error) {
	cmd := exec.Command("ps", "-p", strconv.Itoa(pid), "-o", "comm=")
	comm, err := cmd.Output()
	if err != nil {
		return "", err
	}
	cmd = exec.Command("ps", "-p", strconv.Itoa(pid), "-o", "comm=,args=")
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}
	parts := strings.Split(strings.TrimSpace(string(output)), " ")
	if len(parts) < 2 {
		return "", errors.New("unexpected output from ps command")
	}
	comm = []byte(parts[0])
	args := []byte(strings.Join(parts[1:], " "))
	fmt.Println(args)
	return exec.LookPath(string(comm))
}

func getExecutablePathByPID(pid int) (string, error) {
	// 执行 ps 命令查看进程信息
	cmd := exec.Command("ps", "-o", "comm=", "-p", strconv.Itoa(pid))
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}

	// 从 ps 命令输出中找到执行文件名
	comm := strings.TrimSpace(string(output))
	if comm == "" {
		return "", fmt.Errorf("failed to find command name for PID %d", pid)
	}

	// 执行 which 命令查找执行文件路径
	cmd = exec.Command("which", comm)
	output, err = cmd.Output()
	if err != nil {
		return "", err
	}

	return strings.TrimSpace(string(output)), nil
}

func getPIDs(processName string) []int {
	cmd := exec.Command("pgrep", processName)
	output, err := cmd.Output()
	if err != nil {
		return nil
	}
	pidStr := strings.TrimSpace(string(output))
	ary := strings.Split(pidStr, "\n")
	pidAry := make([]int, 0)
	for _, val := range ary {
		pid, _ := strconv.Atoi(val)
		if pid > 0 {
			pidAry = append(pidAry, pid)
		}
	}
	return pidAry
}

func getProcessPath(pid int) (string, error) {
	exe, err := os.Readlink(fmt.Sprintf("/proc/%d/exe", pid))
	if err != nil {
		return "", err
	}
	return filepath.EvalSymlinks(exe)
}

func main() {

	//aa := `{"custom_app":"roodesign","custom_data":"{\"design_id\":11472,\"md5\":\"970193486ed93314745fd9e02fe3922c\"}","custom_md5":"970193486ed93314745fd9e02fe3922c","custom_path":"202311/970193486ed93314745fd9e02fe3922c.png","model_name":"collect/dvarchMultiPrompt_dvarch.ckpt","model_hash":"1ecb6b4e9c","prompt":"interior, architectural, (RAW photo, best quality, high quality), (realistic, photo-realistic), (8k), ultra-detailed,,<lora:interior_973c1ee7c4ea471e:0.700000>","negative_prompt":"people,face,limbs,head,body,nsfw,human,male,female,men, women, boy, girl,paintings, sketches, (worst quality:2), (low quality:2), (normal quality:2), lowres, normal quality, ((monochrome)), NG_DeepNegative_V1_75T","sampler_name":"Euler a","width":1024,"height":728,"steps":25,"cfg_scale":7,"seed":-1,"eta":0,"control_net_units":"{\"mlsd\":{\"weight\":1.0}}","control_net_input_image_path":"roodesign/202311/67ee08ffd6af66aa291543b1678377e5.jpg","units":[]}`

	//service.DesignService.PushJson1(aa, "970193486ed93314745fd9e02fe3922c", "interior_973c1ee7c4ea471e:0.700000")
	//m := map[string]interface{}{
	//	"key1": "value1",
	//	"key2": 123,
	//	"key3": []string{"a", "b", "c"},
	//}
	//
	//n := m["key1"]
	//nn, ok := m["key2"]
	//if ok {
	//	nn = nn.(int)
	//}
	//println(n, nn)
	//wechatpay.PayService.Init()
	//myredis.InitRedis()
	//isLock1, err1 := myredis.IsLocked("aaabbbcc")
	//fmt.Println(isLock1, err1)
	//fmt.Println(myredis.Lock("aaabbb"))
	//isLock, err := myredis.IsLocked("aaabbb")
	//fmt.Println(isLock, err)
	//service.BaoTaService.RestartProject("aigc_api_4005")

	//pids := getPIDs("mysite.main")
	//logger.Info(pids)
	//
	//for _, pid := range pids {
	//	fmt.Println(getExecutablePath(pid))
	//}

	//path, err := getPath("mysite")
	//if err != nil {
	//	fmt.Println("Failed to get path:", err)
	//	return
	//}
	//fmt.Println("Path of chrome:", path)

	model.InitDb()
	myredis.InitRedis()

	service.LocationService.UpdateLocation(fmt.Sprintf(`{"type":"%s","id":%d}`, "userlog", 516))

	pid1, path, err1 := tools.GetProcessInfo("mysite")
	if err1 != nil {
		logger.Error(pid1, path)
	}

	//pid, err := tools.GetPID("mysite")
	//if err != nil {
	//	logger.Error(pid)
	//}
	//logger.Error(pid)
	tools.GetPathByPID(52938)
	tools.GetPIDs("mysite.main")
	//
	//roomTypeValKeys := "主卧,卧室"
	//roomStyleValKeys := "诧寂"
	//service.LoarService.LoadData(true)
	//tmpAry := service.LoarService.DetectLoraId(roomTypeValKeys, roomStyleValKeys)
	//if len(tmpAry) > 0 {
	//	logger.Info(tmpAry)
	//}

	//width := 400
	//height := 500
	//w, h := service.ImgService.CalculateSize(width, height, 1024)
	//logger.Info("参考图尺寸转换 原始w:", width, " h:", height, "  转换后w:", w, " h:", h)
	//w = w - w%8
	//h = h - h%8
	//logger.Info("参考图尺寸转换 原始w:", width, " h:", height, "  8转换后w:", w, " h:", h)
}

package service

import (
	"bytes"
	"design-ai/enums"
	"design-ai/model"
	"design-ai/utils/config"
	"design-ai/utils/logger"
	"design-ai/utils/myimg"
	"design-ai/utils/myredis"
	"design-ai/utils/tools"
	"encoding/json"
	"errors"
	"fmt"
	"path"
	"strings"
	"unicode/utf8"
)

type design_ struct {
}

type Unit struct {
	InputImagePath string  `json:"input_image_path"`
	Module         string  `json:"module"`
	Model          string  `json:"model"`
	ThresholdA     float64 `json:"threshold_a"`
	ThresholdB     float64 `json:"threshold_b"`
	Weight         float64 `json:"weight"`
	Guessmode      bool    `json:"guessmode"`
}

type designInputData struct {
	CustomApp                string  `json:"custom_app"`
	CustomData               string  `json:"custom_data"`
	CustomMd5                string  `json:"custom_md5"`
	CustomPath               string  `json:"custom_path"`
	ModelName                string  `json:"model_name"`
	ModelHash                string  `json:"model_hash"`
	Prompt                   string  `json:"prompt"`
	NegativePrompt           string  `json:"negative_prompt"`
	SamplerName              string  `json:"sampler_name"`
	Width                    int     `json:"width"`
	Height                   int     `json:"height"`
	Steps                    int     `json:"steps"`
	CfgScale                 float64 `json:"cfg_scale"`
	Seed                     int     `json:"seed"`
	Eta                      float64 `json:"eta"`
	ControlNetUnits          string  `json:"control_net_units"`
	ControlNetInputImagePath string  `json:"control_net_input_image_path"`
	Units                    []Unit  `json:"units"`
}

type designOutputData_ struct {
	CustomApp   string `json:"custom_app"`
	SisPath     string `json:"sis_path"`
	SisMd5      string `json:"sis_md5"`
	CustomData  string `json:"custom_data"`
	ExecuteTime int    `json:"execute_time"`
}

type designCustomData_ struct {
	HandId uint   `json:"hand_id"`
	Md5    string `json:"md5"`
}

func (d *design_) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("Design奔溃:", e)
		}
	}()
	logger.Info("Design.Run 开始循环获取图片")
	for {
		value, err := d.PopJson()

		if err != nil {
			logger.Error(value, err)
			continue
		}
		if value == "" {
			continue
		}
		logger.Info("接收到绘图json:", value)
		if err := d.UpdateToImg(value); err != nil {
			logger.Error(err)
		}
	}
}

func (d *design_) Run1() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("Design奔溃:", e)
		}
	}()
	logger.Info("Design.Run 开始循环获取图片")
	for {
		value, err := d.PopJson1()

		if err != nil {
			logger.Error(value, err)
			continue
		}
		if value == "" {
			continue
		}
		logger.Info("接收到绘图json:", value)
		if err := d.UpdateToImg1(value); err != nil {
			logger.Error(err)
		}
	}
}

func (d *design_) UpdateToImg(value string) error {
	outputData := designOutputData_{}
	customData := designCustomData_{}
	if value == "" {
		logger.Error("数据为空")
		return errors.New("数据为空")
	}

	if err := json.Unmarshal([]byte(value), &outputData); err != nil {
		logger.Error(err)
		return err
	}

	if err := json.Unmarshal([]byte(outputData.CustomData), &customData); err != nil {
		logger.Error(err)
		return err
	}

	var outImg model.OutImg
	if err := outImg.GetByMd5(customData.Md5); err != nil {
		logger.Error(err)
		return err
	} else {
		if outImg.Path != "" {
			err := errors.New("path字段不为空")
			logger.Error(err, customData.Md5)
			return err
		}
		if err := outImg.SetPath(outputData.SisPath); err != nil {
			logger.Error(err)
			return err
		}

		w, h := ImgService.CalculateSize(outImg.Width, outImg.Height, 1024)
		w8 := w - w%8
		h8 := h - h%8
		if json, err := UpScaleServiceV2.GetUpScaleJson(outImg.Path, outImg.ID, 1, w8*4, h8*4); err == nil {
			if _, err1 := UpScaleServiceV2.PushRedisQueue(json); err1 == nil {
				logger.Info("发送超分JSON成功", json)
			} else {
				logger.Error("发送超分JSON失败", err1, " ", json)
			}
		} else {
			logger.Error(err)
		}
	}

	return nil
}
func (d *design_) UpdateToImg1(value string) error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("UpdateToImg1奔溃:", e)
		}
	}()
	if value == "" {
		logger.Error("数据为空")
		return errors.New("数据为空")
	}
	outImgPath := ""
	outImgMd5 := ""
	mSdOutput := tools.GetMapFromJson(value)

	if val, ok := mSdOutput["error_info"]; ok && val.(string) != "" {
		mResult := tools.GetMapFromJson(val.(string))
		if path, exists := mResult["out_image_path"]; exists {
			outImgPath = path.(string)
		}
	}

	if val, ok := mSdOutput["result"]; ok && val.(string) != "" {
		mResult := tools.GetMapFromJson(val.(string))
		if path, exists := mResult["out_image_path"]; exists {
			outImgPath = path.(string)
		}
	} else {
		logger.Error(" key result  is not exist!", value)
	}

	if md5, ok := mSdOutput["custom_md5"]; ok {
		outImgMd5 = md5.(string)
	}

	if outImgMd5 == "" {
		err := errors.New("path字段不为空")
		logger.Error(err, outImgMd5)
		return err
	}
	var outImg model.OutImg
	if err := outImg.GetByMd5(outImgMd5); err != nil {
		logger.Error(err)
		return err
	}

	if outImgPath == "" { //绘图出错处理
		//if err := outImg.SetInfo(value); err != nil {
		//	logger.Error(err)
		//}
		//d.RemoveProgress(outImgMd5)
		err := errors.New("outImgPath 为空")
		logger.Error(err)
		return err
	}

	img, _, err := myimg.FileToImg(config.DiffusionFilePath + outImgPath)
	if err != nil {
		logger.Error(err)
		return err
	}
	if img == nil {
		logger.Error("图片不存在", value)
		return fmt.Errorf("图片不存在")
	}

	sdApi := ""
	if _, ok := mSdOutput["sdapi"]; ok {
		sdApi = mSdOutput["sdapi"].(string)
	}

	if outImg.Path == "" {
		if err := outImg.SetPath(outImgPath); err != nil {
			logger.Error(err)
			return err
		}
		d.RemoveProgress(outImgMd5)
		if err := d.UpscaleImg(outImg.ID); err != nil {
			logger.Error(err)
			return err
		}
	}
	if sdApi == "UpscaleImg" { //超分回调
		if _, ok := mSdOutput["custom_data"]; ok {
			mCustomData := tools.GetMapFromJson(mSdOutput["custom_data"].(string))
			imgId := uint(mCustomData["img_id"].(float64))
			level := int(mCustomData["level"].(float64))
			if err := outImg.SetUpscalePath(imgId, level, outImgPath); err != nil {
				logger.Error(err)
				return err
			}
			logger.Info("超分完成", imgId, "   ", level, "   ", outImgPath, "   ", outImgMd5)
		}
		d.RemoveProgress(outImgMd5)
	}

	return nil
}
func (d *design_) UpscaleImg(outImgId uint) error {
	var outImg model.OutImg
	if err := outImg.GetByID(outImgId); err != nil {
		logger.Error(err)
		return err
	}
	w, h := ImgService.CalculateSize(outImg.Width, outImg.Height, 1024)
	w8 := w - w%8
	h8 := h - h%8
	upScaleW := w8 * 4
	upScaleH := h8 * 4

	parameters := make(map[string]interface{})
	imagesPath := make(map[string]string)

	fields := make(map[string]interface{})

	param, err := UpScaleServiceV3.GetUpScaleParameters(outImg.ID, 1, upScaleW, upScaleH)
	if err != nil {
		logger.Error(err)
		return err
	}

	if tmpParameters, err := SdClient.GenUpscaleParameter(param); err != nil {
		logger.Error(err)
		return err
	} else {
		parameters = tmpParameters
	}

	parametersStr := tools.GetJsonFromMap(parameters)
	if strings.Contains(parametersStr, "{==={upscale_image}===}") {
		imagesPath["upscale_image"] = outImg.Path
	}

	customData := make(map[string]interface{})
	customData["img_id"] = outImg.ID
	customData["level"] = 1

	ext := path.Ext(outImg.Path)
	customPath := strings.Replace(outImg.Path, ext, fmt.Sprintf("_%dx%d.jpg", upScaleW, upScaleH), -1)
	customPath = strings.Replace(customPath, "roodesign/", "", -1)

	fields["sdapi"] = "UpscaleImg"
	//fields["sd_server"] = "http://192.168.200.95:7862/"
	fields["jump_ticket"] = outImg.Md5
	fields["custom_app"] = "roodesign"
	fields["custom_data"] = tools.GetJsonFromMap(customData)
	fields["custom_md5"] = outImg.Md5
	fields["custom_path"] = customPath
	fields["parameters"] = parametersStr
	fields["images_path"] = imagesPath

	options := make(map[string]interface{})
	options["need_info"] = true
	fields["options"] = options

	pushJson := tools.GetJsonFromMap(fields)
	logger.Info("发送超分JSON1:", pushJson)
	if str, err := SdClient.Sd2img(pushJson); err != nil {
		logger.Error(err, str)
		return err
	} else {
		logger.Info("Post Res:", str)
		//lastStr = str
	}
	return nil
}

func (d *design_) GetPushJsonTemplate(designId uint, templatePromptId int, redrawMode int, newSd bool) string {
	logger.Info("开始生成绘图模板  desiginId:", designId, "  templatePromptId:", templatePromptId, "  redrawMode:", redrawMode, " newSd:", newSd)
	var design model.Design
	if err := design.GetById(designId); err != nil {
		logger.Error(err)
		return ""
	}

	enPrompt := ""
	if design.Prompt != "" {
		enPrompt = TencentCloud.TextTranslate(design.Prompt)
		if utf8.RuneCountInString(enPrompt) == 0 || utf8.RuneCountInString(enPrompt) < utf8.RuneCountInString(design.Prompt) {
			logger.Error("翻译失效 ", design.ID, enPrompt, design.Prompt)
			enPrompt = design.Prompt
		}
	}
	enNegativePrompt := ""
	if design.NegativePrompt != "" {
		enNegativePrompt = TencentCloud.TextTranslate(design.NegativePrompt)
		if utf8.RuneCountInString(enNegativePrompt) == 0 || utf8.RuneCountInString(enNegativePrompt) < utf8.RuneCountInString(design.NegativePrompt) {
			logger.Error("翻译失效 ", design.ID, enPrompt, design.Prompt)
			enNegativePrompt = design.NegativePrompt
		}
	}

	//units := []Unit{
	//	{
	//		InputImagePath: design.InputImgPath,
	//		Module:         "canny",
	//		Model:          "control_canny-fp16 [e3fe7712]",
	//		ThresholdA:     100.0,
	//		ThresholdB:     200.0,
	//		Weight:         0.6,
	//		Guessmode:      true,
	//	},
	//	{
	//		InputImagePath: design.InputImgPath,
	//		Module:         "mlsd",
	//		Model:          "control_mlsd-fp16 [e3705cfa]",
	//		ThresholdA:     0.1,
	//		ThresholdB:     0.1,
	//		Weight:         1.0,
	//		Guessmode:      true,
	//	},
	//}
	units := make([]Unit, 0)

	customData := fmt.Sprintf(`{"design_id":%d,"md5":"{==={CustomMd5}===}"}`, design.ID)

	//transPrompt = "architectural, (RAW photo, best quality, high quality), (realistic, photo-realistic), (8k), ultra-detailed,, <lora:interior_cd87ddb831734133:0.8>"
	transPrompt := "architectural, (RAW photo, best quality, high quality), (realistic, photo-realistic), (8k), ultra-detailed,, <lora:{==={lora}===}:{==={LoraWeight}===}>"
	w, h := ImgService.CalculateSize(design.Width, design.Height, 1024)

	w8 := w - w%8
	h8 := h - h%8
	logger.Info("参考图尺寸转换 原始w:", design.Width, " h:", design.Height, "  转换后w:", w, " h:", h, "  转换8整除后w:", w8, " h:", h8)

	data := designInputData{
		CustomApp:                "roodesign",
		CustomData:               customData,
		CustomMd5:                "{==={CustomMd5}===}",
		ModelName:                "collect/dvarchMultiPrompt_dvarch.ckpt",
		ModelHash:                "1ecb6b4e9c",
		Prompt:                   transPrompt,
		NegativePrompt:           "nsfw,human,male,female,men,women,boy,girl,paintings,sketches,(worst quality:2),(low quality:2),(normal quality:2),lowres,normal quality,((monochrome)),NG_DeepNegative_V1_75T",
		SamplerName:              "Euler a",
		Width:                    w8,
		Height:                   h8,
		Steps:                    20,
		CfgScale:                 7.0,
		Seed:                     -1,
		Eta:                      0.0,
		ControlNetUnits:          "{\"mlsd\":1.0}",
		ControlNetInputImagePath: design.InputImgPath,
		Units:                    units,
	}

	if newSd && redrawMode <= 0 {
		var tempImg model.TempImg
		if err := tempImg.GetByMd5(design.InputImgMd5); err != nil {
			logger.Error(err)
			return ""
		}
		var tagger Tagger
		if err := tagger.Load(tempImg.TaggerCaption); err != nil {
			logger.Error(err)
			return ""
		}
		if !tagger.Success {
			logger.Error("tagger.Success: ", tagger.Success)
			return ""
		}
		data.ModelName = "juggernautXL_v8Rundiffusion.safetensors"
		data.ModelHash = "AEB7E9E689"
		data.Prompt = "<lora:{==={lora}===}:1>,8K UHD," + tagger.Tags()
		data.NegativePrompt = "(worst quality, low quality, normal quality, lowres, low details, bad photo, bad photography, bad art:1.4), (watermark, signature, text font, username, error, logo, words, letters, digits, autograph, trademark, name:1.2)"
		data.SamplerName = "DPM++ 2M"
		data.Steps = 35

		if tagger.IsLineArt() {
			data.ControlNetUnits = `{"invert (from white bg & black line)":{"model":"mistoLine_fp16 [2583d73c]"}}`
		} else {
			data.ControlNetUnits = `{"canny":{"model":"mistoLine_fp16 [2583d73c]"}}`
		}

	} else {

		templatePrompt := TemplatePrompt.GetById(templatePromptId)
		if templatePrompt != nil {
			data.ModelName = templatePrompt.ModelName
			data.ModelHash = templatePrompt.ModelHash
			data.Prompt = templatePrompt.Prompt + ",<lora:{==={lora}===}:{==={LoraWeight}===}>"
			data.NegativePrompt = templatePrompt.NegativePrompt
			data.SamplerName = templatePrompt.SamplerName
			data.Steps = templatePrompt.Steps
			data.CfgScale = templatePrompt.CfgScale
			data.Eta = templatePrompt.Eta
			data.ControlNetUnits = templatePrompt.ControlNetUnits

			data.Prompt = strings.Replace(data.Prompt, "{==={LoraWeight}===}", fmt.Sprintf("%f", templatePrompt.LoraWeight), -1)
			//if newSd == false {
			//	data.Prompt = strings.Replace(data.Prompt, "{==={LoraWeight}===}", fmt.Sprintf("%f", templatePrompt.LoraWeight), -1)
			//
			//} else {
			//	data.Prompt = "<lora:{==={lora}===}:1>,no humans, scenery, indoors, bed, pillow, window, curtains, table, plant, realistic, chair, lamp, couch, wooden floor, potted plant, bedroom,(RAW photo, best quality, high quality), (realistic, photo-realistic), (8k), ultra-detailed"
			//	data.NegativePrompt = "people,face,limbs,head,body,nsfw,human,male,female,men, women, boy, girl,paintings, sketches, (worst quality:2), (low quality:2), (normal quality:2), lowres, normal quality, ((monochrome)),(greyscale), NG_DeepNegative_V1_75T, lowres,text, error, low quality, normal quality, jpeg artifacts, signature, watermark, username,bad perspective, english text, lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry"
			//	data.SamplerName = "DPM++ 2M Karras"
			//	data.Steps = 30
			//	data.CfgScale = 7
			//}

		} else {
			data.Prompt = strings.Replace(data.Prompt, "{==={LoraWeight}===}", fmt.Sprintf("%f", 0.8), -1)
		}

	}

	//fmt.Sprintf("debug/%s/{==={CustomMd5_Pre2}===}/{==={CustomMd5}===}.png", design.CreatedAt.Format("20060102"))
	data.CustomPath = fmt.Sprintf("%s/{==={CustomMd5}===}.png", design.CreatedAt.Format("200601"))

	if config.Env == enums.EnvEnum.DEV || config.Env == enums.EnvEnum.TEST {
		//20230323/92/92aaad2d5499c35839bc03147902b26e.png
		data.CustomPath = "debug/" + data.CustomPath
	}

	if enPrompt != "" {
		data.Prompt = enPrompt + "," + data.Prompt
	}
	if enNegativePrompt != "" {
		data.NegativePrompt = enNegativePrompt + "," + data.NegativePrompt
	}

	//if redrawMode == 1 {
	//	data.Prompt = enPrompt
	//}

	bf := bytes.NewBuffer([]byte{})
	jsonEncoder := json.NewEncoder(bf)
	jsonEncoder.SetEscapeHTML(false)
	jsonEncoder.Encode(data)
	return bf.String()
	//
	//jsonByte, err := json.Marshal(data)
	//if err != nil {
	//	logger.Error(err)
	//	return ""
	//}
	//return string(jsonByte)
}

func (d *design_) PopJson() (string, error) {
	value, err := myredis.BRPop(enums.AigcRedisKeyEnum.DesignPop)
	return value, err
}

func (d *design_) RemoveProgress(imgMd5 string) error {
	if _, err := myredis.HDel(enums.AigcRedisKeyEnum.SdTaskProgress, imgMd5); err != nil {
		logger.Error(err)
		return err
	}
	return nil
}

func (o *design_) PushJson(json string, customMd5 string, loraName string) (int64, error) {
	endJson := strings.Replace(json, "{==={CustomMd5}===}", customMd5, -1)
	endJson = strings.Replace(endJson, "{==={CustomMd5_Pre2}===}", customMd5[:2], -1)
	endJson = strings.Replace(endJson, "{==={lora}===}", loraName, -1)
	if strings.Contains(endJson, "{==={") || strings.Contains(endJson, "}===}") {
		logger.Error("存在未替换的字符串", endJson)
		return 0, errors.New("存在未替换的字符串")
	}
	logger.Info("发送绘图Json:", endJson)
	size, err := myredis.LPush(enums.AigcRedisKeyEnum.DesignPush, endJson)
	if err != nil {
		logger.Error(err)
		return size, err
	}
	return size, err
}

func (d *design_) PopJson1() (string, error) {
	value, err := myredis.BRPop(enums.AigcRedisKeyEnum.DesignPop1)
	return value, err
}

func (o *design_) PushJson1(json string, customMd5 string, loraName string, redrawMode int, newSd bool, denoisingStrength float64, sdServer string) (int64, error) {
	endJson := strings.Replace(json, "{==={CustomMd5}===}", customMd5, -1)
	endJson = strings.Replace(endJson, "{==={CustomMd5_Pre2}===}", customMd5[:2], -1)
	endJson = strings.Replace(endJson, "{==={lora}===}", loraName, -1)
	if strings.Contains(endJson, "{==={") || strings.Contains(endJson, "}===}") {
		logger.Error("存在未替换的字符串", endJson)
		return 0, errors.New("存在未替换的字符串")
	}

	endMap := tools.GetMapFromJson(endJson)
	parameters := make(map[string]interface{})
	imagesPath := make(map[string]string)

	useControlnetUnits := make([]map[string]interface{}, 0)

	control_net_input_image_path := ""
	if _, ok := endMap["control_net_input_image_path"]; ok {
		control_net_input_image_path = endMap["control_net_input_image_path"].(string)
	}

	if _, ok := endMap["control_net_units"]; ok { //	"control_net_units": "{\"mlsd\":{\"weight\":1.0}}",
		v := endMap["control_net_units"].(string)
		if v != "" {
			mUnits := tools.GetMapFromJson(v)
			for key, unit := range mUnits {
				if controlnetUnit, err := SdClient.GetControlnetUnit(key, newSd); err != nil {
					logger.Error(err, key, "不存在")
					return 0, err
				} else {
					mUnit := unit.(map[string]interface{})
					for k, u := range mUnit {
						controlnetUnit[k] = u
					}
					if control_net_input_image_path != "" {
						imagesPath[key+"_input_image"] = control_net_input_image_path
					}
					useControlnetUnits = append(useControlnetUnits, controlnetUnit)
				}
			}
		}
	}

	if _, ok := endMap["prompt"]; ok {
		parameters["prompt"] = endMap["prompt"]
	}
	if _, ok := endMap["negative_prompt"]; ok {
		parameters["negative_prompt"] = endMap["negative_prompt"]
		//parameters["negative_prompt"] = strings.Replace(endMap["negative_prompt"].(string), "NG_DeepNegative_V1_75T", "", -1)
	}
	if _, ok := endMap["sampler_name"]; ok {
		parameters["sampler_name"] = endMap["sampler_name"]
	}
	if _, ok := endMap["width"]; ok {
		parameters["width"] = endMap["width"]
	}
	if _, ok := endMap["height"]; ok {
		parameters["height"] = endMap["height"]
	}
	if _, ok := endMap["steps"]; ok {
		parameters["steps"] = endMap["steps"]
	}
	if _, ok := endMap["cfg_scale"]; ok {
		parameters["cfg_scale"] = endMap["cfg_scale"]
	}
	if _, ok := endMap["seed"]; ok {
		parameters["seed"] = endMap["seed"]
	}
	if _, ok := endMap["eta"]; ok {
		parameters["eta"] = endMap["eta"]
	}

	//"model_name": "collect/dvarchMultiPrompt_dvarch.ckpt",
	//"model_hash": "1ecb6b4e9c",
	if _, ok := endMap["model_name"]; ok {
		v := endMap["model_name"].(string)
		if strings.Contains(v, "juggernautXL_v8Rundiffusion.safetensors") {
			parameters["sd_model_checkpoint"] = "juggernautXL_v8Rundiffusion"
		} else if strings.Contains(v, "dvarchMultiPrompt_dvarch.ckpt") {
			//parameters["sd_model_checkpoint"] = "aigc_dvarchMultiPrompt_dvarch"
			parameters["sd_model_checkpoint"] = "dvarchMultiPrompt_dvarchInterior"
			//parameters["sd_model_checkpoint"] = "realisticVisionV51_v51VAE"
			//parameters["sd_model_checkpoint"] = "aigc_dvarchMultiPrompt_dvarch_full"
			//parameters["sd_model_checkpoint"] = "aigc_interiorDesign_v1"

		} else if strings.Contains(v, "dvarchMultiPrompt_dvarch_full.ckpt") {
			parameters["sd_model_checkpoint"] = "aigc_dvarchMultiPrompt_dvarch_full"
		} else if strings.Contains(v, "interiorDesign_v1.ckpt") {
			parameters["sd_model_checkpoint"] = "aigc_interiorDesign_v1"
		} else if strings.Contains(v, "zeyun.ckpt") {
			parameters["sd_model_checkpoint"] = "aigc_zeyun"
		} else {
			logger.Error("发现未处理的大模型：", v)
		}
	}

	if redrawMode > 0 {
		if tmpParameters, err := SdClient.GenParameters1(tools.GetJsonFromMap(parameters), useControlnetUnits); err != nil {
			msg := "生成parameters参数错误"
			logger.Error(msg, err)
			return 0, err
		} else {
			parameters = tmpParameters
		}
	} else {
		if tmpParameters, err := SdClient.GenParameters1(tools.GetJsonFromMap(parameters), useControlnetUnits); err != nil {
			msg := "生成parameters参数错误"
			logger.Error(msg, err)
			return 0, err
		} else {
			parameters = tmpParameters
		}
	}

	fields := make(map[string]interface{})
	if sdServer != "" {
		fields["sd_server"] = sdServer
	}

	if redrawMode > 0 {
		fields["sdapi"] = "Img2img"
		imagesPath["InitImg"] = control_net_input_image_path
		maskPath := ImgService.GetMaskImagePath(control_net_input_image_path)
		imagesPath["MaskImg"] = maskPath
		if b, _ := tools.PathFileExists(config.DiffusionFilePath + maskPath); b == false {
			err := errors.New("蒙版图不存在")
			logger.Error(err)
			return -1, err
		}
		if redrawMode == 1 {
			parameters["inpainting_mask_invert"] = 1 //只绘制蒙版黑色的区域
		} else if redrawMode == 2 {
			parameters["inpainting_mask_invert"] = 0             //只绘制非蒙版区域
			parameters["denoising_strength"] = denoisingStrength //0.55
		}
	} else {
		fields["sdapi"] = "Txt2img"
		delete(parameters, "mask")
		delete(parameters, "init_images")
	}

	fields["custom_app"] = "roodesign"
	if _, ok := endMap["custom_data"]; ok {
		fields["custom_data"] = endMap["custom_data"].(string)
	}
	if _, ok := endMap["custom_md5"]; ok {
		fields["custom_md5"] = endMap["custom_md5"].(string)
	}
	if _, ok := endMap["custom_path"]; ok {
		fields["custom_path"] = endMap["custom_path"].(string)
	}
	fields["parameters"] = tools.GetJsonFromMap(parameters)
	fields["images_path"] = imagesPath

	options := make(map[string]interface{})
	options["need_info"] = true
	fields["options"] = options

	pushJson := tools.GetJsonFromMap(fields)
	logger.Info("发送绘图Json1:", pushJson)

	if str, err := SdClient.Sd2img(pushJson); err != nil {
		logger.Error(err, str)
		return 0, err
	} else {
		logger.Info("Post Res:", str)
	}
	return 1, nil
}

var DesignService design_

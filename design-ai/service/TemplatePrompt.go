package service

import (
	"design-ai/enums"
	"design-ai/utils/logger"
	"design-ai/utils/myredis"
	"encoding/json"
)

type templatePrompt_ struct {
	m map[int]templatePrompt_item
	t string
}

type templatePrompt_item struct {
	Id              int     `json:"id"`
	State           string  `json:"state"`
	Audit           bool    `json:"audit"`
	Name            string  `json:"name"`
	ModelName       string  `json:"modelName"`
	ModelHash       string  `json:"modelHash"`
	LoraWeight      float64 `json:"loraWeight"`
	Prompt          string  `json:"prompt"`
	NegativePrompt  string  `json:"negativePrompt"`
	SamplerName     string  `json:"samplerName"`
	Steps           int     `json:"steps"`
	CfgScale        float64 `json:"cfgScale"`
	Eta             float64 `json:"eta"`
	ControlNetUnits string  `json:"controlNetUnits"`
	Notes           string  `json:"notes"`
}

func (o *templatePrompt_) LoadData(force bool) error {
	t, err := myredis.HGet(enums.AigcRedisKeyEnum.Audited, enums.AigcRedisKeyEnum.TemplatePrompts)
	if err != nil {
		logger.Error(err)
		return err
	}
	if o.t != "" && t == o.t && force == false {
		logger.Info("不重新加载TemplatePrompt", "  TemplatePrompt t:", t, "  ", o.t)
		return nil
	}
	ary, err := myredis.LRange(enums.AigcRedisKeyEnum.TemplatePrompts, 0, -1)
	if err != nil {
		logger.Error(err)
		return err
	}
	mm := make(map[int]templatePrompt_item)
	for i := 0; i < len(ary); i++ {
		mTmp := templatePrompt_item{}
		if err := json.Unmarshal([]byte(ary[i]), &mTmp); err != nil {
			logger.Error(err)
			return err
		}
		mm[mTmp.Id] = mTmp
	}
	o.m = mm
	o.t = t
	if t != "" {
		CacheService.SiteConfResult = nil
	}
	logger.Info("templatePrompt数据初始化完成,共", len(ary), "条数据")
	return nil
}

func (o *templatePrompt_) GetData() map[int]templatePrompt_item {
	return o.m
}

func (o *templatePrompt_) GetById(id int) *templatePrompt_item {

	if id == 0 {
		return nil
	}

	if v, ok := o.m[id]; ok {
		return &v
	} else {
		logger.Error("templatePrompt_item不存在", id)
		return nil
	}
}

var TemplatePrompt templatePrompt_

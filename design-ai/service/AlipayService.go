package service

import (
	"context"
	"design-ai/enums"
	"design-ai/utils/config"
	"design-ai/utils/logger"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/alipay"
	"github.com/shopspring/decimal"
)

type alipayService_ struct {
	ctx             context.Context
	client          *alipay.Client
	appId           string //应用APPID
	privateKey      string //应用私钥
	alipayPublicKey string //支付宝平台获取的支付宝公钥
}

func (o *alipayService_) setUp() {
	if o.client == nil {
		o.ctx = context.Background()
		//o.appId = "2021003187649757"                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              //AI效果图                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               // 商户号
		//o.privateKey = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCT7u0svk8AWow+SdRsFr6TURcgXQtQW1kSxbLrsPb/s16XeE9gJ6YWpIQGeRbrnLyJ80L6ZxadLqC7tsN51thyHYGXUiQmlKA1QbVvtOqaUoAPiGpLQryKGupZr7+iopiZG2AoEc7KmaDX7vIIXgvoKCiXN6XoJuBNUI5N71XYCVsRrhl5NOJh9jqddfbGYgD8K31+iYNv+0DYflSsrQKPJGR+Az/pfaE0AdjZMMgo9xRm/eNa8dR40fL0PdV04IMAnXudvSsECLUtpxPnjTOT9UhftVOdvIYPCFL2S3iMd0ZTExGpVIbAf3HjFYmMbxw/Lax33ZAmp6F3LgsOdPKhAgMBAAECggEAQ5ND0kmIqv+xyo5NM6bcHU1QhB8cmPNxZ+rZRWLMIZfdINH7aFAURYmXKaHMv4oUceumb94BbYGsOLNO7ewfaVljJi2Gh/0ihdFjQQjileYTy4SkEYcFuwx6eseWgYNGs8HWldc0oEW3PIxREVNsmvslN5Za+gYwNgETUDSBATLnn+cBoWohEvw2O1eJgZHiQXONAKCOb0nfYR69eq5qT5JDzExsCvVs4yaZ/v/DSXDpFi3rGDqpfb34V4bxkrR0FnUt72tvTsSrlhXSk5I+cLIgw5yszGeTa9/hrWokH3z0f9tWmdtJGEtzz/PrZNALkX3zy/PZPX0QwGdRPwYcBQKBgQDs9VSwpM9PcIdCg0vbt2hyYX7ZGqqCnGFNceqq0M6TihBTc6NhNRPpM8ezzzCEAsfFWKyJ7oBHZWO2smtIkTsMoFOCMJnpn1XGNnB56orLfPMsE9I6cB2b1kCx7zYFjCJjZCvJiyrIK4Cdq96KLkKsc1wP06is2PHA6FiEX6sU7wKBgQCf0i/jbzp/OQxPoORc17i7MBEtAQfPOMty0KMwAgaQoGP72HnPx4v3zkH8JnQH+NO6gIu+ZV+sN1vztlcEfZhSIvLBn7/orAaE8LgoDlyLsH2R9LPzmmg2vCIzFvlAQtfFysIuquJTEfn2XbcM1qPyQASRCcPHQJ/WpJrmxU0RbwKBgAP5PTlVGjDf1zTTVNNTaQn5/pGPc+qKysrsrR2+MKiiuRFdMBEyPao8dw3Z3JtqHIV/ri0F9dq8pYZryYbHALF+gsf9HqefR2XyhcJVn4w4BS/pCS53sNdrKVySgVpodi8qf916WvZOXHLIbgvtKKsRqCqPxxVW8f7lJo7Zt9m7AoGAfr5MVfxOkrgmKDv6/hnhmSksZdlMXSy4Q0xsLdVdaXKY+A+bwoiIWrQKpjHEkNHm7x+kMxDnx4Z7eJHg/mGv22t1wtGyeo15+hGAqduBzxHe81HxJ74K6WVXZdbRQgIAZg4+4o2IQMKfzZ5SXW8WwzW7qV3qEekRGUKeyWYskScCgYBHIPXhujeBOI3pnDZA2a49alPLi0/4CUIgEICfIXaUCQeQ0cNlta5f7HF+G7VKQtUUaCy+4b5yTnw2O8vj7ZPSVZDMGH98Y1O/oma71o6Q+OUr8GOClQ5+mYCPI+bwYvNbGkBmlJkujRiSoUCMYH+jeUR4yzD+oKC7kZxF9BHq3Q==" // 商户证书序列号
		//o.alipayPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuvjE3X8Ro03vhI2nk7MFhXtKBRQzAa3DX+6yK6wXIgbYLe8SG7Z6nriFh3eA3J+tOYmz7tahn9RJblfu1DQJ5p0KvIN7lXIWWKdzPocCaCYOaA5+1uLimJtjw7bJT+Jna82xnYBRJHDqwbik4PSKzomPmzJMguT41SxH/Xi2F86ypHh2lKCXW9TiWC6ZA7CgKxnGARZm2txq3Ad5HOpGQhMm3pQh4HjTFcD1xvFvmH2cafISuH23Pbs85sKNw/Fo81QUrD+a9+pBvp40LywlBEMlWhWUonehgW2m71N6JlWnNHHxooXBos2zlAON3+IEUEP8FLeNrwEdyrNNBbbnBwIDAQAB"

		o.appId = "2021003192690874"                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              //晨羽AI效果图                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         // 商户号
		o.privateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDRmaqDRaUNndgHGNmi35otPU5i7PxOkwKscSULDUFoEPzSFaMEYNwBaSsmzuzQiAuTCF78GYNmHqNdWVNY04VqCwvjBo75z+Ww4YSSZCIZG7cl1vTVGD8czPJwZOOMBmuMjLIr36jdEf/K17XBN/prfz8MZt0GwDAYNn/paULnVl1xhq7Jlf7jntlbkPdceN7urGpArG6oIJMlHm7JIvmyjN2MJvUDlI8CX4i3DgsCknTbDAymsFGqsm02+DUARpssty2hbnXjrfPsYqmYSajhUNOl+Ij68X6NpsgHjeeX4t2QM+0ZdDofOEwx6LowhhOTmsZWoz0KjQwkRcwHalmJAgMBAAECggEAZqmBnzKpmXXKrg856lDjT21Ly6CQJFxVXuk8X6Fqe/J2IuB+LFLU78v3WtbXn3xDTw02mPxWI9q14a8y57sTCdHHTLeVqD5wHqcXLCznlqzAlEQpP4bUwuKPjQWdfqRWYU04p8yxBTcfttbZK36jpat50jU/5WKVLAbluAnavzvmswE652ppxRXhicVii1v1l+WGtvMIiXUn79ICpRgjIqxWE/MvZV5XLoi58X3eKGq0afFXh+jsR3ZR6Tn05DiGmtg4bjY8l6uH009KoPay3IkWp6fzAB0UxylnRrAwnmo+nKsy/KS/rkzr8BZQk37mDbpvJbdzqMC9heeMMWQqQQKBgQD3tnovKXsJcuP7rSYdmRXibmkJt/MCB3i4DKzJaVt2i+lvENjUAXfsX/EFRtn2aEqCg+toZWTUN1rzfzcC8wQEjVMAnLpa03f4tr3T/AlFY+mZrk1/stUqJ6/gx6g9wF/uz0+fIgdhFXxbUOnh2a13WdJS9opEntAv9NB35L4UTQKBgQDYnManTeNAWKXi8pjRBEB+l3Y0klgh6cH4mHHBAcdiBBg2bjdLJOa4KQUUP35AYjGDm/RSAY3v140/mVrPfo8H59imUNw2xP0rvTotHGJtexMs8jlrBhOMy84FAwkcNzCGo7hxwoldNWafJGovMHloSXlc4mvmdZd8l0kIx4foLQKBgQD2QfI18IbgXddH3FqIEigvcpP5lWWAHu1kCzW7BYJn16+5OoZUPSF7W+W/2S/WRtfL6Fme7y3Nyzof19hYlj7oR5a8hRK+OHyMRR9yHp0Y3rrcjOt+h8pinhG65MHcoeK9BluhV7L+UN3P6iCY5k4M2L6+u1m53igZhMe6aSyOAQKBgQCPvwJpllt/oRR81ecplfvCxnqQCqnn33t0oJ2kqqyI1BTJUjzpZhSiE76mIDiFO2SGZSEupo9toYq/sOI9nkwGwAna3QA2bGVh+FI6C4MaCjnAcwI9jqu8nKccVlZeXhWa090byTBlJYHzjDPXGwEGfvPF+xAFwPWFndO+usLrBQKBgGfH9C1bWoJ7QPlBPeVOvhOcC8MloRjpR4z0t+3pYjHnibGhxuM63cw3YX8G0cn0pWvEYZVRWkEEQv84xAHX8Cp3fb3iD3JSJGZopI5vEF5mmygcPOGzfsWQ4AW82pjdyhL10+DoWIzQawCfuk3z3zCzsrfJVzltw/G82eM7r7EX" // 商户证书序列号
		o.alipayPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuvjE3X8Ro03vhI2nk7MFhXtKBRQzAa3DX+6yK6wXIgbYLe8SG7Z6nriFh3eA3J+tOYmz7tahn9RJblfu1DQJ5p0KvIN7lXIWWKdzPocCaCYOaA5+1uLimJtjw7bJT+Jna82xnYBRJHDqwbik4PSKzomPmzJMguT41SxH/Xi2F86ypHh2lKCXW9TiWC6ZA7CgKxnGARZm2txq3Ad5HOpGQhMm3pQh4HjTFcD1xvFvmH2cafISuH23Pbs85sKNw/Fo81QUrD+a9+pBvp40LywlBEMlWhWUonehgW2m71N6JlWnNHHxooXBos2zlAON3+IEUEP8FLeNrwEdyrNNBbbnBwIDAQAB"

		//初始化支付宝客户端
		//    appId：应用ID
		//    privateKey：应用秘钥
		//    isProd：是否是正式环境
		client, err := alipay.NewClient(o.appId, o.privateKey, true)
		if err != nil {
			logger.Error(err)
			return
		}
		// 打开Debug开关，输出日志，默认关闭
		client.DebugSwitch = gopay.DebugOn

		// 设置支付宝请求 公共参数
		//    注意：具体设置哪些参数，根据不同的方法而不同，此处列举出所有设置参数

		callbackUrl := "https://design.cyuai.com/api/v1/alipay/callback"
		callbackUrl = "http://***********:5173/#/pages/duringPay/duringPay"
		callbackUrl = "https://design.cyuai.com/#/pages/duringPay/duringPay"
		if config.Env == enums.EnvEnum.DEV {
			callbackUrl = "http://***********:5002/api/v1/alipay/callback"
		}
		client.SetLocation(alipay.LocationShanghai). // 设置时区，不设置或出错均为默认服务器时间
								SetCharset(alipay.UTF8).                                      // 设置字符编码，不设置默认 utf-8
								SetSignType(alipay.RSA2).                                     // 设置签名类型，不设置默认 RSA2
								SetReturnUrl(callbackUrl).                                    // 设置返回URL
								SetNotifyUrl("https://design.cyuai.com/api/v1/alipay/notify") // 设置异步通知URL
		//SetAppAuthToken()                           // 设置第三方应用授权

		client.AutoVerifySign([]byte(o.alipayPublicKey))
		o.client = client
	}
}

func (o *alipayService_) TradePagePay(outTradeNo string, amount decimal.Decimal, desc string) (string, error) {
	o.setUp()
	//请求参数
	bm := make(gopay.BodyMap)
	bm.Set("subject", desc) //电脑网站测试支付
	bm.Set("out_trade_no", outTradeNo)
	bm.Set("total_amount", amount.String())
	bm.Set("product_code", "FAST_INSTANT_TRADE_PAY")
	////电脑网站支付请求
	payUrl, err := o.client.TradePagePay(o.ctx, bm)
	if err != nil {
		logger.Error("err:", err)
	}
	return payUrl, err
}

func (o *alipayService_) TradeWapPay(outTradeNo string, amount decimal.Decimal, desc string) (string, error) {
	o.setUp()
	//请求参数
	bm := make(gopay.BodyMap)
	bm.Set("subject", desc) //手机网站测试支付
	bm.Set("out_trade_no", outTradeNo)
	//bm.Set("quit_url", "https://www.fmm.ink")
	bm.Set("total_amount", amount.String())
	bm.Set("product_code", "QUICK_WAP_WAY")
	//手机网站支付请求
	payUrl, err := o.client.TradeWapPay(o.ctx, bm)
	if err != nil {
		logger.Error("err:", err)
	}
	return payUrl, err
	//https://openapi.alipay.com/gateway.do?app_id=2021001191690325&biz_content=%7B%22out_trade_no%22%3A%22r2022121615331500000029%22%2C%22product_code%22%3A%22QUICK_WAP_WAY%22%2C%22subject%22%3A%22%E6%94%AF%E4%BB%98%E6%B5%8B%E8%AF%95%22%2C%22total_amount%22%3A%220.01%22%7D&charset=utf-8&format=JSON&method=alipay.trade.wap.pay&sign=QjuSgEBe7mVgJQejpDZgYuTzGv5DPJvJIiLkoF2Ql5aPI%2BYi99Ip1FXs5A7T18IVA%2FXk9s9FjyAM5jdIlrMZLplOx6KGkKH81lnHA88BX7dTHbyNjGQnIZTpw2O8SGRc4DP%2Fls%2BPJ4V52zeEvoao25nVqrwAUrSFpE%2FVyujED57AVfgOG1e8SWa480Dxf%2BROKNmApTAL1WRTRe637kf1kTUCTK2r%2FJI8sinR%2BCFY6n8X%2Bf90RzEN%2BMgnIi5C1pj3JSQkeve5oabrdwBEg%2BDAKMxAk6f%2Fn1uGtJwN9LOfIwnM%2BHgE6FY2kvJCy4aiYrQg%2FjAVlso2Q%2FdzGqjs2Azpbg%3D%3D&sign_type=RSA2&timestamp=2022-12-16+15%3A33%3A47&version=1.0
}

func (o *alipayService_) TradeAppPay(outTradeNo string, amount decimal.Decimal, desc string) (string, error) {
	o.setUp()
	//请求参数
	bm := make(gopay.BodyMap)
	bm.Set("subject", desc) //手机网站测试支付
	bm.Set("out_trade_no", outTradeNo)
	//bm.Set("quit_url", "https://www.fmm.ink")
	bm.Set("total_amount", amount.String())
	//手机网站支付请求
	payParam, err := o.client.TradeAppPay(o.ctx, bm)
	if err != nil {
		logger.Error("err:", err)
	}
	return payParam, err
}

func (o *alipayService_) TradePrecreate(outTradeNo string, amount decimal.Decimal, desc string) (*alipay.TradePrecreateResponse, error) {
	o.setUp()
	// 请求参数
	bm := make(gopay.BodyMap)
	bm.Set("subject", desc).
		Set("out_trade_no", outTradeNo).
		Set("total_amount", amount.String())

	// 创建订单
	aliRsp, err := o.client.TradePrecreate(o.ctx, bm)
	if err != nil {
		logger.Error("err:", err)
	}
	return aliRsp, err
}

func (o *alipayService_) VerifySign(notifyBean interface{}) (bool, error) {
	o.setUp()
	ok, err := alipay.VerifySign(o.alipayPublicKey, notifyBean)
	if err != nil {
		logger.Error("VerifySign(%+v),error:%+v", notifyBean, err)
	}
	return ok, err
}

func (o *alipayService_) TradeQueryByOutTradeNo(outTradeNo string) (*alipay.TradeQueryResponse, error) {
	o.setUp()
	bm := make(gopay.BodyMap)
	bm.Set("out_trade_no", outTradeNo)
	aliRsp, err := o.client.TradeQuery(o.ctx, bm)
	if err != nil {
		logger.Error(err)
	}
	//// 查询订单
	//aliRsp, err := o.client.TradeQuery(o.ctx, bm)
	//if err != nil {
	//	logger.Error(err)
	//}
	//return aliRsp,err
	//logger.Info("aliRsp:%+v", aliRsp.Response)
	//
	// 同步返回验签
	//ok, err := alipay.VerifySyncSignWithCert(o.alipayPublicKey, aliRsp.SignData, aliRsp.Sign)
	//if !ok || err != nil {
	//	logger.Error(err)
	//}
	return aliRsp, err
	//logger.Debug("同步返回验签：", ok)
}

var AlipayService alipayService_

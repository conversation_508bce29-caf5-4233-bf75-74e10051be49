package v1

import (
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/service"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"design-ai/utils/tools"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"net/http"
)

type userLogApi_ struct {
}

type userLogReq struct {
	UdId        string          `json:"ud_id"`
	LogType     int             `json:"log_type"`
	Plat        string          `json:"plat"`
	Os          string          `json:"os"`
	DeviceBrand string          `json:"device_brand"`
	DeviceType  string          `json:"device_type"`
	Channel     string          `json:"channel"`
	VersionName string          `json:"version_name"`
	VersionCode int             `json:"version_code"`
	Lng         decimal.Decimal `json:"lng"`
	Lat         decimal.Decimal `json:"lat"`
	LogJson     string          `json:"log_json"`
	Province    string          `json:"province"`
	City        string          `json:"city"`
	Address     string          `json:"address"`
}

func (obj userLogApi_) Save(c *gin.Context) {
	var code int
	var msg string
	var oReq userLogReq
	//logger.Info("head", c.Request.Header)

	//xForwardedFor := c.Request.Header.Get("X-Forwarded-For")
	//logger.Info("xForwardedFor:", xForwardedFor)

	tokenHeader := c.Request.Header.Get("Authorization")
	claims, err := middleware.GetClaimsByToken(tokenHeader)
	if err != nil {
		if tokenHeader != "" {
			logger.Error(err)
		}
	}
	userId := uint(0)
	if claims != nil {
		userId = claims.UserId
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	logJson := oReq.LogJson
	if logJson == "" {
		logJson = "{}"
	}

	userLog := model.UserLog{
		UdId:        oReq.UdId,
		UserId:      userId,
		LogType:     oReq.LogType,
		Ip:          tools.GetClientIp(c.Request.Header),
		Lat:         oReq.Lat,
		Lng:         oReq.Lng,
		Plat:        oReq.Plat,
		Os:          oReq.Os,
		DeviceBrand: oReq.DeviceBrand,
		DeviceType:  oReq.DeviceType,
		Channel:     oReq.Channel,
		VersionName: oReq.VersionName,
		VersionCode: oReq.VersionCode,
		LogJson:     logJson,
	}
	if err := userLog.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存日志出错")
		return
	}

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  "日志保存成功",
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
	if _, err := service.LocationService.PushJson("userlog", userLog.ID); err != nil {
		logger.Error(err)
	}
}

var UserLogApi userLogApi_

package model

import (
	"gorm.io/gorm"
)

type TempImg struct {
	gorm.Model
	UserId        uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	OrigWhere     int    `json:"orig_where" gorm:"type:int;not null;default:0;comment:哪里来的数据 1design参考图 5用户头像"`
	OrigId        int    `json:"orig_id" gorm:"type:bigint;not null;default:0;comment:数据id 1design参考图  5用户ID"`
	Md5           string `json:"md5" gorm:"type:varchar(50);not null;default:'';comment:图片md5"`
	Path          string `json:"path" gorm:"type:varchar(100);not null;default:'';comment:图片路径"`
	Width         int    `json:"width" gorm:"type:int;not null;default:0;comment:图片宽度"`
	Height        int    `json:"height" gorm:"type:int;not null;default:0;comment:图片高度"`
	TaggerCaption string `json:"tagger_caption" gorm:"type:json;comment:图片反推内容"`
	State         int    `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态 0初始 1上传成功 2校验失败 3非法图片 4校验通过 5使用中"`
}

func (TempImg) TableName() string {
	return "T_TempImg"
}

func (o *TempImg) GetByID(id uint) error {
	err := db.First(o, id).Error
	return err
}

func (o *TempImg) GetByMd5(md5Str string) error {
	err := db.Where("md5=?", md5Str).First(o).Error
	return err
}

func (o *TempImg) DeleteByMd5(md5Str string) error {
	err := db.Debug().Where("md5=?", md5Str).Delete(o).Error
	return err
}

func (o *TempImg) Save() error {
	if o.TaggerCaption == "" {
		o.TaggerCaption = "{}"
	}
	return db.Save(o).Error
}

func (o *TempImg) SetState(state int) error {
	return db.Model(o).Updates(TempImg{State: state}).Error
}

func (o *TempImg) SetWidthHeight(width int, height int, size int) error {
	return db.Model(o).Updates(TempImg{Width: width, Height: height}).Error
}

func (o *TempImg) SetTaggerCaption(json string) error {
	return db.Model(o).Updates(TempImg{TaggerCaption: json}).Error
}

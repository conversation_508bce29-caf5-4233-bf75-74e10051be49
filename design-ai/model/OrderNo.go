package model

import (
	"design-ai/enums"
	"design-ai/utils/myredis"
	"errors"
	"fmt"
	"time"
)

type orderNo_ struct {
}

func (obj orderNo_) NewByOrderType(orderType int, idx int64) (string, error) {
	dt := time.Now()
	timeStr := dt.Format("20060102150405")

	if idx == 0 {
		idx, _ = myredis.Incr(enums.RedisKeyEnum.AutoSerialNumber)
		if idx <= 0 {
			return "", errors.New("获取自增需要失败")
		}
	}
	idxStr := obj.sup(idx, 8)

	if orderType == enums.OrderTypeEnum.ManagerAdd || orderType == enums.OrderTypeEnum.ManagerCost {
		prefix := enums.OrderNoPrefixEnum.Manage
		return prefix + timeStr + idxStr, nil
	} else if orderType == enums.OrderTypeEnum.UserGift {
		prefix := enums.OrderNoPrefixEnum.Gift
		return prefix + timeStr + idxStr, nil
	} else if orderType == enums.OrderTypeEnum.Cost {
		prefix := enums.OrderNoPrefixEnum.Cost
		return prefix + timeStr + idxStr, nil
	} else if orderType == enums.OrderTypeEnum.RechargeBuy {
		prefix := enums.OrderNoPrefixEnum.Recharge
		return prefix + timeStr + idxStr, nil
	} else if orderType == enums.OrderTypeEnum.Subscribe {
		prefix := enums.OrderNoPrefixEnum.Subscribe
		return prefix + timeStr + idxStr, nil
	} else {
		return "", errors.New("未找到前缀参数")
	}
}

/*
func (obj orderNo_) NewRechargeNo() string {
	dt := time.Now()
	timeStr := dt.Format("20060102150405")
	idx, err := myredis.Incr(enums.RedisKeyEnum.AutoSerialNumber)
	if idx <= 0 {
		logger.Error(err)
		return ""
	}
	idxStr := obj.sup(idx, 8)
	return timeStr + idxStr
}*/

//对长度不足n的数字前面补0
func (obj orderNo_) sup(i int64, n int) string {
	m := fmt.Sprintf("%d", i)
	for len(m) < n {
		m = fmt.Sprintf("0%s", m)
	}
	return m
}

var OrderNo orderNo_
